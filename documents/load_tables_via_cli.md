Okay, absolutely! This is a great idea for future reference. Here's a markdown file summarizing everything we've done. You can copy and paste this into a `.md` file (e.g., `mysql_docker_setup.md`).

````markdown
# MySQL Docker Setup and SQL Import Guide

This document outlines the steps taken to set up a MySQL instance using Docker, import three .sql database dump files, and connect to the databases for interaction. This setup is on a Windows machine using PowerShell.

## Prerequisites

- Docker Desktop installed and running on Windows.
- Three .sql files (`tenant_humanscale.sql`, `tenant_macher.sql`, `tenant_nopanordic.sql`) located at `C:\Users\<USER>\Documents\CarbonBright\tenant tables`.

## Summary of Goals

1.  Run a MySQL Docker container (`mysql-test`).
2.  Map a non-conflicting host port (e.g., `3307`) to the container's MySQL port (`3306`).
3.  Mount the directory containing the .sql files into the container.
4.  Persist MySQL data using a Docker volume.
5.  Set a root password for the MySQL instance.
6.  Create individual databases for each .sql file.
7.  Import each .sql file into its respective database, handling a foreign key constraint error for one file.
8.  Connect to the databases via CLI (both from within the container and from the host) and GUI (MySQL Workbench).

## Steps

### 1. Stop and Remove Any Previous `mysql-test` Container (Clean Slate)

Before starting, ensure any existing container with the same name is removed.
In PowerShell:

```powershell
docker stop mysql-test
docker rm mysql-test
```
````

_(It's okay if these commands show an error if the container doesn't exist)._

### 2. Run the MySQL Docker Container

This command starts a new `mysql-test` container with the specified configurations.
In PowerShell (as a single line):

```powershell
docker run -d --name mysql-test -p 3307:3306 -v mysql_test_data:/var/lib/mysql -v "C:\Users\<USER>\Documents\CarbonBright\tenant tables:/sql_dumps_in_container" -e MYSQL_ROOT_PASSWORD=pass123 mysql
```

**Explanation of `docker run` options:**

- `-d`: Run in detached mode (background).
- `--name mysql-test`: Name of the container.
- `-p 3307:3306`: Maps port `3307` on the host to port `3306` inside the container.
- `-v mysql_test_data:/var/lib/mysql`: Creates/uses a Docker-managed volume named `mysql_test_data` to persist the actual database data (stored at `/var/lib/mysql` inside the container).
- `-v "C:\Users\<USER>\Documents\CarbonBright\tenant tables:/sql_dumps_in_container"`: Mounts your local directory with SQL files into the container at the path `/sql_dumps_in_container`. The quotes are important because the path has spaces.
- `-e MYSQL_ROOT_PASSWORD=pass123`: Sets the MySQL root user's password to `pass123`. **Note: This is a weak password and should only be used for local, temporary testing.**
- `mysql`: The Docker image to use (defaults to `mysql:latest`).

### 3. Create Databases Inside the Container

Connect to the MySQL instance within the container and create the target databases.
In PowerShell:

```powershell
"CREATE DATABASE IF NOT EXISTS db_humanscale; CREATE DATABASE IF NOT EXISTS db_macher; CREATE DATABASE IF NOT EXISTS db_nopanordic; SHOW DATABASES;" | docker exec -i mysql-test mysql -uroot -ppass123
```

This command pipes the SQL statements to the `mysql` client running inside the `mysql-test` container. You should see the created databases listed.

### 4. Import SQL Files into Respective Databases

Import each `.sql` file using the `sh -c` wrapper to correctly handle redirection within the container's shell.

**a. Import `tenant_humanscale.sql` into `db_humanscale`:**
In PowerShell:

```powershell
docker exec -i mysql-test sh -c "mysql -uroot -ppass123 db_humanscale < /sql_dumps_in_container/tenant_humanscale.sql"
```

**b. Import `tenant_macher.sql` into `db_macher`:**
In PowerShell:

```powershell
docker exec -i mysql-test sh -c "mysql -uroot -ppass123 db_macher < /sql_dumps_in_container/tenant_macher.sql"
```

**c. Import `tenant_nopanordic.sql` into `db_nopanordic` (with Foreign Key Check Workaround):**
This file had an `ERROR 1824: Failed to open the referenced table 'node'` due to a foreign key constraint. The workaround is to temporarily disable foreign key checks during this import.
In PowerShell:

```powershell
docker exec -i mysql-test sh -c "echo 'SET FOREIGN_KEY_CHECKS=0;' && cat /sql_dumps_in_container/tenant_nopanordic.sql && echo 'SET FOREIGN_KEY_CHECKS=1;' | mysql -uroot -ppass123 db_nopanordic"
```

_You will see a warning: `mysql: [Warning] Using a password on the command line interface can be insecure.` This is expected and normal for this method._

### 5. Connect and Verify Databases

**a. Connect via Docker Exec (CLI inside container):**
In PowerShell:

```powershell
docker exec -it mysql-test mysql -uroot -ppass123
```

Once at the `mysql>` prompt:

```sql
SHOW DATABASES;
USE db_humanscale;
SHOW TABLES;
SELECT COUNT(*) FROM some_table_in_humanscale; -- Replace 'some_table_in_humanscale'

USE db_macher;
SHOW TABLES;
SELECT COUNT(*) FROM some_table_in_macher; -- Replace 'some_table_in_macher'

USE db_nopanordic;
SHOW TABLES;
SELECT COUNT(*) FROM some_table_in_nopanordic; -- Replace 'some_table_in_nopanordic'

EXIT;
```

**b. Connect via Local MySQL Client (CLI on Host):**
If you have the MySQL client installed on your Windows machine and in your PATH.
In PowerShell:

```powershell
mysql -h 127.0.0.1 -P 3307 -u root -p
```

When prompted, enter the password: `pass123`.
Then use the same SQL commands as above.

**c. Connect via MySQL Workbench (or other GUI):**

- Connection Name: `mysql-test-docker` (or similar)
- Connection Method: `Standard (TCP/IP)`
- Hostname: `127.0.0.1`
- Port: `3307`
- Username: `root`
- Password: `pass123` (Store in Vault if desired)

## Important Notes

- **Password Security:** The password `pass123` is insecure. For any non-testing environment, use a strong, unique password.
- **Data Persistence:** The `-v mysql_test_data:/var/lib/mysql` ensures your database data is stored in a Docker volume (`mysql_test_data`). If you `docker rm mysql-test`, the data in this volume will persist. If you run the `docker run` command again with the same volume name, it will reuse the existing data. To start fresh, you'd need to remove the Docker volume (`docker volume rm mysql_test_data`).
- **PowerShell Redirection:** The `<` operator for input redirection needed to be handled by a shell _inside_ the container, hence the use of `sh -c "command < file"`.
- **Foreign Keys:** The workaround for `tenant_nopanordic.sql` means the specific foreign key constraint causing the error might not be active. For development/testing, this is often acceptable.

This setup provides a flexible and isolated MySQL environment for working with your database dumps.

## Exploring Prediction Override Data

Once your databases are loaded, you can explore the prediction override functionality using the following commands.

### Database Connection and Exploration

**1. Connect to MySQL CLI:**
```powershell
docker exec -it mysql-test mysql -uroot -ppass123
```

**2. Explore available databases and tables:**
```sql
SHOW DATABASES;
USE db_humanscale;
SHOW TABLES;
```

**3. Look for prediction-related tables:**
```sql
SHOW TABLES LIKE '%prediction%';
SHOW TABLES LIKE '%override%';
```

### Prediction Override Table Analysis

**1. Examine table structure:**
```sql
DESCRIBE prediction_override;
```

**Table Structure:**
- `api_name` (varchar 80) - API endpoint name (Primary Key)
- `query` (varchar 255) - Query string (Primary Key) 
- `override_value` (text) - Override value data
- `created_at` (datetime) - Creation timestamp
- `updated_at` (datetime) - Last update timestamp

**2. View sample data:**
```sql
SELECT * FROM prediction_override LIMIT 10;
```

**3. Count total overrides:**
```sql
SELECT COUNT(*) FROM prediction_override;
```

**4. View recent overrides:**
```sql
SELECT * FROM prediction_override ORDER BY updated_at DESC LIMIT 10;
```

### Data Export for Analysis

**Note:** Direct file export from MySQL may be restricted due to `--secure-file-priv` option.

**1. Export to text file (from PowerShell):**
```powershell
docker exec mysql-test mysql -uroot -ppass123 -e "SELECT * FROM db_humanscale.prediction_override LIMIT 50;" > prediction_override.txt
```

**2. Export with table formatting:**
```powershell
docker exec mysql-test mysql -uroot -ppass123 --table -e "SELECT * FROM db_humanscale.prediction_override LIMIT 50;" > prediction_override.txt
```

**3. Export as CSV format:**
```powershell
docker exec mysql-test mysql -uroot -ppass123 --batch --raw -e "SELECT 'api_name','query','override_value','created_at','updated_at' UNION ALL SELECT api_name,query,override_value,created_at,updated_at FROM db_humanscale.prediction_override" > prediction_override.csv
```

**4. View data in vertical format (for long text fields):**
```powershell
docker exec mysql-test mysql -uroot -ppass123 -e "SELECT * FROM db_humanscale.prediction_override LIMIT 5\G"
```

### Common Queries for Prediction Override Analysis

**1. Find overrides by API:**
```sql
SELECT * FROM prediction_override WHERE api_name = 'your_api_name';
```

**2. Search for specific queries:**
```sql
SELECT * FROM prediction_override WHERE query LIKE '%search_term%';
```

**3. View overrides with truncated values for overview:**
```sql
SELECT api_name, 
       LEFT(query, 50) as query_preview,
       LEFT(override_value, 50) as override_preview,
       created_at, 
       updated_at 
FROM prediction_override 
LIMIT 10;
```

**4. Group by API to see override counts:**
```sql
SELECT api_name, COUNT(*) as override_count 
FROM prediction_override 
GROUP BY api_name;
```

### Exit MySQL CLI
```sql
exit
```

```

This should cover everything comprehensively!
```
