# Development Setup Guide

This guide explains how to set up a development environment for the ML Models project using Docker with volume mounting for faster development cycles.

## What is Volume Mounting?

Volume mounting creates a direct link between your local codebase and the running Docker container. This means:

- Changes to your code files are immediately available in the container
- No need to rebuild the container when you change code
- Faster development iterations

## Setup Instructions

### 1. Initial Setup

First, make sure you have all the required environment variables in your `.env` file:

```
AZURE_OPENAI_API_KEY="your-key"
AZURE_OPENAI_API_KEY_US_EAST="your-key"
AZURE_OPENAI_ENDPOINT="your-endpoint"
AZURE_OPENAI_ENDPOINT_US_EAST="your-endpoint"
HF_TOKEN="your-huggingface-token"
AZURE_OPENAI_DEPLOYMENT="3_5_turbo"
AZURE_OPENAI_DEPLOYMENT_4="GPT-4-Turbo"
AZURE_OPENAI_DEPLOYMENT="gpt-4o"  # Note the hyphen, not underscore
AZURE_PHI_3_ENDPOINT="your-endpoint"
AZURE_PHI_3_KEY="your-key"
MAPBOX_ACCESS_TOKEN="your-token"
PORT=5001
REDIS_URL="redis://ml-model-redis:6379"
```

### 2. Building the Development Container

The first time you set up the development environment, you need to build the container:

```bash
# Build and start the development containers
docker-compose -f docker-compose.dev.yml up -d --build
```

This initial build will take some time as it installs all dependencies.

### 3. Development Workflow

Once the containers are running, you can edit code files directly on your local machine:

1. Make changes to any Python file in the project
2. The changes are immediately available in the container
3. The application will use the updated code

For example, if you modify `config.py`, the changes will be immediately reflected in the running application without needing to rebuild or restart the container.

### 4. Restarting the Application

If you need to restart just the application (not rebuild):

```bash
docker restart ml-models-app
```

### 5. Viewing Logs

To see the application logs:

```bash
docker logs -f ml-models-app
```

### 6. When to Rebuild

You still need to rebuild the container in these cases:

1. When you add new dependencies to `requirements.txt`
2. When you change the Dockerfile
3. When you need a completely fresh environment

To rebuild:

```bash
docker-compose -f docker-compose.dev.yml down
docker-compose -f docker-compose.dev.yml up -d --build
```

## Troubleshooting

### File Permission Issues

If you encounter permission issues:

```bash
# Check the user ID in the container
docker exec ml-models-app id

# You might need to adjust permissions on your local files
chmod -R 755 .
```

### Changes Not Reflecting

If your changes aren't being reflected:

1. Check that the volume is mounted correctly:

   ```bash
   docker inspect ml-models-app | grep -A 10 Mounts
   ```

2. Make sure you're editing the correct files (the ones in the mounted directory)

3. Some changes might require a restart of the application:
   ```bash
   docker restart ml-models-app
   ```

### Python Module Caching

Python caches imported modules. If changes to a module aren't being reflected:

1. Add this to the top of your main.py to disable caching in development:

   ```python
   if os.environ.get("DEBUG") == "true":
       import sys
       sys.dont_write_bytecode = True
   ```

2. Or restart the application to clear the cache:
   ```bash
   docker restart ml-models-app
   ```

## Switching Between Development and Production

### Development Mode

```bash
docker-compose -f docker-compose.dev.yml up -d
```

### Production Mode

```bash
docker-compose up -d
```

## Best Practices

1. Keep your local environment clean (no unnecessary files)
2. Don't store large data files in the mounted directory
3. Be careful with file permissions
4. Use .dockerignore to exclude files that shouldn't be in the container
