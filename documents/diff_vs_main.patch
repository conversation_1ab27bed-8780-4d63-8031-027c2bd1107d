diff --git a/check_chroma_metadata.py b/check_chroma_metadata.py
new file mode 100644
index 0000000..2dc564c
--- /dev/null
+++ b/check_chroma_metadata.py
@@ -0,0 +1,48 @@
+#!/usr/bin/env python3
+"""
+Check ChromaDB metadata structure to understand ISIC field names
+"""
+import sys
+sys.path.append('/home/<USER>/app')
+
+from emissions_factor_matching.dataset import collection
+import json
+
+def check_chroma_metadata():
+    print("=== CHECKING CHROMADB METADATA STRUCTURE ===\n")
+    
+    # Query a few transport-related activities to see metadata structure
+    results = collection.query(
+        query_texts=["freight transport truck"],
+        where={"activity_type": {"$eq": "ordinary transforming activity"}},
+        n_results=3,
+        include=['metadatas', 'documents', 'distances']
+    )
+    
+    print("Sample ChromaDB metadata for transport activities:")
+    print(f"Found {len(results['ids'][0])} results\n")
+    
+    for i in range(len(results['ids'][0])):
+        print(f"--- Result {i+1} ---")
+        print(f"ID: {results['ids'][0][i]}")
+        print(f"Document: {results['documents'][0][i]}")
+        print(f"Distance: {results['distances'][0][i]:.4f}")
+        
+        metadata = results['metadatas'][0][i]
+        print("Metadata keys:")
+        for key in sorted(metadata.keys()):
+            print(f"  {key}: {metadata[key]}")
+        print()
+        
+        # Check for ISIC-related fields
+        isic_fields = [key for key in metadata.keys() if 'isic' in key.lower()]
+        if isic_fields:
+            print(f"ISIC-related fields: {isic_fields}")
+            for field in isic_fields:
+                print(f"  {field}: {metadata[field]}")
+        else:
+            print("No ISIC-related fields found")
+        print()
+
+if __name__ == "__main__":
+    check_chroma_metadata()
diff --git a/check_isic_codes.py b/check_isic_codes.py
new file mode 100644
index 0000000..2e44e60
--- /dev/null
+++ b/check_isic_codes.py
@@ -0,0 +1,62 @@
+#!/usr/bin/env python3
+"""
+Quick script to check what ISIC codes are available in our database
+"""
+import sys
+sys.path.append('/home/<USER>/app')
+
+from emissions_factor_matching.dataset import efs_with_geographies
+import pandas as pd
+
+def check_isic_codes():
+    print("=== ISIC Codes Available in Database ===\n")
+    
+    # Check what columns exist
+    print("Available columns:")
+    for col in efs_with_geographies.columns:
+        if 'isic' in col.lower() or 'ISIC' in col:
+            print(f"  - {col}")
+    print()
+    
+    # Check ISIC Sections
+    if 'ISIC Section' in efs_with_geographies.columns:
+        print("ISIC Sections available:")
+        sections = efs_with_geographies['ISIC Section'].value_counts()
+        for section, count in sections.items():
+            print(f"  {section}: {count} activities")
+        print()
+    
+    # Check ISIC Classifications  
+    if 'ISIC Classification' in efs_with_geographies.columns:
+        print("ISIC Classifications (first 20):")
+        classifications = efs_with_geographies['ISIC Classification'].value_counts().head(20)
+        for classification, count in classifications.items():
+            print(f"  {classification}: {count} activities")
+        print(f"... and {len(efs_with_geographies['ISIC Classification'].unique()) - 20} more")
+        print()
+        
+        # Show transport-related ISIC codes specifically
+        print("Transport-related ISIC codes (H section):")
+        transport_codes = efs_with_geographies[
+            efs_with_geographies['ISIC Section'] == 'H - Transportation and storage'
+        ]['ISIC Classification'].value_counts()
+        for code, count in transport_codes.items():
+            print(f"  {code}: {count} activities")
+        print()
+        
+        # Show manufacturing codes (C section)
+        print("Manufacturing ISIC codes (C section - first 10):")
+        manufacturing_codes = efs_with_geographies[
+            efs_with_geographies['ISIC Section'] == 'C - Manufacturing'
+        ]['ISIC Classification'].value_counts().head(10)
+        for code, count in manufacturing_codes.items():
+            print(f"  {code}: {count} activities")
+        print()
+    
+    # Total counts
+    print(f"Total activities in database: {len(efs_with_geographies)}")
+    print(f"Unique ISIC sections: {len(efs_with_geographies['ISIC Section'].unique())}")
+    print(f"Unique ISIC classifications: {len(efs_with_geographies['ISIC Classification'].unique())}")
+
+if __name__ == "__main__":
+    check_isic_codes()
diff --git a/debug_isic_issue.py b/debug_isic_issue.py
new file mode 100644
index 0000000..80f056e
--- /dev/null
+++ b/debug_isic_issue.py
@@ -0,0 +1,71 @@
+#!/usr/bin/env python3
+"""
+Debug script to understand the ISIC code issue
+"""
+import sys
+sys.path.append('/home/<USER>/app')
+
+from emissions_factor_matching.dataset import efs_with_geographies
+import pandas as pd
+
+def debug_isic_issue():
+    print("=== DEBUGGING ISIC CODE ISSUE ===\n")
+    
+    # Check the exact format of ISIC codes
+    print("1. Sample ISIC Classification values:")
+    sample_isic = efs_with_geographies['ISIC Classification'].dropna().head(10)
+    for i, code in enumerate(sample_isic):
+        print(f"   {i+1}. '{code}' (type: {type(code)})")
+    print()
+    
+    # Check if 4923 exists in different formats
+    print("2. Checking for 4923 in different formats:")
+    all_isic = efs_with_geographies['ISIC Classification'].dropna().unique()
+    
+    # Check exact match
+    exact_match = '4923' in all_isic
+    print(f"   Exact '4923' exists: {exact_match}")
+    
+    # Check with description
+    desc_matches = [code for code in all_isic if '4923' in str(code)]
+    print(f"   Codes containing '4923': {desc_matches}")
+    
+    # Check transport codes specifically
+    print("\n3. All transport-related codes (H section):")
+    transport_data = efs_with_geographies[
+        efs_with_geographies['ISIC Section'] == 'H - Transportation and storage'
+    ]
+    transport_codes = transport_data['ISIC Classification'].unique()
+    for code in sorted(transport_codes):
+        count = len(transport_data[transport_data['ISIC Classification'] == code])
+        print(f"   '{code}': {count} activities")
+    
+    # Check the exact code we're looking for
+    print(f"\n4. Checking exact code '4923':")
+    exact_4923 = efs_with_geographies[
+        efs_with_geographies['ISIC Classification'] == '4923'
+    ]
+    print(f"   Found {len(exact_4923)} activities with exact '4923'")
+    
+    # Check with description format
+    print(f"\n5. Checking code with description format:")
+    desc_4923 = efs_with_geographies[
+        efs_with_geographies['ISIC Classification'].str.contains('4923', na=False)
+    ]
+    print(f"   Found {len(desc_4923)} activities containing '4923'")
+    
+    if len(desc_4923) > 0:
+        print(f"   Example: '{desc_4923['ISIC Classification'].iloc[0]}'")
+    
+    # Show the available_isic_codes logic
+    print(f"\n6. Testing the available_isic_codes logic:")
+    available_isic_codes = set(efs_with_geographies['ISIC Classification'].dropna().unique())
+    print(f"   Total unique ISIC codes: {len(available_isic_codes)}")
+    print(f"   '4923' in available_isic_codes: {'4923' in available_isic_codes}")
+    
+    # Check if it's the full description format
+    freight_codes = [code for code in available_isic_codes if 'freight' in str(code).lower()]
+    print(f"   Codes with 'freight': {freight_codes}")
+
+if __name__ == "__main__":
+    debug_isic_issue()
diff --git a/emissions_factor_matching/api.py b/emissions_factor_matching/api.py
index 9ac9c1f..035de29 100644
--- a/emissions_factor_matching/api.py
+++ b/emissions_factor_matching/api.py
@@ -14,6 +14,12 @@ from emissions_factor_matching.dataset import collection, collection_eol
 from emissions_factor_matching.predictions import (
     get_cas_number,
     predict_input_category,
+    predict_enhanced_input_category,
+    spot_modifiers,
+    map_isic_classification,
+    augment_query_text,
+    construct_dynamic_filters,
+    re_rank_candidates,
     predict_product_constituents,
     get_common_chemical_names,
     get_product_description,
@@ -21,6 +27,7 @@ from emissions_factor_matching.predictions import (
     get_closest_match,
     get_technological_representation,
 )
+from emissions_factor_matching.dataset import search_candidates_with_fallback
 from shared_predictions import get_top_match
 from emissions_factor_matching.geography import get_geography_activity_match
 from utils import logger
@@ -63,15 +70,20 @@ class ActivityResponse(Activity):
     similarity: float | None = None
 
 class ActivityRecommendationsRequest(BaseModel):
-    chemical_name: str
-    valid_units: list[str] | None = None
-    product_category: str | None = None
-    cas_number: str | None = None
+    user_query_primary: str
+    user_query_secondary: str | None = None
+    lca_lifecycle_stage: str | None = None  # Optional for now - frontend doesn't send yet
     iso_code: str | None = None
+    valid_units: list[str] | None = None
+    product_category_context: str | None = None
     number_of_matches: int | None = None
     lcia_method: str | None = None
     carbon_only: bool | None = None
 
+    # Backward compatibility fields - will be deprecated
+    chemical_name: str | None = None
+    cas_number: str | None = None
+
 class ActivityRecommendationsResponse(BaseModel):
     matched_activity: Activity
     confidence: str
@@ -191,124 +203,107 @@ async def get_recommended_activities(
     api_version: str = Depends(get_api_version),
 ):
     logger.info(f"API Version: {api_version}")
-
-    if not activity_request.cas_number:
+    logger.info("Phase 1: Enhanced Input Category Prediction - Starting")
+
+    # Handle backward compatibility: convert old chemical_name to new format
+    if activity_request.chemical_name and not activity_request.user_query_primary:
+        activity_request.user_query_primary = activity_request.chemical_name
+        logger.info(f"Backward compatibility: converted chemical_name to user_query_primary")
+
+    # Ensure we have a primary query
+    if not activity_request.user_query_primary:
+        raise HTTPException(status_code=400, detail="user_query_primary is required")
+
+    # Phase 1.1: Enhanced Input Category Prediction
+    enhanced_category = predict_enhanced_input_category(activity_request)
+    logger.info(f"Phase 1 Complete: Enhanced category = {enhanced_category}")
+
+    # Phase 1.2: Modifier Spotting
+    logger.info("Phase 2: Modifier Spotting - Starting")
+    modifiers = spot_modifiers(activity_request, enhanced_category)
+    logger.info(f"Phase 2 Complete: Extracted {len(modifiers)} modifiers = {modifiers}")
+
+    # Phase 1.3: ISIC Classification Mapping
+    logger.info("Phase 3: ISIC Classification Mapping - Starting")
+    isic_codes = map_isic_classification(enhanced_category, modifiers)
+    logger.info(f"Phase 3 Complete: Mapped to {len(isic_codes)} ISIC codes = {isic_codes}")
+
+    # Phase 1.4: Query Text Augmentation
+    logger.info("Phase 4: Query Text Augmentation - Starting")
+    augmented_query = augment_query_text(activity_request, enhanced_category, modifiers, isic_codes)
+    logger.info(f"Phase 4 Complete: Augmented query = '{augmented_query}'")
+
+    # Phase 1.5: Dynamic Filter Construction
+    logger.info("Phase 5: Dynamic Filter Construction - Starting")
+    dynamic_filters = construct_dynamic_filters(activity_request, enhanced_category, modifiers, isic_codes)
+    logger.info(f"Phase 5 Complete: Dynamic filters = {dynamic_filters}")
+
+    # Handle CAS number for chemicals (backward compatibility)
+    if not activity_request.cas_number and activity_request.chemical_name:
         activity_request.cas_number = get_cas_number(activity_request.chemical_name)
+    logger.info(f"CAS Number: {activity_request.cas_number}")
 
-    logger.info(activity_request.cas_number)
-
-    number_of_matches = activity_request.number_of_matches or 5
     iso_code = activity_request.iso_code or "GLO"
-    material = activity_request.chemical_name
-    category = predict_input_category(material)
-
-    if category == "PRODUCT":
-        if activity_request.product_category:
-            material += f" for {activity_request.product_category}"
-
-        constituent = predict_product_constituents(material)
-        query_text = f"{constituent} {material}"
-    else:
-        common_names = get_common_chemical_names(material)
-        logger.warning(common_names)
-
-        description = get_product_description(material)
-        logger.warning(description)
-
-        query_text = (
-            f"{material} ({common_names}): {description}"
-            if common_names != "NONE"
-            else f"{material}: {description}"
-        )
-
-    where = {"activity_type": {"$eq": "ordinary transforming activity"}}
-    product_section = get_product_activity_isic_section(material)
-    if product_section:
-        where = {
-            "$and": [
-                {"activity_type": {"$eq": "ordinary transforming activity"}},
-                {"isic_section": {"$eq": product_section}},
-            ],
-        }
-
-    if activity_request.valid_units:
-        if "$and" in where:
-            where["$and"].append({"unit": {"$in": activity_request.valid_units}})
-        else:
-            where = {
-                "$and": [
-                    {"activity_type": {"$eq": "ordinary transforming activity"}},
-                    {"unit": {"$in": activity_request.valid_units}},
-                ],
-            }
-    
-    if activity_request.lcia_method and not activity_request.carbon_only:
-        if "$and" in where:
-            where["$and"].append({activity_request.lcia_method: {"$eq": True}})
-        else:
-            where = {
-                "$and": [
-                    {"activity_type": {"$eq": "ordinary transforming activity"}},
-                    {activity_request.lcia_method: {"$eq": True}},
-                ],
-            }
 
-    documents = collection.query(
-        query_texts=[query_text],
-        n_results=25,
-        where=where
+    # Phase 1.6: ChromaDB Vector Search Enhancement
+    logger.info("Phase 6: ChromaDB Vector Search Enhancement - Starting")
+    candidates = search_candidates_with_fallback(
+        augmented_query=augmented_query,
+        filters=dynamic_filters,
+        n_results=25
     )
-
-    activities = []
-    activities_for_selection = []
-    for i in range(len(documents["ids"][0])):
-        activity = {
-            "uuid": documents["metadatas"][0][i]["uuid"],
-            "activity_name": documents["documents"][0][i],
-            "metadata": documents["metadatas"][0][i],
-            "similarity": 0,
-        }
-        activities.append(activity)
-        activities_for_selection.append({
-            k: v
-            for k, v in activity.items()
-            if k != "similarity"
-        })
-
-    closest_match = await get_closest_match(
-        query_text,
-        activities_for_selection,
+    logger.info(f"Phase 6 Complete: Retrieved {len(candidates)} candidates")
+
+    # Phase 1.7: LLM Re-ranking & Justification
+    logger.info("Phase 7: LLM Re-ranking & Justification - Starting")
+    if not candidates:
+        raise HTTPException(status_code=404, detail="No emission factor candidates found")
+
+    matched_ef = re_rank_candidates(
+        request_model=activity_request,
+        candidates=candidates,
+        augmented_query=augmented_query,
+        enhanced_category=enhanced_category,
+        modifiers=modifiers,
+        isic_codes=isic_codes
     )
-
-    matched_activity = next(
-        (
-            get_activity_from_dataset(
-                activity["activity_name"],
-                iso_code,
-                activity["similarity"],
-                activity["metadata"]["reference_product_name"]
-            )
-            for activity in activities
-            if closest_match.get("activity_uuid") == activity["uuid"]
-        ),
-        None,
+    logger.info(f"Phase 7 Complete: Selected '{matched_ef.activity_name}' with {matched_ef.confidence} confidence")
+
+    # Phase 1.8: Geography Matching & Record Retrieval
+    logger.info("Phase 8: Geography Matching & Record Retrieval - Starting")
+    matched_activity = get_activity_from_dataset(
+        activity_name=matched_ef.activity_name,
+        iso_code=iso_code,
+        similarity=matched_ef.confidence_score or 0.0,
+        reference_product_name=matched_ef.reference_product_name
     )
-
-    recommendations = [
-        get_activity_from_dataset(
-            activity["activity_name"],
-            iso_code,
-            activity["similarity"],
-            activity["metadata"]["reference_product_name"]
-        )
-        for activity in activities
-        if closest_match.get("activity_uuid") != activity["uuid"]
-    ]
+    logger.info(f"Phase 8 Complete: Geography matched to '{matched_activity.geography}'")
+
+    # Phase 1.9: Response Assembly
+    logger.info("Phase 9: Response Assembly - Starting")
+
+    # Create recommendations from remaining candidates (excluding the selected one)
+    recommendations = []
+    for candidate in candidates:
+        if candidate.activity_uuid != matched_ef.activity_uuid:
+            try:
+                recommendation = get_activity_from_dataset(
+                    activity_name=candidate.activity_name,
+                    iso_code=iso_code,
+                    similarity=candidate.similarity_score or 0.0,
+                    reference_product_name=candidate.reference_product_name
+                )
+                recommendations.append(recommendation)
+            except Exception as e:
+                logger.warning(f"Failed to create recommendation for {candidate.activity_uuid}: {str(e)}")
+                continue
+
+    logger.info(f"Phase 9 Complete: Assembled response with {len(recommendations)} recommendations")
 
     return ActivityRecommendationsResponse(
         matched_activity=matched_activity,
-        confidence=closest_match["confidence"],
-        explanation=closest_match["match_explanation"],
+        confidence=matched_ef.confidence,
+        explanation=matched_ef.explanation,
         recommendations=recommendations,
     )
 
@@ -350,7 +345,7 @@ def get_cache_stats():
         num_items = len(cache)
     except (TypeError, NotImplementedError):
         num_items = "Not available for this cache type"
-    
+
     return {
         "size (KB)": size_kb,
         "number of items": num_items,
diff --git a/emissions_factor_matching/dataset.py b/emissions_factor_matching/dataset.py
index e3271ec..d38ca18 100644
--- a/emissions_factor_matching/dataset.py
+++ b/emissions_factor_matching/dataset.py
@@ -2,11 +2,14 @@ import os
 import zipfile
 import pandas as pd
 import numpy as np
+import time
+from typing import List, Dict, Any, Optional
 from torch import tensor
 from huggingface_hub import hf_hub_download, login
 import chromadb
-from emissions_factor_matching.model import embedding_function, eol_embedding_function
+from emissions_factor_matching.model import embedding_function, eol_embedding_function, Candidate
 from config import config
+from utils import logger
 login(token=config.hf_token)
 
 chroma_db_zip = hf_hub_download(
@@ -48,3 +51,164 @@ efs_with_geographies_filepath = hf_hub_download(
 )
 
 efs_with_geographies = pd.read_pickle(efs_with_geographies_filepath)
+
+
+def search_candidates(augmented_query: str, filters: Dict[str, Any], n_results: int = 25) -> List[Candidate]:
+    """
+    Phase 1.6: ChromaDB Vector Search Enhancement
+
+    Performs enhanced vector search using the augmented query from Phase 1.4
+    and dynamic filters from Phase 1.5 to find the most relevant emission factor candidates.
+
+    Args:
+        augmented_query: Enhanced query text from Phase 1.4 (e.g., "Heavy-duty diesel freight transport truck >32 tonnes long-haul road transportation logistics")
+        filters: Dynamic filters from Phase 1.5 (e.g., {"$and": [{"activity_type": {"$eq": "ordinary transforming activity"}}, {"isic_4_code": {"$eq": "4923"}}]})
+        n_results: Number of candidates to return (default: 25)
+
+    Returns:
+        List[Candidate]: List of candidate emission factors with metadata and similarity scores
+    """
+    log_prefix = "phase_6_search"
+    logger.info(f"{log_prefix} Executing ChromaDB vector search")
+    logger.info(f"{log_prefix} Query: '{augmented_query}'")
+    logger.info(f"{log_prefix} Filters: {filters}")
+    logger.info(f"{log_prefix} Requesting {n_results} results")
+
+    start_time = time.time()
+
+    try:
+        # Execute ChromaDB query with enhanced parameters
+        results = collection.query(
+            query_texts=[augmented_query],
+            where=filters,
+            n_results=n_results,
+            include=['metadatas', 'documents', 'distances']
+        )
+
+        elapsed_time = (time.time() - start_time) * 1000
+
+        # Extract results
+        ids = results.get('ids', [[]])[0]
+        documents = results.get('documents', [[]])[0]
+        metadatas = results.get('metadatas', [[]])[0]
+        distances = results.get('distances', [[]])[0]
+
+        num_results = len(ids)
+        logger.info(f"{log_prefix} ChromaDB search completed in {elapsed_time:.2f}ms")
+        logger.info(f"{log_prefix} Retrieved {num_results} candidates")
+
+        if num_results == 0:
+            logger.warning(f"{log_prefix} No candidates found for query")
+            return []
+
+        # Convert ChromaDB results to Candidate objects
+        candidates = []
+        for i in range(num_results):
+            try:
+                metadata = metadatas[i] if i < len(metadatas) else {}
+                distance = distances[i] if i < len(distances) else 1.0
+
+                # Extract ISIC code from ChromaDB format "4923:Freight transport by road"
+                isic_classification = metadata.get('ISIC Classification', '')
+                isic_4_code = None
+                if isinstance(isic_classification, str) and ':' in isic_classification:
+                    isic_4_code = isic_classification.split(':')[0].strip()
+                elif isinstance(isic_classification, (int, str)) and str(isic_classification).strip():
+                    isic_4_code = str(isic_classification).strip()
+
+                # Ensure we have a valid ISIC code
+                if not isic_4_code or isic_4_code == '':
+                    isic_4_code = None
+
+                # Create Candidate object with all available metadata
+                candidate = Candidate(
+                    # Core identifiers
+                    activity_uuid=metadata.get('uuid', ids[i]),
+                    chroma_id=ids[i],
+
+                    # Activity information
+                    activity_name=documents[i] if i < len(documents) else "Unknown Activity",
+                    reference_product_name=metadata.get('reference_product_name', ''),
+                    product_information=metadata.get('product_information'),
+
+                    # Source and classification
+                    source=metadata.get('source', 'Unknown'),
+                    isic_4_code=isic_4_code,  # Use extracted numeric code
+                    isic_section=metadata.get('isic_section'),
+                    activity_type=metadata.get('activity_type', 'ordinary transforming activity'),
+
+                    # Geography and units
+                    geography=metadata.get('geography'),
+                    unit=metadata.get('unit'),
+
+                    # Search relevance
+                    distance=distance,
+                    similarity_score=max(0.0, 1.0 - distance),
+
+                    # Additional metadata
+                    metadata=metadata
+                )
+
+                candidates.append(candidate)
+
+                # Log top candidates for debugging
+                if i < 3:  # Log first 3 candidates
+                    logger.info(f"{log_prefix} Candidate {i+1}: '{candidate.activity_name}' (distance: {distance:.4f}, similarity: {candidate.similarity_score:.4f})")
+
+            except Exception as e:
+                logger.error(f"{log_prefix} Error creating candidate {i}: {str(e)}")
+                continue
+
+        # Sort candidates by distance (ascending = most similar first)
+        candidates.sort(key=lambda x: x.distance)
+
+        logger.info(f"{log_prefix} Successfully created {len(candidates)} candidate objects")
+        logger.info(f"{log_prefix} Best match: '{candidates[0].activity_name}' (distance: {candidates[0].distance:.4f})")
+
+        return candidates
+
+    except Exception as e:
+        elapsed_time = (time.time() - start_time) * 1000
+        logger.error(f"{log_prefix} ChromaDB search failed in {elapsed_time:.2f}ms: {str(e)}")
+
+        # Return empty list on error
+        return []
+
+
+def search_candidates_with_fallback(augmented_query: str, filters: Dict[str, Any], n_results: int = 25) -> List[Candidate]:
+    """
+    Enhanced search with fallback strategy for better reliability
+
+    Attempts the primary search with full filters, then falls back to simpler filters
+    if no results are found, ensuring we always return some candidates when possible.
+    """
+    log_prefix = "phase_6_fallback"
+
+    # Try primary search first
+    candidates = search_candidates(augmented_query, filters, n_results)
+
+    if candidates:
+        logger.info(f"{log_prefix} Primary search successful with {len(candidates)} candidates")
+        return candidates
+
+    # Fallback 1: Try with just activity_type filter
+    logger.warning(f"{log_prefix} Primary search returned no results, trying fallback with basic filters")
+
+    basic_filters = {"activity_type": {"$eq": "ordinary transforming activity"}}
+    candidates = search_candidates(augmented_query, basic_filters, n_results)
+
+    if candidates:
+        logger.info(f"{log_prefix} Fallback search successful with {len(candidates)} candidates")
+        return candidates
+
+    # Fallback 2: Try with no filters (last resort)
+    logger.warning(f"{log_prefix} Fallback search returned no results, trying without filters")
+
+    candidates = search_candidates(augmented_query, {}, n_results)
+
+    if candidates:
+        logger.info(f"{log_prefix} No-filter search successful with {len(candidates)} candidates")
+    else:
+        logger.error(f"{log_prefix} All search strategies failed - no candidates found")
+
+    return candidates
diff --git a/emissions_factor_matching/model.py b/emissions_factor_matching/model.py
index 0016a45..9971b5b 100644
--- a/emissions_factor_matching/model.py
+++ b/emissions_factor_matching/model.py
@@ -1,4 +1,6 @@
 from chromadb.utils import embedding_functions
+from pydantic import BaseModel
+from typing import Dict, Any, Optional
 
 INSTRUCTION_PROMPT = "Retrieve the closest emissions activity name provided a product description based on product inputs and manufacturing methods, consider suitable proxies:"
 
@@ -13,3 +15,71 @@ eol_embedding_function = embedding_functions.InstructorEmbeddingFunction(
     model_name="hkunlp/instructor-large",
     instruction=EOL_INSTRUCTION_PROMPT,
 )
+
+
+class Candidate(BaseModel):
+    """
+    Data model for Phase 1.6: ChromaDB Vector Search candidates
+
+    Represents a candidate emission factor activity returned from ChromaDB search
+    with all necessary metadata for downstream processing.
+    """
+    # Core identifiers
+    activity_uuid: str
+    chroma_id: str
+
+    # Activity information
+    activity_name: str
+    reference_product_name: str
+    product_information: Optional[str] = None
+
+    # Source and classification
+    source: str
+    isic_4_code: Optional[str] = None
+    isic_section: Optional[str] = None
+    activity_type: str
+
+    # Geography and units
+    geography: Optional[str] = None
+    unit: Optional[str] = None
+
+    # Search relevance
+    distance: float  # ChromaDB distance score (lower = more similar)
+    similarity_score: Optional[float] = None  # Computed as 1 - distance
+
+    # Additional metadata
+    metadata: Dict[str, Any] = {}
+
+    def __post_init__(self):
+        """Compute similarity score from distance"""
+        if self.similarity_score is None and self.distance is not None:
+            self.similarity_score = max(0.0, 1.0 - self.distance)
+
+
+class MatchedEF(BaseModel):
+    """
+    Data model for final matched emission factor after all phases
+
+    Represents the final selected emission factor with confidence and explanation
+    from the AI-assisted matching pipeline.
+    """
+    # Core activity information (inherited from Candidate)
+    activity_uuid: str
+    activity_name: str
+    reference_product_name: str
+    product_information: Optional[str] = None
+    source: str
+    geography: Optional[str] = None
+    unit: Optional[str] = None
+
+    # AI-assisted matching results
+    confidence: str  # "HIGH", "MEDIUM", "LOW"
+    confidence_score: Optional[float] = None  # 0.0 to 1.0
+    explanation: str  # LLM explanation for the match
+
+    # Search and ranking information
+    original_distance: float  # Original ChromaDB distance
+    final_rank: int  # Final rank after LLM re-ranking
+
+    # Processing metadata
+    processing_metadata: Dict[str, Any] = {}
diff --git a/emissions_factor_matching/predictions.py b/emissions_factor_matching/predictions.py
index d2aa6ba..2e9210f 100644
--- a/emissions_factor_matching/predictions.py
+++ b/emissions_factor_matching/predictions.py
@@ -1,7 +1,10 @@
 from typing import Dict, Any, List
 import json
+import time
 from completion_utils import parse_json
 from emissions_factor_matching.dataset import efs_with_geographies
+from emissions_factor_matching.prompt import get_enhanced_input_category_prompt, get_modifier_spotting_prompt, get_llm_reranking_prompt
+from emissions_factor_matching.model import Candidate, MatchedEF
 from completions import get_chat_completion, get_async_chat_completion
 from clients import openai_client, openai_client_us_east, get_openai_async_client
 from config import config
@@ -86,32 +89,289 @@ def get_common_chemical_names(chemical_name: str):
     )
 
 def predict_input_category(input_str: str):
-    prompt = (
-        "Given an input, determine whether the input is a chemical or a more complex product.\n"
-        "Output:\n"
-        "CHEMICAL | PRODUCT"
-    )
+    """Legacy function for backward compatibility - calls enhanced version"""
+    # For backward compatibility, convert single string to request-like structure
+    from pydantic import BaseModel
+
+    class TempRequest(BaseModel):
+        user_query_primary: str
+        user_query_secondary: str | None = None
+        lca_lifecycle_stage: str | None = None
+        iso_code: str | None = None
+        product_category_context: str | None = None
+
+    temp_request = TempRequest(user_query_primary=input_str)
+    category = predict_enhanced_input_category(temp_request)
+
+    # Convert detailed category back to simple CHEMICAL/PRODUCT for backward compatibility
+    if category.startswith("CHEMICAL_"):
+        return "CHEMICAL"
+    elif category.startswith("PRODUCT_"):
+        return "PRODUCT"
+    elif category.startswith("SERVICE_"):
+        return "PRODUCT"  # Services treated as products in legacy system
+    else:
+        return "PRODUCT"  # Default fallback
+
+def predict_enhanced_input_category(request) -> str:
+    """
+    Phase 1.1: Enhanced Input Category Prediction
+    Returns detailed categorical classification for the AI-assisted pipeline
+    """
+    log_prefix = "phase_1_category"
+    logger.info(f"{log_prefix} Enhanced classification for: '{request.user_query_primary}'")
+
+    # Build context for the LLM
+    context_parts = [f"Primary Query: {request.user_query_primary}"]
+
+    if request.user_query_secondary:
+        context_parts.append(f"Secondary Query: {request.user_query_secondary}")
+
+    if request.lca_lifecycle_stage:
+        context_parts.append(f"Lifecycle Stage: {request.lca_lifecycle_stage}")
+
+    if request.iso_code:
+        context_parts.append(f"Geography: {request.iso_code}")
+
+    if request.product_category_context:
+        context_parts.append(f"Product Category Context: {request.product_category_context}")
+
+    context = "\n".join(context_parts)
 
     messages = [
         {
             "role": "system",
-            "content": prompt
+            "content": get_enhanced_input_category_prompt()
         },
         {
             "role": "user",
-            "content": (
-                f"{input_str}"
-            )
+            "content": context
         }
     ]
 
-    return get_chat_completion(
+    category = get_chat_completion(
         openai_client_us_east,
         messages,
         deployment=config.azure_openai_deployment,
         n_validations=0,
     )
 
+    # Clean up the response to ensure we get just the category code
+    category = category.strip().upper()
+
+    logger.info(f"{log_prefix} Enhanced classification result: {category}")
+
+    return category
+
+def spot_modifiers(request, input_category: str) -> List[str]:
+    """
+    Phase 1.2: Modifier Spotting
+    Extracts key modifiers and attributes from user queries
+    """
+    log_prefix = "phase_2_modifiers"
+    logger.info(f"{log_prefix} Spotting modifiers for category: {input_category}")
+
+    # Build context for the LLM including the input category
+    context_parts = [
+        f"Input Category: {input_category}",
+        f"Primary Query: {request.user_query_primary}"
+    ]
+
+    if request.user_query_secondary:
+        context_parts.append(f"Secondary Query: {request.user_query_secondary}")
+
+    if request.lca_lifecycle_stage:
+        context_parts.append(f"Lifecycle Stage: {request.lca_lifecycle_stage}")
+
+    if request.iso_code:
+        context_parts.append(f"Geography: {request.iso_code}")
+
+    if request.product_category_context:
+        context_parts.append(f"Product Category Context: {request.product_category_context}")
+
+    context = "\n".join(context_parts)
+
+    messages = [
+        {
+            "role": "system",
+            "content": get_modifier_spotting_prompt()
+        },
+        {
+            "role": "user",
+            "content": context
+        }
+    ]
+
+    response = get_chat_completion(
+        openai_client_us_east,
+        messages,
+        deployment=config.azure_openai_deployment_4o,
+        n_validations=0,
+        strict=True,  # Request JSON format
+    )
+
+    try:
+        # Parse the JSON response directly
+        modifiers = json.loads(response)
+
+        # Ensure we have a list
+        if not isinstance(modifiers, list):
+            logger.error(f"{log_prefix} Response is not a list: {type(modifiers)}")
+            return []
+
+        # Ensure all items are strings and clean them up
+        cleaned_modifiers = []
+        for modifier in modifiers:
+            if isinstance(modifier, str):
+                cleaned_modifier = modifier.strip()
+                if cleaned_modifier:  # Only add non-empty modifiers
+                    cleaned_modifiers.append(cleaned_modifier)
+
+        logger.info(f"{log_prefix} Extracted {len(cleaned_modifiers)} modifiers: {cleaned_modifiers}")
+        return cleaned_modifiers
+
+    except json.JSONDecodeError as e:
+        logger.error(f"{log_prefix} Failed to parse JSON response: {e}")
+        logger.error(f"{log_prefix} Raw response: {response}")
+        return []  # Return empty list on parsing failure
+    except Exception as e:
+        logger.error(f"{log_prefix} Unexpected error parsing modifiers: {e}")
+        logger.error(f"{log_prefix} Raw response: {response}")
+        return []  # Return empty list on parsing failure
+
+def map_isic_classification(enhanced_category: str, modifiers: List[str]) -> List[str]:
+    """
+    Phase 1.3: ISIC Classification Mapping
+    Maps enhanced categories and modifiers to actual ISIC codes from our database
+    """
+    log_prefix = "phase_3_isic"
+    logger.info(f"{log_prefix} Mapping ISIC for category: {enhanced_category}, modifiers: {modifiers}")
+
+    # Get available ISIC codes from our database
+    # Extract just the numeric codes from the format "4923:Freight transport by road"
+    raw_isic_codes = efs_with_geographies['ISIC Classification'].dropna().unique()
+    available_isic_codes = set()
+
+    for code in raw_isic_codes:
+        if isinstance(code, str) and ':' in code:
+            # Extract numeric part before the colon
+            numeric_code = code.split(':')[0].strip()
+            available_isic_codes.add(numeric_code)
+        elif isinstance(code, (int, str)):
+            # Handle pure numeric codes
+            available_isic_codes.add(str(code))
+
+    logger.debug(f"{log_prefix} Extracted {len(available_isic_codes)} available ISIC codes from database")
+
+    # Enhanced category to ISIC mapping based on our actual database
+    category_isic_mapping = {
+        # Transport Services
+        "SERVICE_TRANSPORT_ROAD_FREIGHT": ["4923"],  # Freight transport by road (256 activities)
+        "SERVICE_TRANSPORT_ROAD_PASSENGER": ["4922", "4921"],  # Passenger transport (123+14 activities)
+        "SERVICE_TRANSPORT_AIR": ["5110", "5120"],  # Passenger/Freight air transport
+        "SERVICE_TRANSPORT_MARITIME": ["5012", "5022"],  # Sea/Inland freight water transport
+        "SERVICE_TRANSPORT_RAIL": ["4911", "4912"],  # Passenger/Freight rail transport
+
+        # Energy Services
+        "SERVICE_ENERGY_ELECTRICITY": ["3510"],  # Electric power generation (3804 activities)
+        "SERVICE_ENERGY_HEATING": ["3520", "3530"],  # Gas distribution, Steam supply
+
+        # Waste Services
+        "SERVICE_WASTE_TREATMENT": ["3821", "3822", "3830"],  # Waste treatment, Materials recovery
+
+        # Chemical Categories
+        "CHEMICAL_ORGANIC_SOLVENT": ["2011"],  # Basic chemicals (2092 activities)
+        "CHEMICAL_INORGANIC_ACID": ["2011"],  # Basic chemicals
+        "CHEMICAL_POLYMER_PLASTIC": ["2013", "2220"],  # Plastics manufacturing
+        "CHEMICAL_FUEL_ENERGY": ["1920"],  # Refined petroleum products (412 activities)
+        "CHEMICAL_PHARMACEUTICAL": ["2011"],  # Basic chemicals (pharmaceutical grade)
+        "CHEMICAL_AGRICULTURAL": ["2012"],  # Fertilizers and nitrogen compounds
+        "CHEMICAL_OTHER": ["2011"],  # Basic chemicals (default)
+
+        # Product Categories
+        "PRODUCT_CONSTRUCTION_MATERIAL": ["2394", "2395"],  # Cement, Concrete products
+        "PRODUCT_ELECTRONICS_DEVICE": ["2592"],  # Treatment and coating of metals
+        "PRODUCT_AUTOMOTIVE_VEHICLE": ["2420"],  # Non-ferrous metals
+        "PRODUCT_PACKAGING_CONTAINER": ["2220"],  # Plastics products
+        "PRODUCT_TEXTILE_CLOTHING": ["1610"],  # Wood processing (proxy)
+        "PRODUCT_FURNITURE_FIXTURE": ["1610"],  # Sawmilling and planing of wood
+        "PRODUCT_OTHER": ["2011"],  # Basic chemicals (default)
+
+        # Agriculture/Food
+        "PRODUCT_FOOD_BEVERAGE": ["0111", "0161"],  # Growing cereals, Support activities
+
+        # Construction Services
+        "SERVICE_CONSTRUCTION": ["4220", "4290"],  # Construction projects
+
+        # Other Services
+        "SERVICE_OTHER": ["2011"],  # Default to basic chemicals
+    }
+
+    # Get candidate ISIC codes for this category
+    candidate_codes = category_isic_mapping.get(enhanced_category, [])
+
+    if not candidate_codes:
+        logger.warning(f"{log_prefix} No ISIC mapping found for category: {enhanced_category}")
+        return []
+
+    # Filter candidates to only include codes that exist in our database
+    valid_codes = [code for code in candidate_codes if code in available_isic_codes]
+
+    if not valid_codes:
+        logger.warning(f"{log_prefix} None of the mapped ISIC codes exist in database: {candidate_codes}")
+        return []
+
+    # Use modifiers to refine the selection
+    refined_codes = _refine_isic_with_modifiers(enhanced_category, valid_codes, modifiers)
+
+    logger.info(f"{log_prefix} Mapped to ISIC codes: {refined_codes}")
+    return refined_codes
+
+def _refine_isic_with_modifiers(category: str, isic_codes: List[str], modifiers: List[str]) -> List[str]:
+    """
+    Use modifiers to refine ISIC code selection
+    """
+    if not modifiers:
+        return isic_codes
+
+    # Transport-specific refinements
+    if category.startswith("SERVICE_TRANSPORT_ROAD"):
+        # Only refine if the category allows for both freight and passenger codes
+        if category == "SERVICE_TRANSPORT_ROAD_PASSENGER":
+            # For passenger category, prefer passenger codes if available
+            passenger_codes = [code for code in isic_codes if code in ["4922", "4921"]]
+            if passenger_codes:
+                return passenger_codes
+        elif category == "SERVICE_TRANSPORT_ROAD_FREIGHT":
+            # For freight category, prefer freight codes if available
+            freight_codes = [code for code in isic_codes if code in ["4923"]]
+            if freight_codes:
+                return freight_codes
+
+        # If category is generic road transport, use modifiers to decide
+        if category == "SERVICE_TRANSPORT_ROAD":
+            if any(mod in ["freight", "cargo", "goods", "delivery"] for mod in modifiers):
+                freight_codes = [code for code in isic_codes if code in ["4923"]]
+                if freight_codes:
+                    return freight_codes
+            elif any(mod in ["passenger", "people", "commuter", "urban"] for mod in modifiers):
+                passenger_codes = [code for code in isic_codes if code in ["4922", "4921"]]
+                if passenger_codes:
+                    return passenger_codes
+
+    # Chemical-specific refinements
+    if category.startswith("CHEMICAL_"):
+        # Could add refinements based on purity, grade, etc.
+        pass
+
+    # Product-specific refinements
+    if category.startswith("PRODUCT_"):
+        # Could add refinements based on material, size, etc.
+        pass
+
+    # Return all valid codes if no specific refinements apply
+    return isic_codes
+
 def predict_product_constituents(product: str):
     prompt = (
         "Given a product, determine the primary material of the primary components it is comprised of, keep this succinct."
@@ -535,3 +795,544 @@ def get_enhanced_activity_description(activity_name: str, activity_description:
     )
 
     return chat_completion.choices[0].message.content
+
+
+def augment_query_text(request_model, enhanced_category: str, modifiers: List[str], isic_codes: List[str] = None) -> str:
+    """
+    Phase 1.4: Query Text Augmentation
+
+    Transforms the enhanced category, modifiers, and ISIC codes from previous phases
+    into optimized search text for ChromaDB vector search.
+
+    Args:
+        request_model: The original request containing user queries and context
+        enhanced_category: Enhanced category from Phase 1.1 (e.g., "SERVICE_TRANSPORT_ROAD_FREIGHT")
+        modifiers: List of modifiers from Phase 1.2 (e.g., ["diesel", ">32t", "long-haul"])
+        isic_codes: List of ISIC codes from Phase 1.3 (e.g., ["4923"])
+
+    Returns:
+        str: Augmented query text optimized for vector search
+    """
+    from emissions_factor_matching.prompt import get_query_augmentation_prompt
+
+    log_prefix = "ajith_aravind"
+    logger.info(f"{log_prefix} Augmenting query text for category: '{enhanced_category}', modifiers: {modifiers}, ISIC: {isic_codes}")
+
+    start_time = time.time()
+
+    try:
+        # Get the prompt template
+        prompt = get_query_augmentation_prompt()
+
+        # Build context string with all available information
+        context_parts = []
+
+        # Add primary and secondary queries
+        context_parts.append(f"Primary Query: {request_model.user_query_primary}")
+        if hasattr(request_model, 'user_query_secondary') and request_model.user_query_secondary:
+            context_parts.append(f"Secondary Query: {request_model.user_query_secondary}")
+
+        # Add enhanced category
+        context_parts.append(f"Enhanced Category: {enhanced_category}")
+
+        # Add modifiers
+        if modifiers:
+            context_parts.append(f"Extracted Modifiers: {', '.join(modifiers)}")
+        else:
+            context_parts.append("Extracted Modifiers: None")
+
+        # Add ISIC codes
+        if isic_codes:
+            context_parts.append(f"ISIC Classification Codes: {', '.join(isic_codes)}")
+        else:
+            context_parts.append("ISIC Classification Codes: None")
+
+        # Add additional context if available
+        if hasattr(request_model, 'lca_lifecycle_stage') and request_model.lca_lifecycle_stage:
+            context_parts.append(f"Lifecycle Stage: {request_model.lca_lifecycle_stage}")
+
+        if hasattr(request_model, 'iso_code') and request_model.iso_code:
+            context_parts.append(f"Geography: {request_model.iso_code}")
+
+        if hasattr(request_model, 'product_category_context') and request_model.product_category_context:
+            context_parts.append(f"Product Category Context: {request_model.product_category_context}")
+
+        context_string = "\n".join(context_parts)
+
+        # Prepare messages for LLM
+        messages = [
+            {
+                "role": "system",
+                "content": prompt
+            },
+            {
+                "role": "user",
+                "content": f"Please generate an optimized search query based on the following information:\n\n{context_string}\n\nOptimized Search Query:"
+            }
+        ]
+
+        # Call LLM
+        augmented_query = get_chat_completion(
+            openai_client_us_east,
+            messages,
+            deployment=config.azure_openai_deployment,
+            n_validations=0,
+        )
+
+        # Clean up the response
+        augmented_query = augmented_query.strip()
+
+        elapsed_time = (time.time() - start_time) * 1000
+        logger.info(f"{log_prefix} Query augmentation completed in {elapsed_time:.2f}ms")
+        logger.info(f"{log_prefix} Original query: '{request_model.user_query_primary}'")
+        logger.info(f"{log_prefix} Augmented query: '{augmented_query}'")
+
+        return augmented_query
+
+    except Exception as e:
+        elapsed_time = (time.time() - start_time) * 1000
+        logger.error(f"{log_prefix} Query augmentation failed in {elapsed_time:.2f}ms: {str(e)}")
+
+        # Fallback: create a basic augmented query from available information
+        fallback_parts = [request_model.user_query_primary]
+
+        if hasattr(request_model, 'user_query_secondary') and request_model.user_query_secondary:
+            fallback_parts.append(request_model.user_query_secondary)
+
+        if modifiers:
+            fallback_parts.extend(modifiers)
+
+        fallback_query = " ".join(fallback_parts)
+        logger.warning(f"{log_prefix} Using fallback query: '{fallback_query}'")
+
+        return fallback_query
+
+
+def construct_dynamic_filters(request_model, enhanced_category: str, modifiers: List[str], isic_codes: List[str] = None) -> Dict[str, Any]:
+    """
+    Phase 1.5: Dynamic Filter Construction
+
+    Constructs intelligent ChromaDB filters based on the enhanced category, modifiers,
+    and ISIC codes from previous phases to narrow down the search space.
+
+    Args:
+        request_model: The original request containing user queries and context
+        enhanced_category: Enhanced category from Phase 1.1 (e.g., "SERVICE_TRANSPORT_ROAD_FREIGHT")
+        modifiers: List of modifiers from Phase 1.2 (e.g., ["diesel", ">32t", "long-haul"])
+        isic_codes: List of ISIC codes from Phase 1.3 (e.g., ["4923"])
+
+    Returns:
+        Dict[str, Any]: ChromaDB filter dictionary optimized for the query
+    """
+    log_prefix = "ajith_aravind"
+    logger.info(f"{log_prefix} Constructing dynamic filters for category: '{enhanced_category}', modifiers: {modifiers}, ISIC: {isic_codes}")
+
+    start_time = time.time()
+
+    try:
+        # Start with base filter
+        filters = []
+
+        # Always filter for ordinary transforming activities
+        filters.append({"activity_type": {"$eq": "ordinary transforming activity"}})
+
+        # Phase 1.5.1: ISIC-based filtering
+        if isic_codes and len(isic_codes) > 0:
+            # ChromaDB stores ISIC codes in format "4923:Freight transport by road"
+            # We need to filter using the "ISIC Classification" field with pattern matching
+            if len(isic_codes) == 1:
+                # Use regex to match the numeric code at the beginning
+                isic_pattern = f"^{isic_codes[0]}:"
+                filters.append({"ISIC Classification": {"$regex": isic_pattern}})
+                logger.info(f"{log_prefix} Added ISIC regex filter: {isic_pattern}")
+            else:
+                # Multiple ISIC codes - create OR condition with regex patterns
+                isic_patterns = [f"^{code}:" for code in isic_codes]
+                # Use $or to match any of the patterns
+                isic_filter = {"$or": [{"ISIC Classification": {"$regex": pattern}} for pattern in isic_patterns]}
+                filters.append(isic_filter)
+                logger.info(f"{log_prefix} Added ISIC regex filter for multiple codes: {isic_codes}")
+
+        # Phase 1.5.2: Category-based filtering
+        category_filters = _get_category_based_filters(enhanced_category)
+        if category_filters:
+            filters.extend(category_filters)
+            logger.info(f"{log_prefix} Added category-based filters: {len(category_filters)} filters")
+
+        # Phase 1.5.3: Modifier-based filtering
+        modifier_filters = _get_modifier_based_filters(modifiers, enhanced_category)
+        if modifier_filters:
+            filters.extend(modifier_filters)
+            logger.info(f"{log_prefix} Added modifier-based filters: {len(modifier_filters)} filters")
+
+        # Phase 1.5.4: User-specified filters (backward compatibility)
+        user_filters = _get_user_specified_filters(request_model)
+        if user_filters:
+            filters.extend(user_filters)
+            logger.info(f"{log_prefix} Added user-specified filters: {len(user_filters)} filters")
+
+        # Construct final filter
+        if len(filters) == 1:
+            final_filter = filters[0]
+        else:
+            final_filter = {"$and": filters}
+
+        elapsed_time = (time.time() - start_time) * 1000
+        logger.info(f"{log_prefix} Dynamic filter construction completed in {elapsed_time:.2f}ms")
+        logger.info(f"{log_prefix} Final filter: {final_filter}")
+
+        return final_filter
+
+    except Exception as e:
+        elapsed_time = (time.time() - start_time) * 1000
+        logger.error(f"{log_prefix} Dynamic filter construction failed in {elapsed_time:.2f}ms: {str(e)}")
+
+        # Fallback to basic filter
+        fallback_filter = {"activity_type": {"$eq": "ordinary transforming activity"}}
+        logger.warning(f"{log_prefix} Using fallback filter: {fallback_filter}")
+
+        return fallback_filter
+
+
+def _get_category_based_filters(enhanced_category: str) -> List[Dict[str, Any]]:
+    """
+    Get filters based on the enhanced category from Phase 1.1
+    """
+    filters = []
+
+    if not enhanced_category:
+        return filters
+
+    # Transport service filters
+    if "TRANSPORT" in enhanced_category:
+        # Focus on transport-related activities
+        transport_keywords = ["transport", "freight", "logistics", "delivery", "shipping"]
+        # Note: This would be implemented as a text search or category field filter
+        # For now, we'll use ISIC-based filtering which is more reliable
+        pass
+
+    # Chemical filters
+    elif "CHEMICAL" in enhanced_category:
+        # Focus on chemical production activities
+        if "ORGANIC" in enhanced_category:
+            # Organic chemicals typically in ISIC 20.1
+            pass
+        elif "FUEL" in enhanced_category:
+            # Fuel production typically in ISIC 19.2
+            pass
+
+    # Product filters
+    elif "PRODUCT" in enhanced_category:
+        if "CONSTRUCTION" in enhanced_category:
+            # Construction materials typically in ISIC 23
+            pass
+        elif "METAL" in enhanced_category:
+            # Metal products typically in ISIC 24-25
+            pass
+
+    # Service filters
+    elif "SERVICE" in enhanced_category:
+        if "ENERGY" in enhanced_category:
+            # Energy services typically in ISIC 35
+            pass
+
+    return filters
+
+
+def _get_modifier_based_filters(modifiers: List[str], enhanced_category: str) -> List[Dict[str, Any]]:
+    """
+    Get filters based on modifiers from Phase 1.2
+    """
+    filters = []
+
+    if not modifiers:
+        return filters
+
+    # Transport-specific modifier filters
+    if "TRANSPORT" in enhanced_category:
+        for modifier in modifiers:
+            # Vehicle size filters
+            if ">32t" in modifier or "32 ton" in modifier.lower():
+                # Filter for heavy-duty vehicles
+                # This could filter by activity names containing ">32" or "heavy"
+                pass
+            elif "diesel" in modifier.lower():
+                # Filter for diesel-powered activities
+                # This could filter by activity names containing "diesel"
+                pass
+            elif "electric" in modifier.lower():
+                # Filter for electric vehicles
+                pass
+
+    # Chemical-specific modifier filters
+    elif "CHEMICAL" in enhanced_category:
+        for modifier in modifiers:
+            if "high purity" in modifier.lower():
+                # Filter for high-purity chemical production
+                pass
+            elif "industrial grade" in modifier.lower():
+                # Filter for industrial-grade chemicals
+                pass
+
+    # Energy-specific modifier filters
+    elif "ENERGY" in enhanced_category or "SERVICE_ENERGY" in enhanced_category:
+        for modifier in modifiers:
+            if "renewable" in modifier.lower():
+                # Filter for renewable energy sources
+                pass
+            elif "grid" in modifier.lower():
+                # Filter for grid electricity
+                pass
+
+    return filters
+
+
+def _get_user_specified_filters(request_model) -> List[Dict[str, Any]]:
+    """
+    Get filters based on user-specified parameters (backward compatibility)
+    """
+    filters = []
+
+    # Valid units filter
+    if hasattr(request_model, 'valid_units') and request_model.valid_units:
+        filters.append({"unit": {"$in": request_model.valid_units}})
+
+    # LCIA method filter
+    if hasattr(request_model, 'lcia_method') and request_model.lcia_method and not getattr(request_model, 'carbon_only', False):
+        filters.append({request_model.lcia_method: {"$eq": True}})
+
+    return filters
+
+
+def re_rank_candidates(
+    request_model,
+    candidates: List[Candidate],
+    augmented_query: str,
+    enhanced_category: str,
+    modifiers: List[str],
+    isic_codes: List[str] = None
+) -> MatchedEF:
+    """
+    Phase 1.7: LLM Re-ranking & Justification
+
+    Uses LLM to analyze candidates from vector search and select the best match
+    with detailed justification and confidence scoring.
+
+    Args:
+        request_model: Original user request with context
+        candidates: List of candidates from Phase 1.6 vector search
+        augmented_query: Enhanced query text from Phase 1.4
+        enhanced_category: Category classification from Phase 1.1
+        modifiers: Extracted modifiers from Phase 1.2
+        isic_codes: ISIC codes from Phase 1.3
+
+    Returns:
+        MatchedEF: Single best match with confidence and explanation
+    """
+    log_prefix = "phase_7_rerank"
+    logger.info(f"{log_prefix} Starting LLM re-ranking of {len(candidates)} candidates")
+
+    start_time = time.time()
+
+    # Validate inputs
+    if not candidates:
+        logger.error(f"{log_prefix} No candidates provided for re-ranking")
+        raise ValueError("Cannot re-rank empty candidate list")
+
+    # Limit to top candidates for LLM analysis (cost optimization)
+    max_candidates = 5
+    top_candidates = candidates[:max_candidates]
+    logger.info(f"{log_prefix} Analyzing top {len(top_candidates)} candidates")
+
+    try:
+        # Build context for LLM
+        context_parts = []
+
+        # Original request context
+        context_parts.append("## USER REQUEST CONTEXT:")
+        if hasattr(request_model, 'user_query_primary'):
+            context_parts.append(f"Primary Query: {request_model.user_query_primary}")
+        if hasattr(request_model, 'user_query_secondary') and request_model.user_query_secondary:
+            context_parts.append(f"Secondary Query: {request_model.user_query_secondary}")
+        if hasattr(request_model, 'product_category_context') and request_model.product_category_context:
+            context_parts.append(f"Product Category: {request_model.product_category_context}")
+        if hasattr(request_model, 'lca_lifecycle_stage') and request_model.lca_lifecycle_stage:
+            context_parts.append(f"Lifecycle Stage: {request_model.lca_lifecycle_stage}")
+        if hasattr(request_model, 'iso_code') and request_model.iso_code:
+            context_parts.append(f"Geography: {request_model.iso_code}")
+
+        # Pipeline analysis results
+        context_parts.append("\n## PIPELINE ANALYSIS RESULTS:")
+        context_parts.append(f"Enhanced Category: {enhanced_category}")
+        context_parts.append(f"Extracted Modifiers: {', '.join(modifiers) if modifiers else 'None'}")
+        if isic_codes:
+            context_parts.append(f"ISIC Codes: {', '.join(isic_codes)}")
+        context_parts.append(f"Augmented Query: {augmented_query}")
+
+        # Candidate details
+        context_parts.append("\n## CANDIDATE EMISSION FACTORS:")
+        for i, candidate in enumerate(top_candidates, 1):
+            context_parts.append(f"\n**Candidate {i}:**")
+            context_parts.append(f"- UUID: {candidate.activity_uuid}")
+            context_parts.append(f"- Activity Name: {candidate.activity_name}")
+            context_parts.append(f"- Reference Product: {candidate.reference_product_name}")
+            if candidate.product_information:
+                context_parts.append(f"- Product Information: {candidate.product_information}")
+            context_parts.append(f"- Source: {candidate.source}")
+            if candidate.isic_4_code:
+                context_parts.append(f"- ISIC Code: {candidate.isic_4_code}")
+            if candidate.geography:
+                context_parts.append(f"- Geography: {candidate.geography}")
+            if candidate.unit:
+                context_parts.append(f"- Unit: {candidate.unit}")
+            context_parts.append(f"- Vector Similarity: {candidate.similarity_score:.4f}")
+            context_parts.append(f"- Vector Distance: {candidate.distance:.4f}")
+
+        context_string = "\n".join(context_parts)
+
+        # Prepare LLM messages
+        messages = [
+            {
+                "role": "system",
+                "content": get_llm_reranking_prompt()
+            },
+            {
+                "role": "user",
+                "content": f"Please analyze the following candidates and select the best emission factor match:\n\n{context_string}\n\nProvide your analysis in the required JSON format:"
+            }
+        ]
+
+        # Call LLM for re-ranking
+        logger.info(f"{log_prefix} Calling LLM for candidate analysis")
+        llm_response = get_chat_completion(
+            openai_client_us_east,
+            messages,
+            deployment=config.azure_openai_deployment_4o,
+            n_validations=0,
+            strict=True,  # Request JSON format
+        )
+
+        if not llm_response:
+            logger.error(f"{log_prefix} LLM returned empty response")
+            raise ValueError("LLM re-ranking failed: empty response")
+
+        # Parse LLM response
+        try:
+            rerank_result = parse_json(llm_response, model=dict)
+            logger.info(f"{log_prefix} LLM response parsed successfully")
+        except Exception as e:
+            logger.error(f"{log_prefix} Failed to parse LLM response: {str(e)}")
+            logger.error(f"{log_prefix} Raw LLM response: {llm_response}")
+            raise ValueError(f"Failed to parse LLM re-ranking response: {str(e)}")
+
+        # Validate required fields
+        required_fields = ["selected_candidate_uuid", "confidence", "confidence_score", "explanation"]
+        for field in required_fields:
+            if field not in rerank_result:
+                logger.error(f"{log_prefix} Missing required field in LLM response: {field}")
+                raise ValueError(f"LLM response missing required field: {field}")
+
+        # Find selected candidate
+        selected_uuid = rerank_result["selected_candidate_uuid"]
+        selected_candidate = None
+        final_rank = None
+
+        for i, candidate in enumerate(top_candidates):
+            if candidate.activity_uuid == selected_uuid:
+                selected_candidate = candidate
+                final_rank = i + 1
+                break
+
+        if not selected_candidate:
+            logger.error(f"{log_prefix} LLM selected UUID not found in candidates: {selected_uuid}")
+            # Fallback to first candidate
+            selected_candidate = top_candidates[0]
+            final_rank = 1
+            logger.warning(f"{log_prefix} Falling back to top vector search result")
+
+        # Validate confidence values
+        confidence = rerank_result["confidence"].upper()
+        if confidence not in ["HIGH", "MEDIUM", "LOW"]:
+            logger.warning(f"{log_prefix} Invalid confidence level: {confidence}, defaulting to MEDIUM")
+            confidence = "MEDIUM"
+
+        confidence_score = float(rerank_result["confidence_score"])
+        if not (0.0 <= confidence_score <= 1.0):
+            logger.warning(f"{log_prefix} Invalid confidence score: {confidence_score}, clamping to [0,1]")
+            confidence_score = max(0.0, min(1.0, confidence_score))
+
+        # Create MatchedEF result
+        matched_ef = MatchedEF(
+            # Core activity information
+            activity_uuid=selected_candidate.activity_uuid,
+            activity_name=selected_candidate.activity_name,
+            reference_product_name=selected_candidate.reference_product_name,
+            product_information=selected_candidate.product_information,
+            source=selected_candidate.source,
+            geography=selected_candidate.geography,
+            unit=selected_candidate.unit,
+
+            # AI-assisted matching results
+            confidence=confidence,
+            confidence_score=confidence_score,
+            explanation=rerank_result["explanation"],
+
+            # Search and ranking information
+            original_distance=selected_candidate.distance,
+            final_rank=final_rank,
+
+            # Processing metadata
+            processing_metadata={
+                "phase_7_rerank": {
+                    "candidates_analyzed": len(top_candidates),
+                    "vector_similarity": selected_candidate.similarity_score,
+                    "ranking_rationale": rerank_result.get("ranking_rationale", ""),
+                    "alternative_considerations": rerank_result.get("alternative_considerations", ""),
+                    "llm_processing_time_ms": (time.time() - start_time) * 1000
+                }
+            }
+        )
+
+        elapsed_time = (time.time() - start_time) * 1000
+        logger.info(f"{log_prefix} Re-ranking completed in {elapsed_time:.2f}ms")
+        logger.info(f"{log_prefix} Selected: '{matched_ef.activity_name}' (confidence: {confidence}, score: {confidence_score:.3f})")
+        logger.info(f"{log_prefix} Final rank: {final_rank} (was vector rank {final_rank})")
+
+        return matched_ef
+
+    except Exception as e:
+        elapsed_time = (time.time() - start_time) * 1000
+        logger.error(f"{log_prefix} Re-ranking failed in {elapsed_time:.2f}ms: {str(e)}")
+
+        # Fallback: return best vector search result as MatchedEF
+        logger.warning(f"{log_prefix} Falling back to top vector search result")
+        fallback_candidate = candidates[0]
+
+        return MatchedEF(
+            # Core activity information
+            activity_uuid=fallback_candidate.activity_uuid,
+            activity_name=fallback_candidate.activity_name,
+            reference_product_name=fallback_candidate.reference_product_name,
+            product_information=fallback_candidate.product_information,
+            source=fallback_candidate.source,
+            geography=fallback_candidate.geography,
+            unit=fallback_candidate.unit,
+
+            # AI-assisted matching results (fallback values)
+            confidence="MEDIUM",
+            confidence_score=fallback_candidate.similarity_score or 0.5,
+            explanation=f"Selected based on vector similarity ({fallback_candidate.similarity_score:.3f}). LLM re-ranking failed: {str(e)}",
+
+            # Search and ranking information
+            original_distance=fallback_candidate.distance,
+            final_rank=1,
+
+            # Processing metadata
+            processing_metadata={
+                "phase_7_rerank": {
+                    "status": "fallback",
+                    "error": str(e),
+                    "fallback_reason": "LLM re-ranking failed, using top vector result"
+                }
+            }
+        )
\ No newline at end of file
diff --git a/emissions_factor_matching/prompt.py b/emissions_factor_matching/prompt.py
index 980aa41..0b5c8f5 100644
--- a/emissions_factor_matching/prompt.py
+++ b/emissions_factor_matching/prompt.py
@@ -16,3 +16,251 @@ def construct_chemical_prompt(
 
     logger.info(prompt)
     return prompt
+
+def get_enhanced_input_category_prompt() -> str:
+    """
+    Prompt template for Phase 1.1: Enhanced Input Category Prediction
+    Returns a detailed categorical classification instead of simple CHEMICAL/PRODUCT
+    """
+    return """You are an expert in environmental impact assessment and emission factor classification.
+Your task is to analyze user queries and classify them into specific, detailed categories that will help in finding the most appropriate emission factors.
+
+Given the user's input context, classify the query into ONE of the following detailed categories:
+
+**CHEMICAL CATEGORIES:**
+- CHEMICAL_ORGANIC_SOLVENT (organic solvents, alcohols, ketones, etc.)
+- CHEMICAL_INORGANIC_ACID (acids, bases, salts)
+- CHEMICAL_POLYMER_PLASTIC (polymers, plastics, resins)
+- CHEMICAL_METAL_COMPOUND (metal oxides, metal salts, alloys)
+- CHEMICAL_FUEL_ENERGY (fuels, energy carriers, combustibles)
+- CHEMICAL_PHARMACEUTICAL (drugs, active ingredients, medical compounds)
+- CHEMICAL_AGRICULTURAL (fertilizers, pesticides, herbicides)
+- CHEMICAL_OTHER (other chemical substances)
+
+**PRODUCT CATEGORIES:**
+- PRODUCT_ELECTRONICS_DEVICE (smartphones, computers, appliances)
+- PRODUCT_AUTOMOTIVE_VEHICLE (cars, trucks, motorcycles)
+- PRODUCT_TEXTILE_CLOTHING (fabrics, garments, footwear)
+- PRODUCT_CONSTRUCTION_MATERIAL (cement, steel, wood, insulation)
+- PRODUCT_PACKAGING_CONTAINER (bottles, boxes, wrapping materials)
+- PRODUCT_FOOD_BEVERAGE (food items, drinks, agricultural products)
+- PRODUCT_FURNITURE_FIXTURE (furniture, fixtures, home goods)
+- PRODUCT_MEDICAL_EQUIPMENT (medical devices, instruments, supplies)
+- PRODUCT_OTHER (other manufactured products)
+
+**SERVICE CATEGORIES:**
+- SERVICE_TRANSPORT_ROAD_FREIGHT (truck transportation, delivery services)
+- SERVICE_TRANSPORT_ROAD_PASSENGER (bus, taxi, ride-sharing)
+- SERVICE_TRANSPORT_AIR (aviation, air freight, passenger flights)
+- SERVICE_TRANSPORT_MARITIME (shipping, marine transport)
+- SERVICE_TRANSPORT_RAIL (train transport, rail freight)
+- SERVICE_ENERGY_ELECTRICITY (electricity generation, grid services)
+- SERVICE_ENERGY_HEATING (heating services, thermal energy)
+- SERVICE_WASTE_TREATMENT (waste processing, recycling, disposal)
+- SERVICE_CONSTRUCTION (building services, infrastructure)
+- SERVICE_OTHER (other service activities)
+
+**ANALYSIS INSTRUCTIONS:**
+1. Consider the primary query and any secondary context provided
+2. Look for key indicators like materials, processes, end-use applications
+3. Consider the lifecycle stage if provided (production, use, disposal)
+4. Consider geographical context if relevant to the classification
+5. Choose the MOST SPECIFIC category that fits the query
+
+**OUTPUT FORMAT:**
+Return ONLY the category code (e.g., "SERVICE_TRANSPORT_ROAD_FREIGHT") with no additional text or explanation."""
+
+def get_modifier_spotting_prompt() -> str:
+    """
+    Prompt template for Phase 1.2: Modifier Spotting
+    Extracts key modifiers and attributes from user queries
+    """
+    return """You are an expert in environmental impact assessment and emission factor analysis.
+Your task is to extract key modifiers and attributes from user queries that are relevant for finding the most appropriate emission factors.
+
+Analyze the provided query context and extract specific modifiers that would affect emission factor selection. Focus on:
+
+**TRANSPORT MODIFIERS:**
+- Fuel type: diesel, gasoline, electric, hybrid, natural gas, biodiesel
+- Vehicle size/weight: <3.5t, 3.5-7.5t, 7.5-16t, 16-32t, >32t, light duty, heavy duty
+- Driving conditions: urban, highway, mixed, city, rural, long-haul
+- Vehicle type: truck, van, car, bus, motorcycle, freight, passenger
+- Load factor: empty, partial load, full load, return trip
+
+**CHEMICAL/MATERIAL MODIFIERS:**
+- Purity/grade: high purity, industrial grade, pharmaceutical grade, technical grade
+- Physical state: liquid, solid, gas, powder, crystalline
+- Concentration: dilute, concentrated, pure, mixed
+- Source/origin: synthetic, natural, bio-based, recycled
+- Processing: refined, crude, processed, raw
+
+**PRODUCT MODIFIERS:**
+- Material composition: steel, aluminum, plastic, wood, composite
+- Quality/grade: high strength, lightweight, premium, standard
+- Manufacturing process: cast, forged, machined, molded, extruded
+- Recycled content: recycled, virgin, post-consumer, post-industrial
+- Size/scale: small, medium, large, industrial, commercial, residential
+
+**ENERGY MODIFIERS:**
+- Source type: renewable, fossil, nuclear, solar, wind, hydro
+- Grid mix: national grid, regional grid, specific utility
+- Efficiency: high efficiency, standard, low efficiency
+- Technology: combined cycle, simple cycle, cogeneration
+
+**GEOGRAPHIC/TEMPORAL MODIFIERS:**
+- Regional specifics: European, Asian, North American
+- Climate conditions: cold climate, hot climate, temperate
+- Seasonal: summer, winter, peak, off-peak
+
+**ANALYSIS INSTRUCTIONS:**
+1. Extract modifiers from both primary and secondary queries
+2. Consider the input category context to focus on relevant modifier types
+3. Look for quantitative specifications (weights, percentages, sizes)
+4. Identify qualitative attributes (efficiency, purity, grade)
+5. Extract implicit modifiers from context clues
+6. Prioritize modifiers that would significantly impact emission factors
+
+**OUTPUT FORMAT:**
+Return a JSON array of modifier strings, each representing a distinct attribute or specification.
+Example: ["diesel", ">32t", "long-haul", "full load"]
+If no relevant modifiers are found, return an empty array: []
+
+**IMPORTANT:**
+- Only extract modifiers that are explicitly mentioned or clearly implied
+- Use standardized terminology when possible
+- Keep modifiers concise but descriptive
+- Do not invent modifiers that aren't supported by the input"""
+
+def get_query_augmentation_prompt() -> str:
+    """
+    Prompt template for Phase 1.4: Query Text Augmentation
+    Transforms enhanced category + modifiers + ISIC codes into optimized search queries
+    """
+    return """You are an expert in environmental impact assessment and emission factor databases.
+Your task is to transform structured query information into optimized search text that will effectively match against emission factor activity descriptions in a vector database.
+
+Given the enhanced category classification, extracted modifiers, and ISIC codes from previous analysis phases, create a descriptive search query that:
+
+**OPTIMIZATION GOALS:**
+1. **Semantic Richness**: Use terminology that matches how emission factors are described in databases
+2. **Technical Precision**: Include specific technical terms, processes, and industry language
+3. **Context Expansion**: Add relevant synonyms, related processes, and industry context
+4. **Vector Search Optimization**: Structure text to maximize similarity with emission factor descriptions
+
+**AUGMENTATION STRATEGIES:**
+
+**For CHEMICAL Categories:**
+- Include chemical class, industrial applications, production methods
+- Add common synonyms, CAS-related terminology, purity grades
+- Mention typical manufacturing processes, feedstocks, end-uses
+- Include relevant industry sectors (petrochemical, pharmaceutical, etc.)
+
+**For PRODUCT Categories:**
+- Describe materials, manufacturing processes, typical applications
+- Include component materials, assembly methods, quality grades
+- Add lifecycle context (production, use phase, disposal)
+- Mention relevant industry standards, certifications
+
+**For SERVICE Categories:**
+- Detail operational characteristics, equipment types, energy sources
+- Include scale indicators, efficiency levels, technology types
+- Add geographic/regulatory context, service delivery methods
+- Mention infrastructure requirements, operational parameters
+
+**ISIC CODE INTEGRATION:**
+- Incorporate industry-specific terminology from the ISIC classification
+- Add sector-specific processes, equipment, and operational context
+- Include regulatory and technical standards relevant to the ISIC sector
+
+**MODIFIER ENHANCEMENT:**
+- Expand abbreviated modifiers (">32t" → "heavy duty freight vehicles over 32 tonnes")
+- Add technical context for specifications (diesel → "diesel fuel combustion in compression ignition engines")
+- Include related operational parameters and efficiency considerations
+
+**OUTPUT REQUIREMENTS:**
+- Generate 2-4 sentences of descriptive text
+- Use natural language that flows well for embedding models
+- Include 8-15 key technical terms and concepts
+- Balance specificity with searchability
+- Avoid overly generic terms, focus on distinctive characteristics
+
+**EXAMPLES:**
+
+Input: Category="SERVICE_TRANSPORT_ROAD_FREIGHT", Modifiers=["diesel", ">32t", "long-haul"], ISIC=["4923"]
+Output: "Heavy duty diesel freight transportation services using vehicles exceeding 32 tonnes gross weight for long-distance cargo delivery. Road freight transport operations involving compression ignition diesel engines in commercial trucking fleets for inter-regional goods movement and logistics services."
+
+Input: Category="CHEMICAL_ORGANIC_SOLVENT", Modifiers=["high purity", "industrial grade"], ISIC=["2011"]
+Output: "High purity organic solvent production for industrial applications in chemical manufacturing processes. Industrial grade solvent synthesis involving distillation, purification, and quality control in petrochemical facilities for use in coatings, adhesives, and chemical processing operations."
+
+**INSTRUCTIONS:**
+1. Analyze the provided category, modifiers, and ISIC codes
+2. Generate descriptive text optimized for vector similarity search
+3. Include relevant technical terminology and industry context
+4. Ensure the output is natural language suitable for embedding models
+5. Focus on terms that would appear in emission factor activity descriptions"""
+
+
+def get_llm_reranking_prompt() -> str:
+    """
+    Phase 1.7: LLM Re-ranking & Justification Prompt
+
+    Provides the system prompt for LLM-based candidate re-ranking and justification.
+    This prompt guides the LLM to analyze candidates from vector search and select
+    the best match with detailed reasoning.
+    """
+    return """You are an expert emission factor matching specialist with deep knowledge of industrial processes, chemical production, transportation, and environmental impact assessment.
+
+Your task is to analyze candidate emission factor activities from a vector database search and select the single best match for a user's request. You must provide detailed justification for your selection.
+
+## ANALYSIS FRAMEWORK:
+
+1. **Contextual Relevance**: How well does the candidate match the user's specific context (industry, process, geography, scale)?
+
+2. **Technical Accuracy**: Does the candidate represent the same or highly similar:
+   - Production process/method
+   - Input materials and energy sources
+   - Output products and co-products
+   - Technology level and efficiency
+
+3. **Scope Alignment**: Does the candidate cover the appropriate:
+   - System boundaries (cradle-to-gate, gate-to-gate, etc.)
+   - Life cycle stages
+   - Geographic representativeness
+   - Temporal relevance
+
+4. **Data Quality**: Consider:
+   - Source reliability (Ecoinvent, IDEMAT, etc.)
+   - Data completeness and uncertainty
+   - Methodological consistency
+   - Geographic and temporal representativeness
+
+## CONFIDENCE LEVELS:
+
+- **HIGH (0.8-1.0)**: Direct match with same process, materials, and context. Minimal uncertainty.
+- **MEDIUM (0.5-0.79)**: Good proxy with similar process but some differences in materials, scale, or geography.
+- **LOW (0.0-0.49)**: Acceptable proxy but significant differences requiring careful interpretation.
+
+## OUTPUT REQUIREMENTS:
+
+You must respond with a valid JSON object containing:
+
+```json
+{
+    "selected_candidate_uuid": "string - UUID of the selected candidate",
+    "confidence": "string - HIGH, MEDIUM, or LOW",
+    "confidence_score": "number - 0.0 to 1.0 confidence score",
+    "explanation": "string - Detailed explanation (200-400 words) of why this candidate was selected",
+    "ranking_rationale": "string - Brief explanation of how candidates were ranked",
+    "alternative_considerations": "string - Brief note on other candidates considered and why they were not selected"
+}
+```
+
+## IMPORTANT GUIDELINES:
+
+- Always select exactly ONE candidate (never return null or empty)
+- Provide specific, technical reasoning in your explanation
+- Consider the user's full context, not just keyword matching
+- Prioritize process similarity over product name similarity
+- Account for geographic and technological differences
+- Be honest about limitations and uncertainties
+- Use clear, professional language suitable for LCA practitioners"""
diff --git a/emissions_factor_matching/tests/test_chromadb_with_phase4.py b/emissions_factor_matching/tests/test_chromadb_with_phase4.py
new file mode 100644
index 0000000..41a6197
--- /dev/null
+++ b/emissions_factor_matching/tests/test_chromadb_with_phase4.py
@@ -0,0 +1,195 @@
+import unittest
+from unittest.mock import patch
+import logging
+
+# Set up logging to see what's happening
+logging.basicConfig(level=logging.INFO)
+logger = logging.getLogger(__name__)
+
+from emissions_factor_matching.dataset import collection
+from emissions_factor_matching.predictions import augment_query_text
+from pydantic import BaseModel
+
+
+class TestRequest(BaseModel):
+    user_query_primary: str
+    user_query_secondary: str | None = None
+
+
+class TestChromaDBWithPhase4(unittest.TestCase):
+    """Test ChromaDB search results with Phase 1.4 augmented queries"""
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    def test_chromadb_original_vs_augmented_query(self, mock_chat_completion):
+        """Compare ChromaDB results: original query vs Phase 1.4 augmented query"""
+        logger.info("🔍 Testing ChromaDB search: Original vs Augmented Query")
+        
+        # Mock the LLM response for query augmentation
+        mock_chat_completion.return_value = "Heavy duty diesel freight transportation services using vehicles exceeding 32 tonnes gross weight for long-distance cargo delivery. Road freight transport operations involving compression ignition diesel engines in commercial trucking fleets for inter-regional goods movement and logistics services."
+        
+        # Original simple query
+        original_query = "truck transportation"
+        
+        # Get augmented query from Phase 1.4
+        request = TestRequest(
+            user_query_primary=original_query,
+            user_query_secondary="diesel fuel, over 32 tonnes"
+        )
+        
+        enhanced_category = "SERVICE_TRANSPORT_ROAD_FREIGHT"
+        modifiers = ["diesel", ">32t", "long-haul"]
+        isic_codes = ["4923"]
+        
+        augmented_query = augment_query_text(request, enhanced_category, modifiers, isic_codes)
+        
+        logger.info(f"📝 Original Query: '{original_query}'")
+        logger.info(f"📝 Augmented Query: '{augmented_query}'")
+        
+        # Search ChromaDB with original query
+        logger.info("\n🔍 SEARCHING CHROMADB WITH ORIGINAL QUERY...")
+        original_results = collection.query(
+            query_texts=[original_query],
+            n_results=5,
+            where={"activity_type": {"$eq": "ordinary transforming activity"}}
+        )
+        
+        # Search ChromaDB with augmented query
+        logger.info("\n🔍 SEARCHING CHROMADB WITH AUGMENTED QUERY...")
+        augmented_results = collection.query(
+            query_texts=[augmented_query],
+            n_results=5,
+            where={"activity_type": {"$eq": "ordinary transforming activity"}}
+        )
+        
+        # Display results comparison
+        logger.info("\n📊 RESULTS COMPARISON:")
+        logger.info("=" * 80)
+        
+        logger.info("\n🔸 ORIGINAL QUERY RESULTS:")
+        if original_results['documents'] and original_results['documents'][0]:
+            for i, (doc, metadata, distance) in enumerate(zip(
+                original_results['documents'][0][:3],
+                original_results['metadatas'][0][:3],
+                original_results['distances'][0][:3]
+            )):
+                logger.info(f"   {i+1}. Distance: {distance:.3f}")
+                logger.info(f"      Activity: {metadata.get('reference_product_name', 'N/A')}")
+                logger.info(f"      Description: {doc[:100]}...")
+                logger.info("")
+        else:
+            logger.info("   No results found")
+        
+        logger.info("\n🔸 AUGMENTED QUERY RESULTS:")
+        if augmented_results['documents'] and augmented_results['documents'][0]:
+            for i, (doc, metadata, distance) in enumerate(zip(
+                augmented_results['documents'][0][:3],
+                augmented_results['metadatas'][0][:3],
+                augmented_results['distances'][0][:3]
+            )):
+                logger.info(f"   {i+1}. Distance: {distance:.3f}")
+                logger.info(f"      Activity: {metadata.get('reference_product_name', 'N/A')}")
+                logger.info(f"      Description: {doc[:100]}...")
+                logger.info("")
+        else:
+            logger.info("   No results found")
+        
+        # Verify we got results
+        self.assertTrue(len(original_results['documents'][0]) > 0, "Should get results for original query")
+        self.assertTrue(len(augmented_results['documents'][0]) > 0, "Should get results for augmented query")
+        
+        # Compare result quality (distances should be different)
+        if (original_results['distances'][0] and augmented_results['distances'][0]):
+            original_best_distance = original_results['distances'][0][0]
+            augmented_best_distance = augmented_results['distances'][0][0]
+            
+            logger.info(f"\n📈 QUALITY COMPARISON:")
+            logger.info(f"   Original Query Best Distance: {original_best_distance:.3f}")
+            logger.info(f"   Augmented Query Best Distance: {augmented_best_distance:.3f}")
+            
+            if augmented_best_distance < original_best_distance:
+                logger.info("   ✅ Augmented query found better matches!")
+            elif augmented_best_distance > original_best_distance:
+                logger.info("   ⚠️  Original query had better distance, but augmented may have better semantic matches")
+            else:
+                logger.info("   ➡️  Similar distances")
+        
+        logger.info("\n✅ ChromaDB comparison test completed!")
+
+    def test_chromadb_search_with_transport_terms(self):
+        """Test ChromaDB search specifically for transport-related terms"""
+        logger.info("🚛 Testing ChromaDB search for transport activities")
+        
+        # Search for transport activities
+        transport_query = "freight transport road diesel truck lorry"
+        
+        results = collection.query(
+            query_texts=[transport_query],
+            n_results=10,
+            where={"activity_type": {"$eq": "ordinary transforming activity"}}
+        )
+        
+        logger.info(f"🔍 Searching for: '{transport_query}'")
+        logger.info(f"📊 Found {len(results['documents'][0])} results")
+        
+        # Display top results
+        if results['documents'] and results['documents'][0]:
+            logger.info("\n🔸 TOP TRANSPORT ACTIVITIES:")
+            for i, (doc, metadata, distance) in enumerate(zip(
+                results['documents'][0][:5],
+                results['metadatas'][0][:5],
+                results['distances'][0][:5]
+            )):
+                activity_name = metadata.get('reference_product_name', 'N/A')
+                logger.info(f"   {i+1}. Distance: {distance:.3f}")
+                logger.info(f"      Activity: {activity_name}")
+                logger.info(f"      UUID: {metadata.get('uuid', 'N/A')}")
+                logger.info(f"      Description: {doc[:150]}...")
+                logger.info("")
+                
+                # Check if it's transport-related
+                if any(term in activity_name.lower() for term in ['transport', 'freight', 'truck', 'lorry']):
+                    logger.info(f"      ✅ Transport-related activity found!")
+                logger.info("")
+        
+        # Verify we found transport activities
+        self.assertTrue(len(results['documents'][0]) > 0, "Should find transport activities")
+        
+        logger.info("✅ Transport search test completed!")
+
+    def test_chromadb_chemical_search(self):
+        """Test ChromaDB search for chemical activities"""
+        logger.info("🧪 Testing ChromaDB search for chemical activities")
+        
+        # Search for chemical activities
+        chemical_query = "ethanol alcohol production chemical manufacturing"
+        
+        results = collection.query(
+            query_texts=[chemical_query],
+            n_results=5,
+            where={"activity_type": {"$eq": "ordinary transforming activity"}}
+        )
+        
+        logger.info(f"🔍 Searching for: '{chemical_query}'")
+        logger.info(f"📊 Found {len(results['documents'][0])} results")
+        
+        # Display results
+        if results['documents'] and results['documents'][0]:
+            logger.info("\n🔸 TOP CHEMICAL ACTIVITIES:")
+            for i, (doc, metadata, distance) in enumerate(zip(
+                results['documents'][0],
+                results['metadatas'][0],
+                results['distances'][0]
+            )):
+                activity_name = metadata.get('reference_product_name', 'N/A')
+                logger.info(f"   {i+1}. Distance: {distance:.3f}")
+                logger.info(f"      Activity: {activity_name}")
+                logger.info(f"      Description: {doc[:100]}...")
+                logger.info("")
+        
+        self.assertTrue(len(results['documents'][0]) > 0, "Should find chemical activities")
+        
+        logger.info("✅ Chemical search test completed!")
+
+
+if __name__ == '__main__':
+    unittest.main()
diff --git a/emissions_factor_matching/tests/test_phase1_6_integration.py b/emissions_factor_matching/tests/test_phase1_6_integration.py
new file mode 100644
index 0000000..d87ac13
--- /dev/null
+++ b/emissions_factor_matching/tests/test_phase1_6_integration.py
@@ -0,0 +1,265 @@
+import unittest
+from unittest.mock import patch, MagicMock
+from pydantic import BaseModel
+import logging
+
+# Set up logging to see what's happening
+logging.basicConfig(level=logging.INFO)
+logger = logging.getLogger(__name__)
+
+from emissions_factor_matching.predictions import (
+    predict_enhanced_input_category,
+    spot_modifiers,
+    map_isic_classification,
+    augment_query_text,
+    construct_dynamic_filters
+)
+from emissions_factor_matching.dataset import search_candidates
+from emissions_factor_matching.model import Candidate
+
+
+class TestRequest(BaseModel):
+    user_query_primary: str
+    user_query_secondary: str | None = None
+    lca_lifecycle_stage: str | None = None
+    iso_code: str | None = None
+    product_category_context: str | None = None
+    valid_units: list | None = None
+    lcia_method: str | None = None
+    carbon_only: bool | None = None
+
+
+class TestPhase1to6Integration(unittest.TestCase):
+    """Test integration of Phases 1.1-1.6: Complete AI-assisted pipeline"""
+
+    def setUp(self):
+        """Set up test fixtures"""
+        # Mock ChromaDB response for transport query
+        self.mock_transport_response = {
+            'ids': [['ef-transport-1', 'ef-transport-2', 'ef-transport-3']],
+            'documents': [['transport, freight, lorry >32 metric ton', 'transport, freight, lorry 16-32 metric ton', 'transport, freight, lorry 7.5-16 metric ton']],
+            'metadatas': [[
+                {
+                    'uuid': 'ef-transport-1',
+                    'reference_product_name': 'transport, freight, lorry >32 metric ton',
+                    'product_information': 'Heavy-duty freight transport by road',
+                    'source': 'Ecoinvent 3.11',
+                    'isic_4_code': '4923',
+                    'isic_section': 'H - Transportation and storage',
+                    'activity_type': 'ordinary transforming activity',
+                    'geography': 'GLO',
+                    'unit': 'tkm'
+                },
+                {
+                    'uuid': 'ef-transport-2',
+                    'reference_product_name': 'transport, freight, lorry 16-32 metric ton',
+                    'product_information': 'Medium-duty freight transport by road',
+                    'source': 'Ecoinvent 3.11',
+                    'isic_4_code': '4923',
+                    'isic_section': 'H - Transportation and storage',
+                    'activity_type': 'ordinary transforming activity',
+                    'geography': 'GLO',
+                    'unit': 'tkm'
+                },
+                {
+                    'uuid': 'ef-transport-3',
+                    'reference_product_name': 'transport, freight, lorry 7.5-16 metric ton',
+                    'product_information': 'Light-duty freight transport by road',
+                    'source': 'Ecoinvent 3.11',
+                    'isic_4_code': '4923',
+                    'isic_section': 'H - Transportation and storage',
+                    'activity_type': 'ordinary transforming activity',
+                    'geography': 'GLO',
+                    'unit': 'tkm'
+                }
+            ]],
+            'distances': [[0.154, 0.267, 0.389]]  # Improved distances from augmented query
+        }
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    @patch('emissions_factor_matching.dataset.collection')
+    def test_complete_transport_pipeline(self, mock_collection, mock_llm):
+        """Test complete pipeline for transport query (Phases 1.1-1.6)"""
+        logger.info("🚀 Testing complete AI-assisted pipeline (Phases 1.1-1.6)")
+
+        # Mock LLM responses for each phase
+        mock_llm.side_effect = [
+            "SERVICE_TRANSPORT_ROAD_FREIGHT",  # Phase 1.1: Enhanced category
+            '["diesel", ">32t", "long-haul", "heavy-duty"]',  # Phase 1.2: Modifiers
+            "Heavy-duty diesel freight transport truck over 32 tonnes long-haul road transportation logistics cargo delivery commercial vehicle operations"  # Phase 1.4: Augmented query
+        ]
+
+        # Mock ChromaDB response
+        mock_collection.query.return_value = self.mock_transport_response
+
+        # Create test request
+        request = TestRequest(
+            user_query_primary="truck transportation for heavy cargo",
+            user_query_secondary="diesel trucks over 32 tons",
+            lca_lifecycle_stage="use",
+            iso_code="US",
+            valid_units=["tkm"]
+        )
+
+        logger.info(f"📝 Input: '{request.user_query_primary}' + '{request.user_query_secondary}'")
+
+        # Phase 1.1: Enhanced Input Category Prediction
+        enhanced_category = predict_enhanced_input_category(request)
+        logger.info(f"🎯 Phase 1.1 - Enhanced Category: {enhanced_category}")
+        self.assertEqual(enhanced_category, "SERVICE_TRANSPORT_ROAD_FREIGHT")
+
+        # Phase 1.2: Modifier Spotting
+        modifiers = spot_modifiers(request, enhanced_category)
+        logger.info(f"🔍 Phase 1.2 - Modifiers: {modifiers}")
+        self.assertEqual(len(modifiers), 4)
+        self.assertIn("diesel", modifiers)
+        self.assertIn(">32t", modifiers)
+
+        # Phase 1.3: ISIC Classification Mapping
+        isic_codes = map_isic_classification(enhanced_category, modifiers)
+        logger.info(f"🏭 Phase 1.3 - ISIC Codes: {isic_codes}")
+        self.assertEqual(isic_codes, ["4923"])
+
+        # Phase 1.4: Query Text Augmentation
+        augmented_query = augment_query_text(request, enhanced_category, modifiers, isic_codes)
+        logger.info(f"📈 Phase 1.4 - Augmented Query: '{augmented_query}'")
+        self.assertIn("Heavy-duty", augmented_query)
+        self.assertIn("diesel", augmented_query)
+        self.assertIn("freight", augmented_query)
+
+        # Phase 1.5: Dynamic Filter Construction
+        filters = construct_dynamic_filters(request, enhanced_category, modifiers, isic_codes)
+        logger.info(f"🔧 Phase 1.5 - Dynamic Filters: {filters}")
+
+        # Verify filter structure (using correct ChromaDB format)
+        self.assertIn("$and", filters)
+        filter_conditions = filters["$and"]
+
+        # Check that all expected filter types are present
+        has_activity_type = any("activity_type" in condition for condition in filter_conditions)
+        has_isic_filter = any("ISIC Classification" in condition for condition in filter_conditions)
+        has_unit_filter = any("unit" in condition for condition in filter_conditions)
+
+        self.assertTrue(has_activity_type, "Should have activity_type filter")
+        self.assertTrue(has_isic_filter, "Should have ISIC Classification regex filter")
+        self.assertTrue(has_unit_filter, "Should have unit filter")
+
+        # Verify ISIC regex pattern
+        isic_condition = next(condition for condition in filter_conditions if "ISIC Classification" in condition)
+        self.assertEqual(isic_condition["ISIC Classification"]["$regex"], "^4923:")
+
+        # Phase 1.6: ChromaDB Vector Search
+        candidates = search_candidates(augmented_query, filters, n_results=10)
+        logger.info(f"🔍 Phase 1.6 - Found {len(candidates)} candidates")
+
+        # Verify search results
+        self.assertEqual(len(candidates), 3)
+        self.assertIsInstance(candidates[0], Candidate)
+
+        # Verify best match
+        best_match = candidates[0]
+        logger.info(f"🏆 Best Match: '{best_match.activity_name}' (distance: {best_match.distance:.4f})")
+
+        self.assertEqual(best_match.activity_uuid, 'ef-transport-1')
+        # ISIC code should be extracted from ChromaDB metadata (may be '4923' or None depending on extraction)
+        # The important thing is that we found the right activity
+        self.assertEqual(best_match.unit, 'tkm')
+        self.assertLess(best_match.distance, 0.2)  # Should be a good match
+
+        # Verify we found the correct transport activity
+        self.assertIn('transport', best_match.activity_name.lower())
+        self.assertIn('freight', best_match.activity_name.lower())
+
+        # Verify ChromaDB was called with enhanced parameters
+        mock_collection.query.assert_called_once_with(
+            query_texts=[augmented_query],
+            where=filters,
+            n_results=10,
+            include=['metadatas', 'documents', 'distances']
+        )
+
+        logger.info("✅ Complete transport pipeline test passed!")
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    @patch('emissions_factor_matching.dataset.collection')
+    def test_pipeline_performance_improvement(self, mock_collection, mock_llm):
+        """Test that the AI-assisted pipeline provides better results than simple search"""
+        logger.info("📊 Testing pipeline performance improvement")
+
+        # Mock LLM responses
+        mock_llm.side_effect = [
+            "SERVICE_ENERGY_ELECTRICITY",
+            '["renewable", "grid", "solar"]',
+            "Renewable solar photovoltaic electricity generation grid-connected power production clean energy sustainable"
+        ]
+
+        # Mock improved results for augmented query
+        improved_response = {
+            'ids': [['ef-solar-1']],
+            'documents': [['electricity production, photovoltaic']],
+            'metadatas': [[{
+                'uuid': 'ef-solar-1',
+                'reference_product_name': 'electricity, low voltage',
+                'source': 'Ecoinvent 3.11',
+                'isic_4_code': '3510',
+                'activity_type': 'ordinary transforming activity',
+                'unit': 'kWh'
+            }]],
+            'distances': [[0.089]]  # Much better distance than simple query
+        }
+
+        mock_collection.query.return_value = improved_response
+
+        request = TestRequest(
+            user_query_primary="solar power",
+            lca_lifecycle_stage="use"
+        )
+
+        # Run through pipeline
+        enhanced_category = predict_enhanced_input_category(request)
+        modifiers = spot_modifiers(request, enhanced_category)
+        isic_codes = map_isic_classification(enhanced_category, modifiers)
+        augmented_query = augment_query_text(request, enhanced_category, modifiers, isic_codes)
+        filters = construct_dynamic_filters(request, enhanced_category, modifiers, isic_codes)
+        candidates = search_candidates(augmented_query, filters)
+
+        # Verify improved performance
+        self.assertEqual(len(candidates), 1)
+        best_match = candidates[0]
+
+        # Should find a very relevant match with low distance
+        self.assertLess(best_match.distance, 0.1)  # Excellent match
+        self.assertIn("photovoltaic", best_match.activity_name)
+        self.assertEqual(best_match.isic_4_code, "3510")
+
+        logger.info(f"🎯 Improved match: '{best_match.activity_name}' (distance: {best_match.distance:.4f})")
+        logger.info("✅ Pipeline performance improvement test passed!")
+
+    def test_pipeline_robustness(self):
+        """Test pipeline robustness with edge cases"""
+        logger.info("🛡️ Testing pipeline robustness")
+
+        # Test with minimal input
+        minimal_request = TestRequest(user_query_primary="steel")
+
+        # Should not crash with minimal input
+        try:
+            enhanced_category = predict_enhanced_input_category(minimal_request)
+            modifiers = spot_modifiers(minimal_request, enhanced_category)
+            isic_codes = map_isic_classification(enhanced_category, modifiers)
+            filters = construct_dynamic_filters(minimal_request, enhanced_category, modifiers, isic_codes)
+
+            # All phases should complete without errors
+            self.assertIsInstance(enhanced_category, str)
+            self.assertIsInstance(modifiers, list)
+            self.assertIsInstance(isic_codes, list)
+            self.assertIsInstance(filters, dict)
+
+            logger.info("✅ Pipeline robustness test passed!")
+
+        except Exception as e:
+            self.fail(f"Pipeline failed with minimal input: {str(e)}")
+
+
+if __name__ == '__main__':
+    unittest.main()
diff --git a/emissions_factor_matching/tests/test_phase1_7_complete_integration.py b/emissions_factor_matching/tests/test_phase1_7_complete_integration.py
new file mode 100644
index 0000000..b687058
--- /dev/null
+++ b/emissions_factor_matching/tests/test_phase1_7_complete_integration.py
@@ -0,0 +1,242 @@
+import unittest
+from unittest.mock import patch, MagicMock
+from pydantic import BaseModel
+import logging
+
+# Set up logging to see what's happening
+logging.basicConfig(level=logging.INFO)
+logger = logging.getLogger(__name__)
+
+from emissions_factor_matching.predictions import (
+    predict_enhanced_input_category,
+    spot_modifiers,
+    map_isic_classification,
+    augment_query_text,
+    construct_dynamic_filters,
+    re_rank_candidates
+)
+from emissions_factor_matching.dataset import search_candidates
+from emissions_factor_matching.model import Candidate, MatchedEF
+
+
+class TestRequest(BaseModel):
+    user_query_primary: str
+    user_query_secondary: str | None = None
+    lca_lifecycle_stage: str | None = None
+    iso_code: str | None = None
+    product_category_context: str | None = None
+    valid_units: list | None = None
+    lcia_method: str | None = None
+    carbon_only: bool | None = None
+
+
+class TestPhase1to7CompleteIntegration(unittest.TestCase):
+    """Test complete integration of Phases 1.1-1.7: Full AI-assisted pipeline with LLM re-ranking"""
+
+    def setUp(self):
+        """Set up test fixtures"""
+        # Mock ChromaDB response for transport query
+        self.mock_transport_response = {
+            'ids': [['ef-transport-1', 'ef-transport-2', 'ef-transport-3']],
+            'documents': [['transport, freight, lorry >32 metric ton', 'transport, freight, lorry 16-32 metric ton', 'transport, freight, lorry 7.5-16 metric ton']],
+            'metadatas': [[
+                {
+                    'uuid': 'ef-transport-1',
+                    'reference_product_name': 'transport, freight, lorry >32 metric ton',
+                    'product_information': 'Heavy-duty freight transport by road',
+                    'source': 'Ecoinvent 3.11',
+                    'ISIC Classification': '4923:Freight transport by road',
+                    'isic_section': 'H - Transportation and storage',
+                    'activity_type': 'ordinary transforming activity',
+                    'geography': 'GLO',
+                    'unit': 'tkm'
+                },
+                {
+                    'uuid': 'ef-transport-2',
+                    'reference_product_name': 'transport, freight, lorry 16-32 metric ton',
+                    'product_information': 'Medium-duty freight transport by road',
+                    'source': 'Ecoinvent 3.11',
+                    'ISIC Classification': '4923:Freight transport by road',
+                    'isic_section': 'H - Transportation and storage',
+                    'activity_type': 'ordinary transforming activity',
+                    'geography': 'GLO',
+                    'unit': 'tkm'
+                },
+                {
+                    'uuid': 'ef-transport-3',
+                    'reference_product_name': 'transport, freight, lorry 7.5-16 metric ton',
+                    'product_information': 'Light-duty freight transport by road',
+                    'source': 'Ecoinvent 3.11',
+                    'ISIC Classification': '4923:Freight transport by road',
+                    'isic_section': 'H - Transportation and storage',
+                    'activity_type': 'ordinary transforming activity',
+                    'geography': 'GLO',
+                    'unit': 'tkm'
+                }
+            ]],
+            'distances': [[0.154, 0.267, 0.389]]
+        }
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    @patch('emissions_factor_matching.dataset.collection')
+    def test_complete_pipeline_with_phase7_reranking(self, mock_collection, mock_llm):
+        """Test complete pipeline including Phase 1.7 LLM re-ranking"""
+        logger.info("🚀 Testing complete AI-assisted pipeline (Phases 1.1-1.7)")
+
+        # Mock LLM responses for each phase
+        mock_llm.side_effect = [
+            "SERVICE_TRANSPORT_ROAD_FREIGHT",  # Phase 1.1: Enhanced category
+            '["diesel", ">32t", "long-haul", "heavy-duty"]',  # Phase 1.2: Modifiers
+            "Heavy-duty diesel freight transport truck over 32 tonnes long-haul road transportation logistics cargo delivery commercial vehicle operations",  # Phase 1.4: Augmented query
+            # Phase 1.7: LLM re-ranking response
+            """{
+                "selected_candidate_uuid": "ef-transport-1",
+                "confidence": "HIGH",
+                "confidence_score": 0.92,
+                "explanation": "This activity directly matches the user's request for heavy cargo truck transportation. The >32 metric ton specification aligns perfectly with the heavy cargo requirement, and the diesel fuel type is the standard for this vehicle class. The activity represents the exact transportation service requested with appropriate geographic scope and operational characteristics.",
+                "ranking_rationale": "Ranked based on vehicle weight class match (>32t), fuel type alignment (diesel), and service type precision (freight transport). The heavy-duty specification in the user query strongly indicates the need for the largest vehicle category.",
+                "alternative_considerations": "Other candidates represent smaller vehicle classes (16-32t, 7.5-16t) which would be less suitable for heavy cargo applications requiring maximum payload capacity."
+            }"""
+        ]
+
+        # Mock ChromaDB response
+        mock_collection.query.return_value = self.mock_transport_response
+
+        # Create test request
+        request = TestRequest(
+            user_query_primary="truck transportation for heavy cargo",
+            user_query_secondary="diesel trucks over 32 tons",
+            lca_lifecycle_stage="use",
+            iso_code="US",
+            valid_units=["tkm"]
+        )
+
+        logger.info(f"📝 Input: '{request.user_query_primary}' + '{request.user_query_secondary}'")
+
+        # Phase 1.1: Enhanced Input Category Prediction
+        enhanced_category = predict_enhanced_input_category(request)
+        logger.info(f"🎯 Phase 1.1 - Enhanced Category: {enhanced_category}")
+        self.assertEqual(enhanced_category, "SERVICE_TRANSPORT_ROAD_FREIGHT")
+
+        # Phase 1.2: Modifier Spotting
+        modifiers = spot_modifiers(request, enhanced_category)
+        logger.info(f"🔍 Phase 1.2 - Modifiers: {modifiers}")
+        self.assertEqual(len(modifiers), 4)
+        self.assertIn("diesel", modifiers)
+        self.assertIn(">32t", modifiers)
+
+        # Phase 1.3: ISIC Classification Mapping
+        isic_codes = map_isic_classification(enhanced_category, modifiers)
+        logger.info(f"🏭 Phase 1.3 - ISIC Codes: {isic_codes}")
+        self.assertEqual(isic_codes, ["4923"])
+
+        # Phase 1.4: Query Text Augmentation
+        augmented_query = augment_query_text(request, enhanced_category, modifiers, isic_codes)
+        logger.info(f"📈 Phase 1.4 - Augmented Query: '{augmented_query}'")
+        self.assertIn("Heavy-duty", augmented_query)
+        self.assertIn("diesel", augmented_query)
+        self.assertIn("freight", augmented_query)
+
+        # Phase 1.5: Dynamic Filter Construction
+        filters = construct_dynamic_filters(request, enhanced_category, modifiers, isic_codes)
+        logger.info(f"🔧 Phase 1.5 - Dynamic Filters: {filters}")
+
+        # Phase 1.6: ChromaDB Vector Search
+        candidates = search_candidates(augmented_query, filters, n_results=10)
+        logger.info(f"🔍 Phase 1.6 - Found {len(candidates)} candidates")
+        self.assertEqual(len(candidates), 3)
+        self.assertIsInstance(candidates[0], Candidate)
+
+        # Phase 1.7: LLM Re-ranking & Justification
+        matched_ef = re_rank_candidates(
+            request_model=request,
+            candidates=candidates,
+            augmented_query=augmented_query,
+            enhanced_category=enhanced_category,
+            modifiers=modifiers,
+            isic_codes=isic_codes
+        )
+        logger.info(f"🧠 Phase 1.7 - LLM Re-ranking Complete")
+
+        # Verify Phase 1.7 results
+        self.assertIsInstance(matched_ef, MatchedEF)
+        self.assertEqual(matched_ef.activity_uuid, "ef-transport-1")
+        self.assertEqual(matched_ef.confidence, "HIGH")
+        self.assertEqual(matched_ef.confidence_score, 0.92)
+        self.assertIn("heavy cargo truck transportation", matched_ef.explanation)
+        self.assertEqual(matched_ef.final_rank, 1)
+        self.assertEqual(matched_ef.original_distance, 0.154)
+
+        # Verify LLM reasoning quality
+        self.assertIn(">32 metric ton", matched_ef.explanation)
+        self.assertIn("diesel", matched_ef.explanation)
+        self.assertIn("heavy cargo", matched_ef.explanation)
+
+        # Verify processing metadata
+        self.assertIn("phase_7_rerank", matched_ef.processing_metadata)
+        phase7_metadata = matched_ef.processing_metadata["phase_7_rerank"]
+        self.assertEqual(phase7_metadata["candidates_analyzed"], 3)
+        self.assertIn("ranking_rationale", phase7_metadata)
+        self.assertIn("alternative_considerations", phase7_metadata)
+
+        logger.info(f"🏆 Final Selection: '{matched_ef.activity_name}' with {matched_ef.confidence} confidence")
+        logger.info(f"💡 Explanation: {matched_ef.explanation[:100]}...")
+        logger.info("✅ Complete pipeline with Phase 1.7 test passed!")
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    @patch('emissions_factor_matching.dataset.collection')
+    def test_phase7_reranking_changes_selection(self, mock_collection, mock_llm):
+        """Test that Phase 1.7 can intelligently re-rank and change the selection"""
+        logger.info("🔄 Testing Phase 1.7 intelligent re-ranking")
+
+        # Mock LLM responses - Phase 1.7 selects the second candidate instead of first
+        mock_llm.side_effect = [
+            "SERVICE_TRANSPORT_ROAD_FREIGHT",
+            '["diesel", "medium-duty"]',
+            "Medium-duty diesel freight transport truck 16-32 tonnes urban delivery",
+            # Phase 1.7: LLM selects the second candidate (medium-duty) as better match
+            """{
+                "selected_candidate_uuid": "ef-transport-2",
+                "confidence": "HIGH",
+                "confidence_score": 0.88,
+                "explanation": "While the first candidate offers higher payload capacity, the user's context suggests medium-duty transport is more appropriate. The 16-32 metric ton range provides optimal balance between capacity and operational flexibility for typical freight operations.",
+                "ranking_rationale": "Re-ranked based on operational context analysis. Medium-duty vehicles offer better fuel efficiency and maneuverability while still meeting freight requirements.",
+                "alternative_considerations": "Heavy-duty >32t option available but may be oversized for typical freight needs. Light-duty option insufficient for freight applications."
+            }"""
+        ]
+
+        mock_collection.query.return_value = self.mock_transport_response
+
+        request = TestRequest(
+            user_query_primary="freight transport truck",
+            user_query_secondary="medium capacity delivery",
+            valid_units=["tkm"]
+        )
+
+        # Run through pipeline
+        enhanced_category = predict_enhanced_input_category(request)
+        modifiers = spot_modifiers(request, enhanced_category)
+        isic_codes = map_isic_classification(enhanced_category, modifiers)
+        augmented_query = augment_query_text(request, enhanced_category, modifiers, isic_codes)
+        filters = construct_dynamic_filters(request, enhanced_category, modifiers, isic_codes)
+        candidates = search_candidates(augmented_query, filters)
+
+        # Verify vector search would select first candidate
+        self.assertEqual(candidates[0].activity_uuid, "ef-transport-1")  # Best vector similarity
+
+        # Phase 1.7: LLM re-ranking
+        matched_ef = re_rank_candidates(request, candidates, augmented_query, enhanced_category, modifiers, isic_codes)
+
+        # Verify LLM selected different candidate
+        self.assertEqual(matched_ef.activity_uuid, "ef-transport-2")  # LLM chose second candidate
+        self.assertEqual(matched_ef.final_rank, 2)  # Was ranked 2nd by vector search
+        self.assertEqual(matched_ef.confidence, "HIGH")
+        self.assertIn("medium-duty", matched_ef.explanation.lower())
+
+        logger.info(f"🎯 Vector search selected: {candidates[0].activity_name}")
+        logger.info(f"🧠 LLM re-ranking selected: {matched_ef.activity_name}")
+        logger.info("✅ Phase 1.7 intelligent re-ranking test passed!")
+
+
+if __name__ == '__main__':
+    unittest.main()
diff --git a/emissions_factor_matching/tests/test_phase1_category_prediction.py b/emissions_factor_matching/tests/test_phase1_category_prediction.py
new file mode 100644
index 0000000..680558f
--- /dev/null
+++ b/emissions_factor_matching/tests/test_phase1_category_prediction.py
@@ -0,0 +1,128 @@
+import unittest
+from unittest.mock import patch, MagicMock
+from pydantic import BaseModel
+import logging
+
+# Set up logging to see what's happening
+logging.basicConfig(level=logging.INFO)
+logger = logging.getLogger(__name__)
+
+from emissions_factor_matching.predictions import predict_enhanced_input_category
+
+
+class TestRequest(BaseModel):
+    user_query_primary: str
+    user_query_secondary: str | None = None
+    lca_lifecycle_stage: str | None = None
+    iso_code: str | None = None
+    product_category_context: str | None = None
+
+
+class TestPhase1CategoryPrediction(unittest.TestCase):
+    """Test Phase 1.1: Enhanced Input Category Prediction"""
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    @patch('emissions_factor_matching.predictions.get_enhanced_input_category_prompt')
+    def test_chemical_classification(self, mock_prompt, mock_completion):
+        """Test that chemicals are classified into detailed chemical categories"""
+        logger.info("Starting test_chemical_classification")
+
+        mock_prompt.return_value = "Mock prompt for testing"
+        mock_completion.return_value = "CHEMICAL_ORGANIC_SOLVENT"
+
+        request = TestRequest(user_query_primary="ethanol")
+        logger.info(f"Created request: {request}")
+
+        result = predict_enhanced_input_category(request)
+        logger.info(f"Got result: {result}")
+
+        self.assertEqual(result, "CHEMICAL_ORGANIC_SOLVENT")
+        mock_completion.assert_called_once()
+        logger.info("Test completed successfully")
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    def test_product_classification(self, mock_completion):
+        """Test that products are classified into detailed product categories"""
+        mock_completion.return_value = "PRODUCT_ELECTRONICS_DEVICE"
+
+        request = TestRequest(user_query_primary="smartphone")
+        result = predict_enhanced_input_category(request)
+
+        self.assertEqual(result, "PRODUCT_ELECTRONICS_DEVICE")
+        mock_completion.assert_called_once()
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    def test_service_classification(self, mock_completion):
+        """Test that services are classified into detailed service categories"""
+        mock_completion.return_value = "SERVICE_TRANSPORT_ROAD_FREIGHT"
+
+        request = TestRequest(
+            user_query_primary="truck transportation",
+            user_query_secondary="diesel fuel, >32 tonnes"
+        )
+        result = predict_enhanced_input_category(request)
+
+        self.assertEqual(result, "SERVICE_TRANSPORT_ROAD_FREIGHT")
+        mock_completion.assert_called_once()
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    def test_context_inclusion(self, mock_completion):
+        """Test that all context fields are included in the LLM prompt"""
+        mock_completion.return_value = "PRODUCT_CONSTRUCTION_MATERIAL"
+
+        request = TestRequest(
+            user_query_primary="concrete",
+            user_query_secondary="high strength",
+            lca_lifecycle_stage="production",
+            iso_code="DE",
+            product_category_context="building materials"
+        )
+
+        result = predict_enhanced_input_category(request)
+
+        # Verify the function was called
+        mock_completion.assert_called_once()
+
+        # Check that the context was properly formatted
+        call_args = mock_completion.call_args
+        messages = call_args[0][1]  # Second argument is messages
+        user_content = messages[1]["content"]
+
+        self.assertIn("Primary Query: concrete", user_content)
+        self.assertIn("Secondary Query: high strength", user_content)
+        self.assertIn("Lifecycle Stage: production", user_content)
+        self.assertIn("Geography: DE", user_content)
+        self.assertIn("Product Category Context: building materials", user_content)
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    def test_minimal_context(self, mock_completion):
+        """Test that function works with minimal context (only primary query)"""
+        mock_completion.return_value = "CHEMICAL_FUEL_ENERGY"
+
+        request = TestRequest(user_query_primary="gasoline")
+        result = predict_enhanced_input_category(request)
+
+        self.assertEqual(result, "CHEMICAL_FUEL_ENERGY")
+
+        # Verify only primary query is in context
+        call_args = mock_completion.call_args
+        messages = call_args[0][1]
+        user_content = messages[1]["content"]
+
+        self.assertIn("Primary Query: gasoline", user_content)
+        self.assertNotIn("Secondary Query:", user_content)
+        self.assertNotIn("Lifecycle Stage:", user_content)
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    def test_response_cleanup(self, mock_completion):
+        """Test that response is properly cleaned up (stripped and uppercased)"""
+        mock_completion.return_value = "  service_transport_air  "
+
+        request = TestRequest(user_query_primary="airplane flight")
+        result = predict_enhanced_input_category(request)
+
+        self.assertEqual(result, "SERVICE_TRANSPORT_AIR")
+
+
+if __name__ == '__main__':
+    unittest.main()
diff --git a/emissions_factor_matching/tests/test_phase1_simple.py b/emissions_factor_matching/tests/test_phase1_simple.py
new file mode 100644
index 0000000..2075781
--- /dev/null
+++ b/emissions_factor_matching/tests/test_phase1_simple.py
@@ -0,0 +1,108 @@
+import unittest
+import logging
+
+# Set up logging to see what's happening
+logging.basicConfig(level=logging.INFO)
+logger = logging.getLogger(__name__)
+
+class TestPhase1Simple(unittest.TestCase):
+    """Simple test to verify Phase 1 components are importable and basic structure works"""
+
+    def test_imports(self):
+        """Test that we can import the new Phase 1 functions"""
+        logger.info("Testing imports...")
+        
+        try:
+            from emissions_factor_matching.predictions import predict_enhanced_input_category
+            logger.info("✓ predict_enhanced_input_category imported successfully")
+        except ImportError as e:
+            self.fail(f"Failed to import predict_enhanced_input_category: {e}")
+        
+        try:
+            from emissions_factor_matching.prompt import get_enhanced_input_category_prompt
+            logger.info("✓ get_enhanced_input_category_prompt imported successfully")
+        except ImportError as e:
+            self.fail(f"Failed to import get_enhanced_input_category_prompt: {e}")
+        
+        logger.info("All imports successful!")
+
+    def test_prompt_function(self):
+        """Test that the prompt function returns a string"""
+        logger.info("Testing prompt function...")
+        
+        from emissions_factor_matching.prompt import get_enhanced_input_category_prompt
+        
+        prompt = get_enhanced_input_category_prompt()
+        
+        self.assertIsInstance(prompt, str)
+        self.assertGreater(len(prompt), 100)  # Should be a substantial prompt
+        self.assertIn("CHEMICAL_", prompt)  # Should contain category examples
+        self.assertIn("PRODUCT_", prompt)
+        self.assertIn("SERVICE_", prompt)
+        
+        logger.info(f"✓ Prompt function works, returned {len(prompt)} characters")
+
+    def test_request_model_structure(self):
+        """Test that we can create the new request model structure"""
+        logger.info("Testing request model structure...")
+        
+        try:
+            from pydantic import BaseModel
+            
+            class TestRequest(BaseModel):
+                user_query_primary: str
+                user_query_secondary: str | None = None
+                lca_lifecycle_stage: str | None = None
+                iso_code: str | None = None
+                product_category_context: str | None = None
+            
+            # Test creating a request
+            request = TestRequest(user_query_primary="test query")
+            self.assertEqual(request.user_query_primary, "test query")
+            self.assertIsNone(request.user_query_secondary)
+            
+            # Test with all fields
+            full_request = TestRequest(
+                user_query_primary="concrete",
+                user_query_secondary="high strength",
+                lca_lifecycle_stage="production",
+                iso_code="DE",
+                product_category_context="building materials"
+            )
+            self.assertEqual(full_request.user_query_primary, "concrete")
+            self.assertEqual(full_request.user_query_secondary, "high strength")
+            
+            logger.info("✓ Request model structure works correctly")
+            
+        except Exception as e:
+            self.fail(f"Request model test failed: {e}")
+
+    def test_api_model_backward_compatibility(self):
+        """Test that the API model supports both old and new fields"""
+        logger.info("Testing API model backward compatibility...")
+        
+        try:
+            from emissions_factor_matching.api import ActivityRecommendationsRequest
+            
+            # Test new format
+            new_request = ActivityRecommendationsRequest(
+                user_query_primary="truck transportation"
+            )
+            self.assertEqual(new_request.user_query_primary, "truck transportation")
+            
+            # Test backward compatibility
+            old_request = ActivityRecommendationsRequest(
+                user_query_primary="ethanol",
+                chemical_name="ethanol"  # Old field should still work
+            )
+            self.assertEqual(old_request.user_query_primary, "ethanol")
+            self.assertEqual(old_request.chemical_name, "ethanol")
+            
+            logger.info("✓ API model backward compatibility works")
+            
+        except Exception as e:
+            self.fail(f"API model test failed: {e}")
+
+
+if __name__ == '__main__':
+    unittest.main()
diff --git a/emissions_factor_matching/tests/test_phase2_modifier_spotting.py b/emissions_factor_matching/tests/test_phase2_modifier_spotting.py
new file mode 100644
index 0000000..8eb00ea
--- /dev/null
+++ b/emissions_factor_matching/tests/test_phase2_modifier_spotting.py
@@ -0,0 +1,162 @@
+import unittest
+from unittest.mock import patch
+from pydantic import BaseModel
+import logging
+import json
+
+# Set up logging to see what's happening
+logging.basicConfig(level=logging.INFO)
+logger = logging.getLogger(__name__)
+
+from emissions_factor_matching.predictions import spot_modifiers
+
+
+class TestRequest(BaseModel):
+    user_query_primary: str
+    user_query_secondary: str | None = None
+    lca_lifecycle_stage: str | None = None
+    iso_code: str | None = None
+    product_category_context: str | None = None
+
+
+class TestPhase2ModifierSpotting(unittest.TestCase):
+    """Test Phase 1.2: Modifier Spotting"""
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    @patch('emissions_factor_matching.predictions.get_modifier_spotting_prompt')
+    def test_transport_modifiers(self, mock_prompt, mock_completion):
+        """Test extraction of transport-related modifiers"""
+        logger.info("Starting test_transport_modifiers")
+        
+        mock_prompt.return_value = "Mock prompt for testing"
+        mock_completion.return_value = json.dumps(["diesel", ">32t", "long-haul"])
+        
+        request = TestRequest(
+            user_query_primary="truck transportation",
+            user_query_secondary="diesel fuel, over 32 tonnes, long distance"
+        )
+        
+        result = spot_modifiers(request, "SERVICE_TRANSPORT_ROAD_FREIGHT")
+        logger.info(f"Got result: {result}")
+        
+        self.assertEqual(result, ["diesel", ">32t", "long-haul"])
+        mock_completion.assert_called_once()
+        logger.info("Test completed successfully")
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    @patch('emissions_factor_matching.predictions.get_modifier_spotting_prompt')
+    def test_chemical_modifiers(self, mock_prompt, mock_completion):
+        """Test extraction of chemical-related modifiers"""
+        logger.info("Starting test_chemical_modifiers")
+        
+        mock_prompt.return_value = "Mock prompt for testing"
+        mock_completion.return_value = json.dumps(["high purity", "liquid", "industrial grade"])
+        
+        request = TestRequest(
+            user_query_primary="ethanol",
+            user_query_secondary="high purity, liquid form, industrial grade"
+        )
+        
+        result = spot_modifiers(request, "CHEMICAL_ORGANIC_SOLVENT")
+        
+        self.assertEqual(result, ["high purity", "liquid", "industrial grade"])
+        mock_completion.assert_called_once()
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    @patch('emissions_factor_matching.predictions.get_modifier_spotting_prompt')
+    def test_product_modifiers(self, mock_prompt, mock_completion):
+        """Test extraction of product-related modifiers"""
+        mock_prompt.return_value = "Mock prompt for testing"
+        mock_completion.return_value = json.dumps(["high strength", "recycled content", "steel"])
+        
+        request = TestRequest(
+            user_query_primary="concrete",
+            user_query_secondary="high strength, with recycled steel content"
+        )
+        
+        result = spot_modifiers(request, "PRODUCT_CONSTRUCTION_MATERIAL")
+        
+        self.assertEqual(result, ["high strength", "recycled content", "steel"])
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    @patch('emissions_factor_matching.predictions.get_modifier_spotting_prompt')
+    def test_no_modifiers(self, mock_prompt, mock_completion):
+        """Test handling when no modifiers are found"""
+        mock_prompt.return_value = "Mock prompt for testing"
+        mock_completion.return_value = json.dumps([])
+        
+        request = TestRequest(user_query_primary="water")
+        
+        result = spot_modifiers(request, "CHEMICAL_OTHER")
+        
+        self.assertEqual(result, [])
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    @patch('emissions_factor_matching.predictions.get_modifier_spotting_prompt')
+    def test_context_inclusion(self, mock_prompt, mock_completion):
+        """Test that all context fields are included in the LLM prompt"""
+        mock_prompt.return_value = "Mock prompt for testing"
+        mock_completion.return_value = json.dumps(["renewable", "grid mix"])
+        
+        request = TestRequest(
+            user_query_primary="electricity",
+            user_query_secondary="renewable sources",
+            lca_lifecycle_stage="use",
+            iso_code="DE",
+            product_category_context="energy supply"
+        )
+        
+        result = spot_modifiers(request, "SERVICE_ENERGY_ELECTRICITY")
+        
+        # Verify the function was called
+        mock_completion.assert_called_once()
+        
+        # Check that the context was properly formatted
+        call_args = mock_completion.call_args
+        messages = call_args[0][1]  # Second argument is messages
+        user_content = messages[1]["content"]
+        
+        self.assertIn("Input Category: SERVICE_ENERGY_ELECTRICITY", user_content)
+        self.assertIn("Primary Query: electricity", user_content)
+        self.assertIn("Secondary Query: renewable sources", user_content)
+        self.assertIn("Lifecycle Stage: use", user_content)
+        self.assertIn("Geography: DE", user_content)
+        self.assertIn("Product Category Context: energy supply", user_content)
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    @patch('emissions_factor_matching.predictions.get_modifier_spotting_prompt')
+    def test_parsing_error_handling(self, mock_prompt, mock_completion):
+        """Test handling of JSON parsing errors"""
+        mock_prompt.return_value = "Mock prompt for testing"
+        mock_completion.return_value = "Invalid JSON response"
+        
+        request = TestRequest(user_query_primary="test")
+        
+        result = spot_modifiers(request, "CHEMICAL_OTHER")
+        
+        # Should return empty list on parsing failure
+        self.assertEqual(result, [])
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    @patch('emissions_factor_matching.predictions.get_modifier_spotting_prompt')
+    def test_modifier_cleanup(self, mock_prompt, mock_completion):
+        """Test that modifiers are properly cleaned up"""
+        mock_prompt.return_value = "Mock prompt for testing"
+        mock_completion.return_value = json.dumps([
+            "  diesel  ",  # Should be trimmed
+            "",           # Should be removed
+            "high strength",  # Should be kept as-is
+            "   ",        # Should be removed
+            "32t"         # Should be kept
+        ])
+        
+        request = TestRequest(user_query_primary="truck")
+        
+        result = spot_modifiers(request, "SERVICE_TRANSPORT_ROAD_FREIGHT")
+        
+        # Should only contain non-empty, trimmed modifiers
+        self.assertEqual(result, ["diesel", "high strength", "32t"])
+
+
+if __name__ == '__main__':
+    unittest.main()
diff --git a/emissions_factor_matching/tests/test_phase3_isic_mapping.py b/emissions_factor_matching/tests/test_phase3_isic_mapping.py
new file mode 100644
index 0000000..6527174
--- /dev/null
+++ b/emissions_factor_matching/tests/test_phase3_isic_mapping.py
@@ -0,0 +1,177 @@
+import unittest
+from unittest.mock import patch, MagicMock
+import logging
+import pandas as pd
+
+# Set up logging to see what's happening
+logging.basicConfig(level=logging.INFO)
+logger = logging.getLogger(__name__)
+
+from emissions_factor_matching.predictions import map_isic_classification
+
+
+class TestPhase3ISICMapping(unittest.TestCase):
+    """Test Phase 1.3: ISIC Classification Mapping"""
+
+    def setUp(self):
+        """Set up mock data for testing"""
+        # Mock the efs_with_geographies DataFrame with real ISIC codes from our database
+        self.mock_isic_codes = [
+            "4923",  # Freight transport by road
+            "4922",  # Other passenger land transport
+            "4921",  # Urban and suburban passenger land transport
+            "3510",  # Electric power generation
+            "2011",  # Basic chemicals
+            "2394",  # Cement manufacturing
+            "2220",  # Plastics products
+            "1920",  # Refined petroleum products
+            "5110",  # Passenger air transport
+            "5120",  # Freight air transport
+        ]
+
+    @patch('emissions_factor_matching.predictions.efs_with_geographies')
+    def test_transport_freight_mapping(self, mock_df):
+        """Test mapping for road freight transport"""
+        logger.info("Starting test_transport_freight_mapping")
+        
+        # Mock the DataFrame
+        mock_df.__getitem__.return_value.dropna.return_value.unique.return_value = self.mock_isic_codes
+        
+        enhanced_category = "SERVICE_TRANSPORT_ROAD_FREIGHT"
+        modifiers = ["diesel", ">32t", "freight"]
+        
+        result = map_isic_classification(enhanced_category, modifiers)
+        logger.info(f"Got result: {result}")
+        
+        # Should map to freight transport code
+        self.assertEqual(result, ["4923"])
+        logger.info("Test completed successfully")
+
+    @patch('emissions_factor_matching.predictions.efs_with_geographies')
+    def test_transport_passenger_mapping(self, mock_df):
+        """Test mapping for road passenger transport"""
+        mock_df.__getitem__.return_value.dropna.return_value.unique.return_value = self.mock_isic_codes
+        
+        enhanced_category = "SERVICE_TRANSPORT_ROAD_PASSENGER"
+        modifiers = ["passenger", "urban", "commuter"]
+        
+        result = map_isic_classification(enhanced_category, modifiers)
+        
+        # Should map to passenger transport codes
+        self.assertIn("4922", result)
+        self.assertIn("4921", result)
+
+    @patch('emissions_factor_matching.predictions.efs_with_geographies')
+    def test_chemical_mapping(self, mock_df):
+        """Test mapping for chemical categories"""
+        mock_df.__getitem__.return_value.dropna.return_value.unique.return_value = self.mock_isic_codes
+        
+        enhanced_category = "CHEMICAL_ORGANIC_SOLVENT"
+        modifiers = ["high purity", "industrial grade"]
+        
+        result = map_isic_classification(enhanced_category, modifiers)
+        
+        # Should map to basic chemicals
+        self.assertEqual(result, ["2011"])
+
+    @patch('emissions_factor_matching.predictions.efs_with_geographies')
+    def test_product_mapping(self, mock_df):
+        """Test mapping for product categories"""
+        mock_df.__getitem__.return_value.dropna.return_value.unique.return_value = self.mock_isic_codes
+        
+        enhanced_category = "PRODUCT_CONSTRUCTION_MATERIAL"
+        modifiers = ["concrete", "high strength"]
+        
+        result = map_isic_classification(enhanced_category, modifiers)
+        
+        # Should map to cement/concrete codes
+        self.assertIn("2394", result)  # Cement manufacturing
+
+    @patch('emissions_factor_matching.predictions.efs_with_geographies')
+    def test_energy_mapping(self, mock_df):
+        """Test mapping for energy services"""
+        mock_df.__getitem__.return_value.dropna.return_value.unique.return_value = self.mock_isic_codes
+        
+        enhanced_category = "SERVICE_ENERGY_ELECTRICITY"
+        modifiers = ["renewable", "grid mix"]
+        
+        result = map_isic_classification(enhanced_category, modifiers)
+        
+        # Should map to electricity generation
+        self.assertEqual(result, ["3510"])
+
+    @patch('emissions_factor_matching.predictions.efs_with_geographies')
+    def test_unknown_category(self, mock_df):
+        """Test handling of unknown categories"""
+        mock_df.__getitem__.return_value.dropna.return_value.unique.return_value = self.mock_isic_codes
+        
+        enhanced_category = "UNKNOWN_CATEGORY"
+        modifiers = []
+        
+        result = map_isic_classification(enhanced_category, modifiers)
+        
+        # Should return empty list for unknown categories
+        self.assertEqual(result, [])
+
+    @patch('emissions_factor_matching.predictions.efs_with_geographies')
+    def test_invalid_isic_codes_filtered(self, mock_df):
+        """Test that invalid ISIC codes are filtered out"""
+        # Mock database with limited ISIC codes (missing some we try to map to)
+        limited_codes = ["4923", "2011"]  # Only freight transport and basic chemicals
+        mock_df.__getitem__.return_value.dropna.return_value.unique.return_value = limited_codes
+        
+        enhanced_category = "SERVICE_TRANSPORT_ROAD_PASSENGER"  # Maps to 4922, 4921
+        modifiers = ["passenger"]
+        
+        result = map_isic_classification(enhanced_category, modifiers)
+        
+        # Should return empty list since passenger codes don't exist in mock database
+        self.assertEqual(result, [])
+
+    @patch('emissions_factor_matching.predictions.efs_with_geographies')
+    def test_modifier_refinement_freight_vs_passenger(self, mock_df):
+        """Test that modifiers properly refine between freight and passenger transport"""
+        mock_df.__getitem__.return_value.dropna.return_value.unique.return_value = self.mock_isic_codes
+        
+        # Test freight modifiers
+        enhanced_category = "SERVICE_TRANSPORT_ROAD_FREIGHT"
+        freight_modifiers = ["freight", "cargo", "delivery"]
+        
+        freight_result = map_isic_classification(enhanced_category, freight_modifiers)
+        self.assertEqual(freight_result, ["4923"])  # Freight code
+        
+        # Test passenger modifiers on road transport (should still prefer freight for this category)
+        passenger_modifiers = ["passenger", "commuter"]
+        passenger_result = map_isic_classification(enhanced_category, passenger_modifiers)
+        self.assertEqual(passenger_result, ["4923"])  # Still freight because category is freight
+
+    @patch('emissions_factor_matching.predictions.efs_with_geographies')
+    def test_no_modifiers(self, mock_df):
+        """Test mapping without modifiers"""
+        mock_df.__getitem__.return_value.dropna.return_value.unique.return_value = self.mock_isic_codes
+        
+        enhanced_category = "CHEMICAL_FUEL_ENERGY"
+        modifiers = []
+        
+        result = map_isic_classification(enhanced_category, modifiers)
+        
+        # Should still map correctly without modifiers
+        self.assertEqual(result, ["1920"])  # Refined petroleum products
+
+    @patch('emissions_factor_matching.predictions.efs_with_geographies')
+    def test_air_transport_mapping(self, mock_df):
+        """Test mapping for air transport"""
+        mock_df.__getitem__.return_value.dropna.return_value.unique.return_value = self.mock_isic_codes
+        
+        enhanced_category = "SERVICE_TRANSPORT_AIR"
+        modifiers = ["passenger", "commercial"]
+        
+        result = map_isic_classification(enhanced_category, modifiers)
+        
+        # Should map to air transport codes
+        self.assertIn("5110", result)  # Passenger air transport
+        self.assertIn("5120", result)  # Freight air transport
+
+
+if __name__ == '__main__':
+    unittest.main()
diff --git a/emissions_factor_matching/tests/test_phase4_integration.py b/emissions_factor_matching/tests/test_phase4_integration.py
new file mode 100644
index 0000000..7ec2326
--- /dev/null
+++ b/emissions_factor_matching/tests/test_phase4_integration.py
@@ -0,0 +1,250 @@
+import unittest
+from unittest.mock import patch, MagicMock
+from fastapi.testclient import TestClient
+import logging
+
+# Set up logging to see what's happening
+logging.basicConfig(level=logging.INFO)
+logger = logging.getLogger(__name__)
+
+from emissions_factor_matching.api import model_api
+
+
+class TestPhase4Integration(unittest.TestCase):
+    """Test Phase 1.4 integration in the API pipeline"""
+
+    def setUp(self):
+        self.client = TestClient(model_api)
+
+    @patch('emissions_factor_matching.api.collection')
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    @patch('emissions_factor_matching.predictions.get_closest_match')
+    def test_api_with_phase4_augmentation(self, mock_closest_match, mock_chat_completion, mock_collection):
+        """Test that the API uses Phase 1.4 query augmentation in the pipeline"""
+        logger.info("Starting test_api_with_phase4_augmentation")
+
+        # Mock the LLM responses for all phases
+        def mock_llm_response(client, messages, **kwargs):
+            system_content = messages[0]["content"]
+            user_content = messages[1]["content"]
+
+            # Phase 1.1: Enhanced Input Category Prediction
+            if "detailed categorical classification" in system_content:
+                return "SERVICE_TRANSPORT_ROAD_FREIGHT"
+
+            # Phase 1.2: Modifier Spotting
+            elif "extract key modifiers" in system_content:
+                return '["diesel", ">32t", "long-haul"]'
+
+            # Phase 1.4: Query Text Augmentation
+            elif "optimized search text" in system_content:
+                return "Heavy duty diesel freight transportation services using vehicles exceeding 32 tonnes gross weight for long-distance cargo delivery. Road freight transport operations involving compression ignition diesel engines in commercial trucking fleets."
+
+            # Legacy functions (CAS, common names, description)
+            elif "cas number" in system_content.lower():
+                return "NONE"
+            elif "common chemical synonyms" in system_content:
+                return "NONE"
+            elif "succinct description" in system_content:
+                return "Transportation service description"
+            elif "ISIC Section" in system_content:
+                return "H"
+
+            # Default fallback
+            return "Mock response"
+
+        mock_chat_completion.side_effect = mock_llm_response
+
+        # Mock ChromaDB collection response
+        mock_collection.query.return_value = {
+            "ids": [["test-id-1", "test-id-2"]],
+            "documents": [["freight transport activity", "road transport service"]],
+            "metadatas": [[
+                {"uuid": "test-id-1", "reference_product_name": "freight transport", "source": "test"},
+                {"uuid": "test-id-2", "reference_product_name": "road transport", "source": "test"}
+            ]],
+            "distances": [[0.1, 0.2]]
+        }
+
+        # Mock the closest match response
+        mock_closest_match.return_value = {
+            "activity_uuid": "test-id-1",
+            "confidence": "high",
+            "match_explanation": "Best match for freight transport"
+        }
+
+        # Mock geography matching
+        with patch('emissions_factor_matching.geography.get_geography_activity_match') as mock_geography:
+            mock_geography.return_value = {
+                "Activity Name": "freight transport activity",
+                "Activity UUID": "test-id-1",
+                "Reference Product Name": "freight transport",
+                "Product Information": "Test product info",
+                "Source": "test",
+                "Geography": "GLO"
+            }
+
+            # Make API request with a unique query to avoid cache hits
+            response = self.client.post(
+                "/activities/recommendations",
+                json={
+                    "user_query_primary": "truck transportation phase4 test",
+                    "user_query_secondary": "diesel fuel, over 32 tonnes",
+                    "iso_code": "GLO"
+                }
+            )
+
+        # Verify response
+        self.assertEqual(response.status_code, 200)
+        data = response.json()
+
+        # Verify response structure
+        self.assertIn("matched_activity", data)
+        self.assertIn("confidence", data)
+        self.assertIn("explanation", data)
+        self.assertIn("recommendations", data)
+
+        # Verify that ChromaDB was called with the augmented query
+        mock_collection.query.assert_called_once()
+        call_args = mock_collection.query.call_args
+
+        # The query_texts should contain the augmented query from Phase 1.4
+        query_texts = call_args[1]["query_texts"]
+        self.assertEqual(len(query_texts), 1)
+
+        augmented_query = query_texts[0]
+        logger.info(f"ChromaDB was called with augmented query: '{augmented_query}'")
+
+        # Verify the augmented query contains expected transport terminology
+        self.assertIn("freight", augmented_query.lower())
+        self.assertIn("diesel", augmented_query.lower())
+        self.assertIn("transport", augmented_query.lower())
+
+        # Verify it's not the simple original query
+        self.assertNotEqual(augmented_query, "truck transportation")
+
+        logger.info("Test completed successfully")
+
+    @patch('emissions_factor_matching.api.collection')
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    @patch('emissions_factor_matching.predictions.get_closest_match')
+    def test_api_phase4_with_minimal_context(self, mock_closest_match, mock_chat_completion, mock_collection):
+        """Test Phase 1.4 with minimal context (only primary query)"""
+
+        # Mock the LLM responses
+        def mock_llm_response(client, messages, **kwargs):
+            system_content = messages[0]["content"]
+
+            if "detailed categorical classification" in system_content:
+                return "CHEMICAL_FUEL_ENERGY"
+            elif "extract key modifiers" in system_content:
+                return '[]'  # No modifiers
+            elif "optimized search text" in system_content:
+                return "Gasoline fuel production and refining operations for automotive applications. Petroleum product manufacturing involving crude oil processing and distillation."
+            elif "cas number" in system_content.lower():
+                return "NONE"
+            elif "common chemical synonyms" in system_content:
+                return "NONE"
+            elif "succinct description" in system_content:
+                return "Fuel description"
+            elif "ISIC Section" in system_content:
+                return "C"
+
+            return "Mock response"
+
+        mock_chat_completion.side_effect = mock_llm_response
+
+        # Mock ChromaDB collection response
+        mock_collection.query.return_value = {
+            "ids": [["fuel-id-1"]],
+            "documents": [["gasoline production"]],
+            "metadatas": [[{"uuid": "fuel-id-1", "reference_product_name": "gasoline", "source": "test"}]],
+            "distances": [[0.1]]
+        }
+
+        # Mock the closest match response
+        mock_closest_match.return_value = {
+            "activity_uuid": "fuel-id-1",
+            "confidence": "high",
+            "match_explanation": "Best match for gasoline"
+        }
+
+        # Mock geography matching
+        with patch('emissions_factor_matching.geography.get_geography_activity_match') as mock_geography:
+            mock_geography.return_value = {
+                "Activity Name": "gasoline production",
+                "Activity UUID": "fuel-id-1",
+                "Reference Product Name": "gasoline",
+                "Product Information": "Test fuel info",
+                "Source": "test",
+                "Geography": "GLO"
+            }
+
+            # Make API request with minimal context
+            response = self.client.post(
+                "/activities/recommendations",
+                json={
+                    "user_query_primary": "gasoline"
+                }
+            )
+
+        # Verify response
+        self.assertEqual(response.status_code, 200)
+
+        # Verify that ChromaDB was called with the augmented query
+        mock_collection.query.assert_called_once()
+        call_args = mock_collection.query.call_args
+        query_texts = call_args[1]["query_texts"]
+
+        augmented_query = query_texts[0]
+
+        # Verify the augmented query contains expected fuel terminology
+        self.assertIn("gasoline", augmented_query.lower())
+        self.assertIn("fuel", augmented_query.lower())
+        self.assertIn("production", augmented_query.lower())
+
+    def test_api_backward_compatibility(self):
+        """Test that the API maintains backward compatibility with chemical_name field"""
+
+        with patch('emissions_factor_matching.predictions.get_chat_completion') as mock_chat_completion:
+            with patch('emissions_factor_matching.predictions.get_closest_match') as mock_closest_match:
+                with patch('emissions_factor_matching.dataset.collection') as mock_collection:
+
+                    # Mock responses
+                    mock_chat_completion.return_value = "CHEMICAL_OTHER"
+                    mock_collection.query.return_value = {
+                        "ids": [["chem-id-1"]],
+                        "documents": [["chemical production"]],
+                        "metadatas": [[{"uuid": "chem-id-1", "reference_product_name": "chemical", "source": "test"}]],
+                        "distances": [[0.1]]
+                    }
+                    mock_closest_match.return_value = {
+                        "activity_uuid": "chem-id-1",
+                        "confidence": "medium",
+                        "match_explanation": "Chemical match"
+                    }
+
+                    with patch('emissions_factor_matching.geography.get_geography_activity_match') as mock_geography:
+                        mock_geography.return_value = {
+                            "Activity Name": "chemical production",
+                            "Activity UUID": "chem-id-1",
+                            "Reference Product Name": "chemical",
+                            "Product Information": "Test chemical info",
+                            "Source": "test",
+                            "Geography": "GLO"
+                        }
+
+                        # Test with old chemical_name field
+                        response = self.client.post(
+                            "/activities/recommendations",
+                            json={
+                                "chemical_name": "ethanol"  # Old field name
+                            }
+                        )
+
+                    # Should still work
+                    self.assertEqual(response.status_code, 200)
+
+
+if __name__ == '__main__':
+    unittest.main()
diff --git a/emissions_factor_matching/tests/test_phase4_query_augmentation.py b/emissions_factor_matching/tests/test_phase4_query_augmentation.py
new file mode 100644
index 0000000..01e0403
--- /dev/null
+++ b/emissions_factor_matching/tests/test_phase4_query_augmentation.py
@@ -0,0 +1,246 @@
+import unittest
+from unittest.mock import patch, MagicMock
+from pydantic import BaseModel
+import logging
+
+# Set up logging to see what's happening
+logging.basicConfig(level=logging.INFO)
+logger = logging.getLogger(__name__)
+
+from emissions_factor_matching.predictions import augment_query_text
+
+
+class TestRequest(BaseModel):
+    user_query_primary: str
+    user_query_secondary: str | None = None
+    lca_lifecycle_stage: str | None = None
+    iso_code: str | None = None
+    product_category_context: str | None = None
+
+
+class TestPhase4QueryAugmentation(unittest.TestCase):
+    """Test Phase 1.4: Query Text Augmentation"""
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    @patch('emissions_factor_matching.prompt.get_query_augmentation_prompt')
+    def test_transport_freight_augmentation(self, mock_prompt, mock_completion):
+        """Test query augmentation for transport freight services"""
+        logger.info("Starting test_transport_freight_augmentation")
+
+        mock_prompt.return_value = "Mock prompt for testing"
+        mock_completion.return_value = "Heavy duty diesel freight transportation services using vehicles exceeding 32 tonnes gross weight for long-distance cargo delivery. Road freight transport operations involving compression ignition diesel engines in commercial trucking fleets for inter-regional goods movement and logistics services."
+
+        request = TestRequest(
+            user_query_primary="truck transportation",
+            user_query_secondary="diesel fuel, over 32 tonnes"
+        )
+
+        enhanced_category = "SERVICE_TRANSPORT_ROAD_FREIGHT"
+        modifiers = ["diesel", ">32t", "long-haul"]
+        isic_codes = ["4923"]
+
+        result = augment_query_text(request, enhanced_category, modifiers, isic_codes)
+        logger.info(f"Got result: {result}")
+
+        # Verify the function was called
+        mock_completion.assert_called_once()
+
+        # Check that the result contains expected transport terminology
+        self.assertIn("freight", result.lower())
+        self.assertIn("diesel", result.lower())
+        self.assertIn("transport", result.lower())
+
+        logger.info("Test completed successfully")
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    @patch('emissions_factor_matching.prompt.get_query_augmentation_prompt')
+    def test_chemical_augmentation(self, mock_prompt, mock_completion):
+        """Test query augmentation for chemical categories"""
+        mock_prompt.return_value = "Mock prompt for testing"
+        mock_completion.return_value = "High purity organic solvent production for industrial applications in chemical manufacturing processes. Industrial grade solvent synthesis involving distillation, purification, and quality control in petrochemical facilities for use in coatings, adhesives, and chemical processing operations."
+
+        request = TestRequest(
+            user_query_primary="ethanol",
+            user_query_secondary="high purity, industrial grade"
+        )
+
+        enhanced_category = "CHEMICAL_ORGANIC_SOLVENT"
+        modifiers = ["high purity", "industrial grade"]
+        isic_codes = ["2011"]
+
+        result = augment_query_text(request, enhanced_category, modifiers, isic_codes)
+
+        # Verify the function was called
+        mock_completion.assert_called_once()
+
+        # Check that the result contains expected chemical terminology
+        self.assertIn("solvent", result.lower())
+        self.assertIn("chemical", result.lower())
+        self.assertIn("industrial", result.lower())
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    @patch('emissions_factor_matching.prompt.get_query_augmentation_prompt')
+    def test_product_augmentation(self, mock_prompt, mock_completion):
+        """Test query augmentation for product categories"""
+        mock_prompt.return_value = "Mock prompt for testing"
+        mock_completion.return_value = "High strength concrete production for construction applications involving cement manufacturing, aggregate mixing, and quality control processes. Construction material manufacturing with enhanced durability characteristics for infrastructure and building projects."
+
+        request = TestRequest(
+            user_query_primary="concrete",
+            user_query_secondary="high strength, construction grade"
+        )
+
+        enhanced_category = "PRODUCT_CONSTRUCTION_MATERIAL"
+        modifiers = ["high strength", "construction grade"]
+        isic_codes = ["2394"]
+
+        result = augment_query_text(request, enhanced_category, modifiers, isic_codes)
+
+        # Verify the function was called
+        mock_completion.assert_called_once()
+
+        # Check that the result contains expected product terminology
+        self.assertIn("concrete", result.lower())
+        self.assertIn("construction", result.lower())
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    @patch('emissions_factor_matching.prompt.get_query_augmentation_prompt')
+    def test_context_inclusion(self, mock_prompt, mock_completion):
+        """Test that all context fields are included in the LLM prompt"""
+        mock_prompt.return_value = "Mock prompt for testing"
+        mock_completion.return_value = "Renewable electricity generation services using grid-connected power systems for commercial energy supply. Electric power generation operations involving sustainable energy sources and grid distribution infrastructure for regional electricity markets."
+
+        request = TestRequest(
+            user_query_primary="electricity",
+            user_query_secondary="renewable sources",
+            lca_lifecycle_stage="use",
+            iso_code="DE",
+            product_category_context="energy supply"
+        )
+
+        enhanced_category = "SERVICE_ENERGY_ELECTRICITY"
+        modifiers = ["renewable", "grid mix"]
+        isic_codes = ["3510"]
+
+        result = augment_query_text(request, enhanced_category, modifiers, isic_codes)
+
+        # Verify the function was called
+        mock_completion.assert_called_once()
+
+        # Check that the context was properly formatted
+        call_args = mock_completion.call_args
+        messages = call_args[0][1]  # Second argument is messages
+        user_content = messages[1]["content"]
+
+        self.assertIn("Primary Query: electricity", user_content)
+        self.assertIn("Secondary Query: renewable sources", user_content)
+        self.assertIn("Enhanced Category: SERVICE_ENERGY_ELECTRICITY", user_content)
+        self.assertIn("Extracted Modifiers: renewable, grid mix", user_content)
+        self.assertIn("ISIC Classification Codes: 3510", user_content)
+        self.assertIn("Lifecycle Stage: use", user_content)
+        self.assertIn("Geography: DE", user_content)
+        self.assertIn("Product Category Context: energy supply", user_content)
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    @patch('emissions_factor_matching.prompt.get_query_augmentation_prompt')
+    def test_minimal_context(self, mock_prompt, mock_completion):
+        """Test that function works with minimal context (only primary query)"""
+        mock_prompt.return_value = "Mock prompt for testing"
+        mock_completion.return_value = "Gasoline fuel production and refining operations for automotive applications. Petroleum product manufacturing involving crude oil processing, distillation, and quality control in refinery facilities."
+
+        request = TestRequest(user_query_primary="gasoline")
+
+        enhanced_category = "CHEMICAL_FUEL_ENERGY"
+        modifiers = []
+        isic_codes = ["1920"]
+
+        result = augment_query_text(request, enhanced_category, modifiers, isic_codes)
+
+        # Verify the function was called
+        mock_completion.assert_called_once()
+
+        # Check that only primary query is in context
+        call_args = mock_completion.call_args
+        messages = call_args[0][1]
+        user_content = messages[1]["content"]
+
+        self.assertIn("Primary Query: gasoline", user_content)
+        self.assertIn("Enhanced Category: CHEMICAL_FUEL_ENERGY", user_content)
+        self.assertIn("Extracted Modifiers: None", user_content)
+        self.assertIn("ISIC Classification Codes: 1920", user_content)
+        self.assertNotIn("Secondary Query:", user_content)
+        self.assertNotIn("Lifecycle Stage:", user_content)
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    @patch('emissions_factor_matching.prompt.get_query_augmentation_prompt')
+    def test_no_modifiers_no_isic(self, mock_prompt, mock_completion):
+        """Test handling when no modifiers or ISIC codes are provided"""
+        mock_prompt.return_value = "Mock prompt for testing"
+        mock_completion.return_value = "Water treatment and purification processes for municipal and industrial applications. Water supply operations involving filtration, disinfection, and quality control systems."
+
+        request = TestRequest(user_query_primary="water")
+
+        enhanced_category = "CHEMICAL_OTHER"
+        modifiers = []
+        isic_codes = []
+
+        result = augment_query_text(request, enhanced_category, modifiers, isic_codes)
+
+        # Verify the function was called
+        mock_completion.assert_called_once()
+
+        # Check that empty modifiers and ISIC are handled properly
+        call_args = mock_completion.call_args
+        messages = call_args[0][1]
+        user_content = messages[1]["content"]
+
+        self.assertIn("Extracted Modifiers: None", user_content)
+        self.assertIn("ISIC Classification Codes: None", user_content)
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    @patch('emissions_factor_matching.prompt.get_query_augmentation_prompt')
+    def test_response_cleanup(self, mock_prompt, mock_completion):
+        """Test that response is properly cleaned up (stripped)"""
+        mock_prompt.return_value = "Mock prompt for testing"
+        mock_completion.return_value = "  \n  Air transport services for passenger and freight operations using commercial aircraft.  \n  "
+
+        request = TestRequest(user_query_primary="airplane flight")
+
+        enhanced_category = "SERVICE_TRANSPORT_AIR"
+        modifiers = ["passenger", "commercial"]
+        isic_codes = ["5110"]
+
+        result = augment_query_text(request, enhanced_category, modifiers, isic_codes)
+
+        # Check that the result is properly cleaned up
+        self.assertEqual(result, "Air transport services for passenger and freight operations using commercial aircraft.")
+        self.assertFalse(result.startswith(" "))
+        self.assertFalse(result.endswith(" "))
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    @patch('emissions_factor_matching.prompt.get_query_augmentation_prompt')
+    def test_llm_error_fallback(self, mock_prompt, mock_completion):
+        """Test fallback behavior when LLM call fails"""
+        mock_prompt.return_value = "Mock prompt for testing"
+        mock_completion.side_effect = Exception("LLM service unavailable")
+
+        request = TestRequest(
+            user_query_primary="steel production",
+            user_query_secondary="high strength alloy"
+        )
+
+        enhanced_category = "PRODUCT_CONSTRUCTION_MATERIAL"
+        modifiers = ["high strength", "alloy"]
+        isic_codes = ["2420"]
+
+        result = augment_query_text(request, enhanced_category, modifiers, isic_codes)
+
+        # Should return fallback query combining available information
+        self.assertIn("steel production", result)
+        self.assertIn("high strength alloy", result)
+        self.assertIn("high strength", result)
+        self.assertIn("alloy", result)
+
+
+if __name__ == '__main__':
+    unittest.main()
diff --git a/emissions_factor_matching/tests/test_phase4_simple.py b/emissions_factor_matching/tests/test_phase4_simple.py
new file mode 100644
index 0000000..97ec580
--- /dev/null
+++ b/emissions_factor_matching/tests/test_phase4_simple.py
@@ -0,0 +1,164 @@
+import unittest
+from unittest.mock import patch, MagicMock
+from pydantic import BaseModel
+import logging
+
+# Set up logging to see what's happening
+logging.basicConfig(level=logging.INFO)
+logger = logging.getLogger(__name__)
+
+from emissions_factor_matching.predictions import augment_query_text
+
+
+class TestRequest(BaseModel):
+    user_query_primary: str
+    user_query_secondary: str | None = None
+
+
+class TestPhase4Simple(unittest.TestCase):
+    """Simple test to verify Phase 1.4 functionality"""
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    def test_phase4_direct_call(self, mock_chat_completion):
+        """Test Phase 1.4 function directly with expected inputs from Phases 1-3"""
+        logger.info("Testing Phase 1.4 direct function call")
+        
+        # Mock the LLM response for query augmentation
+        mock_chat_completion.return_value = "Heavy duty diesel freight transportation services using vehicles exceeding 32 tonnes gross weight for long-distance cargo delivery. Road freight transport operations involving compression ignition diesel engines in commercial trucking fleets."
+        
+        # Create test request
+        request = TestRequest(
+            user_query_primary="truck transportation",
+            user_query_secondary="diesel fuel, over 32 tonnes"
+        )
+        
+        # Simulate outputs from Phases 1.1-1.3
+        enhanced_category = "SERVICE_TRANSPORT_ROAD_FREIGHT"
+        modifiers = ["diesel", ">32t", "long-haul"]
+        isic_codes = ["4923"]
+        
+        # Call Phase 1.4
+        result = augment_query_text(request, enhanced_category, modifiers, isic_codes)
+        
+        logger.info(f"Input: {request.user_query_primary}")
+        logger.info(f"Enhanced Category: {enhanced_category}")
+        logger.info(f"Modifiers: {modifiers}")
+        logger.info(f"ISIC Codes: {isic_codes}")
+        logger.info(f"Augmented Query: {result}")
+        
+        # Verify the function was called
+        mock_chat_completion.assert_called_once()
+        
+        # Verify the result is the augmented query, not the original
+        self.assertNotEqual(result, "truck transportation")
+        self.assertIn("freight", result.lower())
+        self.assertIn("diesel", result.lower())
+        self.assertIn("transport", result.lower())
+        self.assertIn("32 tonnes", result.lower())
+        
+        # Verify the LLM was called with proper context
+        call_args = mock_chat_completion.call_args
+        messages = call_args[0][1]  # Second argument is messages
+        user_content = messages[1]["content"]
+        
+        self.assertIn("Primary Query: truck transportation", user_content)
+        self.assertIn("Secondary Query: diesel fuel, over 32 tonnes", user_content)
+        self.assertIn("Enhanced Category: SERVICE_TRANSPORT_ROAD_FREIGHT", user_content)
+        self.assertIn("Extracted Modifiers: diesel, >32t, long-haul", user_content)
+        self.assertIn("ISIC Classification Codes: 4923", user_content)
+        
+        logger.info("✅ Phase 1.4 direct test passed!")
+
+    def test_phase4_integration_flow(self):
+        """Test the complete Phase 1.1-1.4 flow to show the transformation"""
+        logger.info("Testing complete Phase 1.1-1.4 integration flow")
+        
+        # Simulate the complete flow
+        original_query = "truck transportation"
+        secondary_query = "diesel fuel, over 32 tonnes"
+        
+        logger.info(f"📥 INPUT:")
+        logger.info(f"   Primary Query: '{original_query}'")
+        logger.info(f"   Secondary Query: '{secondary_query}'")
+        
+        # Phase 1.1 output (would come from predict_enhanced_input_category)
+        enhanced_category = "SERVICE_TRANSPORT_ROAD_FREIGHT"
+        logger.info(f"🔄 Phase 1.1 Output: '{enhanced_category}'")
+        
+        # Phase 1.2 output (would come from spot_modifiers)
+        modifiers = ["diesel", ">32t", "long-haul"]
+        logger.info(f"🔄 Phase 1.2 Output: {modifiers}")
+        
+        # Phase 1.3 output (would come from map_isic_classification)
+        isic_codes = ["4923"]
+        logger.info(f"🔄 Phase 1.3 Output: {isic_codes}")
+        
+        # Phase 1.4 - this is what we're testing
+        with patch('emissions_factor_matching.predictions.get_chat_completion') as mock_chat:
+            mock_chat.return_value = "Heavy duty diesel freight transportation services using vehicles exceeding 32 tonnes gross weight for long-distance cargo delivery. Road freight transport operations involving compression ignition diesel engines in commercial trucking fleets."
+            
+            request = TestRequest(
+                user_query_primary=original_query,
+                user_query_secondary=secondary_query
+            )
+            
+            augmented_query = augment_query_text(request, enhanced_category, modifiers, isic_codes)
+            
+            logger.info(f"🔄 Phase 1.4 Output: '{augmented_query}'")
+        
+        # Verify transformation
+        self.assertNotEqual(augmented_query, original_query)
+        self.assertTrue(len(augmented_query) > len(original_query))
+        
+        logger.info(f"📊 TRANSFORMATION SUMMARY:")
+        logger.info(f"   Original length: {len(original_query)} chars")
+        logger.info(f"   Augmented length: {len(augmented_query)} chars")
+        logger.info(f"   Enhancement ratio: {len(augmented_query) / len(original_query):.1f}x")
+        
+        # This augmented query would then be used for ChromaDB vector search
+        logger.info(f"📤 OUTPUT FOR CHROMADB:")
+        logger.info(f"   Query Text: '{augmented_query}'")
+        
+        logger.info("✅ Complete integration flow test passed!")
+
+    def test_phase4_vs_legacy_comparison(self):
+        """Compare Phase 1.4 output vs legacy query construction"""
+        logger.info("Comparing Phase 1.4 vs Legacy query construction")
+        
+        # Original input
+        original_query = "concrete"
+        secondary_query = "high strength, construction grade"
+        
+        # Legacy approach (simplified)
+        legacy_query = f"{original_query} {secondary_query}"
+        
+        # Phase 1.4 approach
+        with patch('emissions_factor_matching.predictions.get_chat_completion') as mock_chat:
+            mock_chat.return_value = "High strength concrete production for construction applications involving cement manufacturing, aggregate mixing, and quality control processes. Construction material manufacturing with enhanced durability characteristics for infrastructure and building projects."
+            
+            request = TestRequest(
+                user_query_primary=original_query,
+                user_query_secondary=secondary_query
+            )
+            
+            enhanced_category = "PRODUCT_CONSTRUCTION_MATERIAL"
+            modifiers = ["high strength", "construction grade"]
+            isic_codes = ["2394"]
+            
+            augmented_query = augment_query_text(request, enhanced_category, modifiers, isic_codes)
+        
+        logger.info(f"🔄 COMPARISON:")
+        logger.info(f"   Legacy Query: '{legacy_query}'")
+        logger.info(f"   Phase 1.4 Query: '{augmented_query}'")
+        
+        # Verify Phase 1.4 is more descriptive
+        self.assertTrue(len(augmented_query) > len(legacy_query))
+        self.assertIn("concrete", augmented_query.lower())
+        self.assertIn("construction", augmented_query.lower())
+        self.assertIn("manufacturing", augmented_query.lower())
+        
+        logger.info("✅ Phase 1.4 vs Legacy comparison passed!")
+
+
+if __name__ == '__main__':
+    unittest.main()
diff --git a/emissions_factor_matching/tests/test_phase5_dynamic_filters.py b/emissions_factor_matching/tests/test_phase5_dynamic_filters.py
new file mode 100644
index 0000000..c530578
--- /dev/null
+++ b/emissions_factor_matching/tests/test_phase5_dynamic_filters.py
@@ -0,0 +1,297 @@
+import unittest
+from unittest.mock import patch
+from pydantic import BaseModel
+import logging
+
+# Set up logging to see what's happening
+logging.basicConfig(level=logging.INFO)
+logger = logging.getLogger(__name__)
+
+from emissions_factor_matching.predictions import construct_dynamic_filters
+
+
+class TestRequest(BaseModel):
+    user_query_primary: str
+    user_query_secondary: str | None = None
+    valid_units: list | None = None
+    lcia_method: str | None = None
+    carbon_only: bool | None = None
+
+
+class TestPhase5DynamicFilters(unittest.TestCase):
+    """Test Phase 1.5: Dynamic Filter Construction"""
+
+    def test_basic_filter_construction(self):
+        """Test basic filter construction with ISIC codes"""
+        logger.info("Testing basic dynamic filter construction")
+        
+        request = TestRequest(user_query_primary="truck transportation")
+        
+        enhanced_category = "SERVICE_TRANSPORT_ROAD_FREIGHT"
+        modifiers = ["diesel", ">32t", "long-haul"]
+        isic_codes = ["4923"]
+        
+        filters = construct_dynamic_filters(request, enhanced_category, modifiers, isic_codes)
+        
+        logger.info(f"Generated filters: {filters}")
+        
+        # Verify basic structure
+        self.assertIn("$and", filters)
+        self.assertIsInstance(filters["$and"], list)
+        
+        # Verify activity_type filter is always present
+        activity_type_filter = {"activity_type": {"$eq": "ordinary transforming activity"}}
+        self.assertIn(activity_type_filter, filters["$and"])
+        
+        # Verify ISIC filter is present
+        isic_filter = {"isic_4_code": {"$eq": "4923"}}
+        self.assertIn(isic_filter, filters["$and"])
+        
+        logger.info("✅ Basic filter construction test passed!")
+
+    def test_multiple_isic_codes(self):
+        """Test filter construction with multiple ISIC codes"""
+        logger.info("Testing multiple ISIC codes filter construction")
+        
+        request = TestRequest(user_query_primary="chemical production")
+        
+        enhanced_category = "CHEMICAL_ORGANIC"
+        modifiers = ["industrial grade"]
+        isic_codes = ["2011", "2012", "2013"]  # Multiple codes
+        
+        filters = construct_dynamic_filters(request, enhanced_category, modifiers, isic_codes)
+        
+        logger.info(f"Generated filters: {filters}")
+        
+        # Verify ISIC filter uses $in for multiple codes
+        isic_filter = {"isic_4_code": {"$in": ["2011", "2012", "2013"]}}
+        self.assertIn(isic_filter, filters["$and"])
+        
+        logger.info("✅ Multiple ISIC codes test passed!")
+
+    def test_no_isic_codes(self):
+        """Test filter construction when no ISIC codes are provided"""
+        logger.info("Testing filter construction without ISIC codes")
+        
+        request = TestRequest(user_query_primary="general query")
+        
+        enhanced_category = "PRODUCT_OTHER"
+        modifiers = []
+        isic_codes = []  # No ISIC codes
+        
+        filters = construct_dynamic_filters(request, enhanced_category, modifiers, isic_codes)
+        
+        logger.info(f"Generated filters: {filters}")
+        
+        # Should only have activity_type filter
+        expected_filter = {"activity_type": {"$eq": "ordinary transforming activity"}}
+        self.assertEqual(filters, expected_filter)
+        
+        logger.info("✅ No ISIC codes test passed!")
+
+    def test_user_specified_filters(self):
+        """Test that user-specified filters are included"""
+        logger.info("Testing user-specified filters inclusion")
+        
+        request = TestRequest(
+            user_query_primary="electricity",
+            valid_units=["kWh", "MJ"],
+            lcia_method="climate_change_gwp100",
+            carbon_only=False
+        )
+        
+        enhanced_category = "SERVICE_ENERGY_ELECTRICITY"
+        modifiers = ["renewable"]
+        isic_codes = ["3510"]
+        
+        filters = construct_dynamic_filters(request, enhanced_category, modifiers, isic_codes)
+        
+        logger.info(f"Generated filters: {filters}")
+        
+        # Verify user-specified filters are included
+        unit_filter = {"unit": {"$in": ["kWh", "MJ"]}}
+        lcia_filter = {"climate_change_gwp100": {"$eq": True}}
+        
+        self.assertIn(unit_filter, filters["$and"])
+        self.assertIn(lcia_filter, filters["$and"])
+        
+        logger.info("✅ User-specified filters test passed!")
+
+    def test_carbon_only_filter_exclusion(self):
+        """Test that LCIA method filter is excluded when carbon_only=True"""
+        logger.info("Testing carbon_only filter exclusion")
+        
+        request = TestRequest(
+            user_query_primary="electricity",
+            lcia_method="climate_change_gwp100",
+            carbon_only=True  # Should exclude LCIA method filter
+        )
+        
+        enhanced_category = "SERVICE_ENERGY_ELECTRICITY"
+        modifiers = []
+        isic_codes = ["3510"]
+        
+        filters = construct_dynamic_filters(request, enhanced_category, modifiers, isic_codes)
+        
+        logger.info(f"Generated filters: {filters}")
+        
+        # Verify LCIA method filter is NOT included
+        lcia_filter = {"climate_change_gwp100": {"$eq": True}}
+        self.assertNotIn(lcia_filter, filters["$and"])
+        
+        logger.info("✅ Carbon only filter exclusion test passed!")
+
+    def test_transport_category_filters(self):
+        """Test filter construction for transport categories"""
+        logger.info("Testing transport category filter construction")
+        
+        request = TestRequest(user_query_primary="freight transport")
+        
+        enhanced_category = "SERVICE_TRANSPORT_ROAD_FREIGHT"
+        modifiers = ["diesel", ">32t", "heavy-duty"]
+        isic_codes = ["4923"]
+        
+        filters = construct_dynamic_filters(request, enhanced_category, modifiers, isic_codes)
+        
+        logger.info(f"Generated filters for transport: {filters}")
+        
+        # Verify basic filters are present
+        self.assertIn("$and", filters)
+        
+        # Should have activity_type and ISIC filters at minimum
+        activity_type_filter = {"activity_type": {"$eq": "ordinary transforming activity"}}
+        isic_filter = {"isic_4_code": {"$eq": "4923"}}
+        
+        self.assertIn(activity_type_filter, filters["$and"])
+        self.assertIn(isic_filter, filters["$and"])
+        
+        logger.info("✅ Transport category filters test passed!")
+
+    def test_chemical_category_filters(self):
+        """Test filter construction for chemical categories"""
+        logger.info("Testing chemical category filter construction")
+        
+        request = TestRequest(user_query_primary="ethanol production")
+        
+        enhanced_category = "CHEMICAL_ORGANIC_SOLVENT"
+        modifiers = ["high purity", "industrial grade"]
+        isic_codes = ["2011"]
+        
+        filters = construct_dynamic_filters(request, enhanced_category, modifiers, isic_codes)
+        
+        logger.info(f"Generated filters for chemical: {filters}")
+        
+        # Verify basic filters are present
+        self.assertIn("$and", filters)
+        
+        activity_type_filter = {"activity_type": {"$eq": "ordinary transforming activity"}}
+        isic_filter = {"isic_4_code": {"$eq": "2011"}}
+        
+        self.assertIn(activity_type_filter, filters["$and"])
+        self.assertIn(isic_filter, filters["$and"])
+        
+        logger.info("✅ Chemical category filters test passed!")
+
+    def test_service_category_filters(self):
+        """Test filter construction for service categories"""
+        logger.info("Testing service category filter construction")
+        
+        request = TestRequest(user_query_primary="electricity generation")
+        
+        enhanced_category = "SERVICE_ENERGY_ELECTRICITY"
+        modifiers = ["renewable", "grid mix"]
+        isic_codes = ["3510"]
+        
+        filters = construct_dynamic_filters(request, enhanced_category, modifiers, isic_codes)
+        
+        logger.info(f"Generated filters for service: {filters}")
+        
+        # Verify basic filters are present
+        self.assertIn("$and", filters)
+        
+        activity_type_filter = {"activity_type": {"$eq": "ordinary transforming activity"}}
+        isic_filter = {"isic_4_code": {"$eq": "3510"}}
+        
+        self.assertIn(activity_type_filter, filters["$and"])
+        self.assertIn(isic_filter, filters["$and"])
+        
+        logger.info("✅ Service category filters test passed!")
+
+    def test_error_handling_fallback(self):
+        """Test error handling and fallback behavior"""
+        logger.info("Testing error handling and fallback")
+        
+        # Create a request that might cause issues
+        request = TestRequest(user_query_primary="test")
+        
+        # Simulate error by passing invalid data
+        with patch('emissions_factor_matching.predictions.logger') as mock_logger:
+            # This should not raise an exception, but use fallback
+            filters = construct_dynamic_filters(request, None, None, None)
+            
+            logger.info(f"Fallback filters: {filters}")
+            
+            # Should return basic fallback filter
+            expected_fallback = {"activity_type": {"$eq": "ordinary transforming activity"}}
+            self.assertEqual(filters, expected_fallback)
+        
+        logger.info("✅ Error handling test passed!")
+
+    def test_filter_optimization(self):
+        """Test that filters are optimized (single filter vs $and)"""
+        logger.info("Testing filter optimization")
+        
+        # Test single filter case
+        request = TestRequest(user_query_primary="simple query")
+        
+        enhanced_category = "PRODUCT_OTHER"
+        modifiers = []
+        isic_codes = []  # No additional filters
+        
+        filters = construct_dynamic_filters(request, enhanced_category, modifiers, isic_codes)
+        
+        # Should be a single filter, not wrapped in $and
+        expected_single_filter = {"activity_type": {"$eq": "ordinary transforming activity"}}
+        self.assertEqual(filters, expected_single_filter)
+        self.assertNotIn("$and", filters)
+        
+        logger.info("✅ Filter optimization test passed!")
+
+    def test_comprehensive_filter_combination(self):
+        """Test comprehensive filter combination with all components"""
+        logger.info("Testing comprehensive filter combination")
+        
+        request = TestRequest(
+            user_query_primary="heavy truck transport",
+            user_query_secondary="diesel, long haul",
+            valid_units=["tkm"],
+            lcia_method="climate_change_gwp100",
+            carbon_only=False
+        )
+        
+        enhanced_category = "SERVICE_TRANSPORT_ROAD_FREIGHT"
+        modifiers = ["diesel", ">32t", "long-haul", "heavy-duty"]
+        isic_codes = ["4923", "4924"]  # Multiple ISIC codes
+        
+        filters = construct_dynamic_filters(request, enhanced_category, modifiers, isic_codes)
+        
+        logger.info(f"Comprehensive filters: {filters}")
+        
+        # Verify all expected filters are present
+        self.assertIn("$and", filters)
+        
+        expected_filters = [
+            {"activity_type": {"$eq": "ordinary transforming activity"}},
+            {"isic_4_code": {"$in": ["4923", "4924"]}},
+            {"unit": {"$in": ["tkm"]}},
+            {"climate_change_gwp100": {"$eq": True}}
+        ]
+        
+        for expected_filter in expected_filters:
+            self.assertIn(expected_filter, filters["$and"])
+        
+        logger.info("✅ Comprehensive filter combination test passed!")
+
+
+if __name__ == '__main__':
+    unittest.main()
diff --git a/emissions_factor_matching/tests/test_phase6_vector_search.py b/emissions_factor_matching/tests/test_phase6_vector_search.py
new file mode 100644
index 0000000..cad9d49
--- /dev/null
+++ b/emissions_factor_matching/tests/test_phase6_vector_search.py
@@ -0,0 +1,280 @@
+import unittest
+from unittest.mock import patch, MagicMock
+from pydantic import BaseModel
+import logging
+
+# Set up logging to see what's happening
+logging.basicConfig(level=logging.INFO)
+logger = logging.getLogger(__name__)
+
+from emissions_factor_matching.dataset import search_candidates, search_candidates_with_fallback
+from emissions_factor_matching.model import Candidate
+
+
+class TestRequest(BaseModel):
+    user_query_primary: str
+    user_query_secondary: str | None = None
+
+
+class TestPhase6VectorSearch(unittest.TestCase):
+    """Test Phase 1.6: ChromaDB Vector Search Enhancement"""
+
+    def setUp(self):
+        """Set up test fixtures"""
+        # Mock ChromaDB response structure
+        self.mock_chroma_response = {
+            'ids': [['ef-12345', 'ef-23456', 'ef-34567']],
+            'documents': [['truck transport, freight', 'road transport, heavy duty', 'diesel vehicle operation']],
+            'metadatas': [[
+                {
+                    'uuid': 'ef-12345',
+                    'reference_product_name': 'transport, freight, lorry >32 metric ton',
+                    'product_information': 'Heavy-duty freight transport',
+                    'source': 'Ecoinvent 3.11',
+                    'isic_4_code': '4923',
+                    'isic_section': 'H - Transportation and storage',
+                    'activity_type': 'ordinary transforming activity',
+                    'geography': 'GLO',
+                    'unit': 'tkm'
+                },
+                {
+                    'uuid': 'ef-23456',
+                    'reference_product_name': 'transport, freight, lorry 16-32 metric ton',
+                    'product_information': 'Medium-duty freight transport',
+                    'source': 'Ecoinvent 3.11',
+                    'isic_4_code': '4923',
+                    'isic_section': 'H - Transportation and storage',
+                    'activity_type': 'ordinary transforming activity',
+                    'geography': 'GLO',
+                    'unit': 'tkm'
+                },
+                {
+                    'uuid': 'ef-34567',
+                    'reference_product_name': 'transport, freight, lorry 7.5-16 metric ton',
+                    'product_information': 'Light-duty freight transport',
+                    'source': 'Ecoinvent 3.11',
+                    'isic_4_code': '4923',
+                    'isic_section': 'H - Transportation and storage',
+                    'activity_type': 'ordinary transforming activity',
+                    'geography': 'GLO',
+                    'unit': 'tkm'
+                }
+            ]],
+            'distances': [[0.123, 0.234, 0.345]]
+        }
+
+    @patch('emissions_factor_matching.dataset.collection')
+    def test_basic_vector_search(self, mock_collection):
+        """Test basic vector search functionality"""
+        logger.info("Testing basic vector search")
+        
+        # Mock ChromaDB collection query
+        mock_collection.query.return_value = self.mock_chroma_response
+        
+        augmented_query = "Heavy-duty diesel freight transport truck >32 tonnes long-haul road transportation logistics"
+        filters = {
+            "$and": [
+                {"activity_type": {"$eq": "ordinary transforming activity"}},
+                {"isic_4_code": {"$eq": "4923"}}
+            ]
+        }
+        
+        candidates = search_candidates(augmented_query, filters, n_results=25)
+        
+        # Verify ChromaDB was called correctly
+        mock_collection.query.assert_called_once_with(
+            query_texts=[augmented_query],
+            where=filters,
+            n_results=25,
+            include=['metadatas', 'documents', 'distances']
+        )
+        
+        # Verify results
+        self.assertEqual(len(candidates), 3)
+        self.assertIsInstance(candidates[0], Candidate)
+        
+        # Verify first candidate details
+        first_candidate = candidates[0]
+        self.assertEqual(first_candidate.activity_uuid, 'ef-12345')
+        self.assertEqual(first_candidate.chroma_id, 'ef-12345')
+        self.assertEqual(first_candidate.activity_name, 'truck transport, freight')
+        self.assertEqual(first_candidate.source, 'Ecoinvent 3.11')
+        self.assertEqual(first_candidate.isic_4_code, '4923')
+        self.assertEqual(first_candidate.distance, 0.123)
+        self.assertAlmostEqual(first_candidate.similarity_score, 0.877, places=3)
+        
+        logger.info("✅ Basic vector search test passed!")
+
+    @patch('emissions_factor_matching.dataset.collection')
+    def test_empty_results_handling(self, mock_collection):
+        """Test handling of empty search results"""
+        logger.info("Testing empty results handling")
+        
+        # Mock empty ChromaDB response
+        mock_collection.query.return_value = {
+            'ids': [[]],
+            'documents': [[]],
+            'metadatas': [[]],
+            'distances': [[]]
+        }
+        
+        augmented_query = "non-existent activity"
+        filters = {"activity_type": {"$eq": "ordinary transforming activity"}}
+        
+        candidates = search_candidates(augmented_query, filters)
+        
+        # Should return empty list
+        self.assertEqual(len(candidates), 0)
+        self.assertIsInstance(candidates, list)
+        
+        logger.info("✅ Empty results handling test passed!")
+
+    @patch('emissions_factor_matching.dataset.collection')
+    def test_error_handling(self, mock_collection):
+        """Test error handling during search"""
+        logger.info("Testing error handling")
+        
+        # Mock ChromaDB exception
+        mock_collection.query.side_effect = Exception("ChromaDB connection error")
+        
+        augmented_query = "test query"
+        filters = {"activity_type": {"$eq": "ordinary transforming activity"}}
+        
+        candidates = search_candidates(augmented_query, filters)
+        
+        # Should return empty list on error
+        self.assertEqual(len(candidates), 0)
+        
+        logger.info("✅ Error handling test passed!")
+
+    @patch('emissions_factor_matching.dataset.collection')
+    def test_candidate_sorting(self, mock_collection):
+        """Test that candidates are sorted by distance (similarity)"""
+        logger.info("Testing candidate sorting")
+        
+        # Mock response with unsorted distances
+        unsorted_response = {
+            'ids': [['ef-1', 'ef-2', 'ef-3']],
+            'documents': [['activity 1', 'activity 2', 'activity 3']],
+            'metadatas': [[
+                {'uuid': 'ef-1', 'source': 'Test', 'activity_type': 'ordinary transforming activity'},
+                {'uuid': 'ef-2', 'source': 'Test', 'activity_type': 'ordinary transforming activity'},
+                {'uuid': 'ef-3', 'source': 'Test', 'activity_type': 'ordinary transforming activity'}
+            ]],
+            'distances': [[0.5, 0.2, 0.8]]  # Unsorted: middle one is best
+        }
+        
+        mock_collection.query.return_value = unsorted_response
+        
+        candidates = search_candidates("test query", {})
+        
+        # Verify sorting (lowest distance first)
+        self.assertEqual(len(candidates), 3)
+        self.assertEqual(candidates[0].distance, 0.2)  # Best match
+        self.assertEqual(candidates[1].distance, 0.5)  # Second best
+        self.assertEqual(candidates[2].distance, 0.8)  # Worst match
+        
+        # Verify corresponding similarity scores
+        self.assertAlmostEqual(candidates[0].similarity_score, 0.8, places=3)
+        self.assertAlmostEqual(candidates[1].similarity_score, 0.5, places=3)
+        self.assertAlmostEqual(candidates[2].similarity_score, 0.2, places=3)
+        
+        logger.info("✅ Candidate sorting test passed!")
+
+    @patch('emissions_factor_matching.dataset.search_candidates')
+    def test_fallback_search_strategy(self, mock_search):
+        """Test fallback search strategy"""
+        logger.info("Testing fallback search strategy")
+        
+        # Mock search_candidates to return empty list first, then results
+        mock_search.side_effect = [
+            [],  # First call (primary search) returns empty
+            [Candidate(
+                activity_uuid='ef-fallback',
+                chroma_id='ef-fallback',
+                activity_name='fallback activity',
+                reference_product_name='fallback product',
+                source='Test',
+                activity_type='ordinary transforming activity',
+                distance=0.5
+            )]  # Second call (fallback search) returns results
+        ]
+        
+        augmented_query = "test query"
+        complex_filters = {
+            "$and": [
+                {"activity_type": {"$eq": "ordinary transforming activity"}},
+                {"isic_4_code": {"$eq": "9999"}}  # Non-existent ISIC code
+            ]
+        }
+        
+        candidates = search_candidates_with_fallback(augmented_query, complex_filters)
+        
+        # Verify fallback was triggered
+        self.assertEqual(len(candidates), 1)
+        self.assertEqual(candidates[0].activity_uuid, 'ef-fallback')
+        
+        # Verify search_candidates was called twice (primary + fallback)
+        self.assertEqual(mock_search.call_count, 2)
+        
+        # Verify first call used complex filters
+        first_call_args = mock_search.call_args_list[0]
+        self.assertEqual(first_call_args[0][1], complex_filters)
+        
+        # Verify second call used basic filters
+        second_call_args = mock_search.call_args_list[1]
+        expected_basic_filters = {"activity_type": {"$eq": "ordinary transforming activity"}}
+        self.assertEqual(second_call_args[0][1], expected_basic_filters)
+        
+        logger.info("✅ Fallback search strategy test passed!")
+
+    @patch('emissions_factor_matching.dataset.collection')
+    def test_metadata_extraction(self, mock_collection):
+        """Test comprehensive metadata extraction"""
+        logger.info("Testing metadata extraction")
+        
+        # Mock response with rich metadata
+        rich_metadata_response = {
+            'ids': [['ef-rich']],
+            'documents': [['comprehensive activity']],
+            'metadatas': [[{
+                'uuid': 'ef-rich',
+                'reference_product_name': 'comprehensive product',
+                'product_information': 'Detailed product information',
+                'source': 'Ecoinvent 3.11',
+                'isic_4_code': '2011',
+                'isic_section': 'C - Manufacturing',
+                'activity_type': 'ordinary transforming activity',
+                'geography': 'US',
+                'unit': 'kg',
+                'custom_field': 'custom_value',
+                'another_field': 123
+            }]],
+            'distances': [[0.1]]
+        }
+        
+        mock_collection.query.return_value = rich_metadata_response
+        
+        candidates = search_candidates("test", {})
+        
+        candidate = candidates[0]
+        
+        # Verify all metadata fields are properly extracted
+        self.assertEqual(candidate.activity_uuid, 'ef-rich')
+        self.assertEqual(candidate.reference_product_name, 'comprehensive product')
+        self.assertEqual(candidate.product_information, 'Detailed product information')
+        self.assertEqual(candidate.source, 'Ecoinvent 3.11')
+        self.assertEqual(candidate.isic_4_code, '2011')
+        self.assertEqual(candidate.isic_section, 'C - Manufacturing')
+        self.assertEqual(candidate.geography, 'US')
+        self.assertEqual(candidate.unit, 'kg')
+        
+        # Verify custom metadata is preserved
+        self.assertEqual(candidate.metadata['custom_field'], 'custom_value')
+        self.assertEqual(candidate.metadata['another_field'], 123)
+        
+        logger.info("✅ Metadata extraction test passed!")
+
+
+if __name__ == '__main__':
+    unittest.main()
diff --git a/emissions_factor_matching/tests/test_phase_7_reranking.py b/emissions_factor_matching/tests/test_phase_7_reranking.py
new file mode 100644
index 0000000..609b57d
--- /dev/null
+++ b/emissions_factor_matching/tests/test_phase_7_reranking.py
@@ -0,0 +1,208 @@
+import unittest
+from unittest.mock import patch, MagicMock
+from emissions_factor_matching.predictions import re_rank_candidates
+from emissions_factor_matching.model import Candidate, MatchedEF
+
+
+class TestPhase7Reranking(unittest.TestCase):
+    """Test Phase 1.7: LLM Re-ranking & Justification"""
+
+    def setUp(self):
+        """Set up test fixtures"""
+        # Create mock request model
+        self.mock_request = MagicMock()
+        self.mock_request.user_query_primary = "truck transportation for heavy cargo"
+        self.mock_request.user_query_secondary = None
+        self.mock_request.product_category_context = "transportation"
+        self.mock_request.lca_lifecycle_stage = "use"
+        self.mock_request.iso_code = "US"
+
+        # Create mock candidates
+        self.mock_candidates = [
+            Candidate(
+                activity_uuid="ef-12345",
+                chroma_id="chroma-1",
+                activity_name="transport, freight, lorry >32 metric ton",
+                reference_product_name="transport, freight, lorry >32 metric ton",
+                product_information="Heavy duty freight transport",
+                source="Ecoinvent",
+                isic_4_code="4923",
+                isic_section="H",
+                activity_type="ordinary transforming activity",
+                geography="GLO",
+                unit="tkm",
+                distance=0.154,
+                similarity_score=0.846,
+                metadata={}
+            ),
+            Candidate(
+                activity_uuid="ef-23456",
+                chroma_id="chroma-2",
+                activity_name="transport, freight, lorry 16-32 metric ton",
+                reference_product_name="transport, freight, lorry 16-32 metric ton",
+                product_information="Medium duty freight transport",
+                source="Ecoinvent",
+                isic_4_code="4923",
+                isic_section="H",
+                activity_type="ordinary transforming activity",
+                geography="GLO",
+                unit="tkm",
+                distance=0.198,
+                similarity_score=0.802,
+                metadata={}
+            ),
+            Candidate(
+                activity_uuid="ef-34567",
+                chroma_id="chroma-3",
+                activity_name="transport, freight, lorry 7.5-16 metric ton",
+                reference_product_name="transport, freight, lorry 7.5-16 metric ton",
+                product_information="Light duty freight transport",
+                source="Ecoinvent",
+                isic_4_code="4923",
+                isic_section="H",
+                activity_type="ordinary transforming activity",
+                geography="GLO",
+                unit="tkm",
+                distance=0.245,
+                similarity_score=0.755,
+                metadata={}
+            )
+        ]
+
+        # Test parameters
+        self.augmented_query = "Heavy duty diesel freight transportation services using vehicles exceeding 32 tonnes"
+        self.enhanced_category = "SERVICE_TRANSPORT_ROAD_FREIGHT"
+        self.modifiers = ["diesel", ">32t", "heavy cargo"]
+        self.isic_codes = ["4923"]
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    def test_successful_reranking(self, mock_llm):
+        """Test successful LLM re-ranking with valid response"""
+        # Mock LLM response
+        mock_llm_response = """{
+            "selected_candidate_uuid": "ef-12345",
+            "confidence": "HIGH",
+            "confidence_score": 0.92,
+            "explanation": "This activity directly matches the user's request for heavy cargo truck transportation. The >32 metric ton specification aligns perfectly with the heavy cargo requirement, and the diesel fuel type is the standard for this vehicle class. The activity represents the exact transportation service requested with appropriate geographic scope.",
+            "ranking_rationale": "Ranked based on vehicle weight class match (>32t), fuel type alignment (diesel), and service type precision (freight transport)",
+            "alternative_considerations": "Other candidates represent smaller vehicle classes (16-32t, 7.5-16t) which would be less suitable for heavy cargo applications"
+        }"""
+        
+        mock_llm.return_value = mock_llm_response
+
+        # Execute re-ranking
+        result = re_rank_candidates(
+            self.mock_request,
+            self.mock_candidates,
+            self.augmented_query,
+            self.enhanced_category,
+            self.modifiers,
+            self.isic_codes
+        )
+
+        # Verify result type and structure
+        self.assertIsInstance(result, MatchedEF)
+        self.assertEqual(result.activity_uuid, "ef-12345")
+        self.assertEqual(result.activity_name, "transport, freight, lorry >32 metric ton")
+        self.assertEqual(result.confidence, "HIGH")
+        self.assertEqual(result.confidence_score, 0.92)
+        self.assertIn("heavy cargo truck transportation", result.explanation)
+        self.assertEqual(result.final_rank, 1)
+        self.assertEqual(result.original_distance, 0.154)
+
+        # Verify LLM was called correctly
+        mock_llm.assert_called_once()
+        call_args = mock_llm.call_args
+        self.assertEqual(len(call_args[0][1]), 2)  # Two messages: system and user
+        self.assertIn("truck transportation for heavy cargo", call_args[0][1][1]["content"])
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    def test_llm_failure_fallback(self, mock_llm):
+        """Test fallback behavior when LLM fails"""
+        # Mock LLM failure
+        mock_llm.return_value = None
+
+        # Execute re-ranking
+        result = re_rank_candidates(
+            self.mock_request,
+            self.mock_candidates,
+            self.augmented_query,
+            self.enhanced_category,
+            self.modifiers,
+            self.isic_codes
+        )
+
+        # Verify fallback to top candidate
+        self.assertIsInstance(result, MatchedEF)
+        self.assertEqual(result.activity_uuid, "ef-12345")  # First candidate
+        self.assertEqual(result.confidence, "MEDIUM")  # Fallback confidence
+        self.assertEqual(result.confidence_score, 0.846)  # Vector similarity score
+        self.assertIn("LLM re-ranking failed", result.explanation)
+        self.assertEqual(result.final_rank, 1)
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    def test_invalid_llm_response_fallback(self, mock_llm):
+        """Test fallback when LLM returns invalid JSON"""
+        # Mock invalid LLM response
+        mock_llm.return_value = "This is not valid JSON"
+
+        # Execute re-ranking
+        result = re_rank_candidates(
+            self.mock_request,
+            self.mock_candidates,
+            self.augmented_query,
+            self.enhanced_category,
+            self.modifiers,
+            self.isic_codes
+        )
+
+        # Verify fallback behavior
+        self.assertIsInstance(result, MatchedEF)
+        self.assertEqual(result.activity_uuid, "ef-12345")
+        self.assertEqual(result.confidence, "MEDIUM")
+        self.assertIn("LLM re-ranking failed", result.explanation)
+
+    def test_empty_candidates_error(self):
+        """Test error handling for empty candidate list"""
+        with self.assertRaises(ValueError) as context:
+            re_rank_candidates(
+                self.mock_request,
+                [],  # Empty candidates
+                self.augmented_query,
+                self.enhanced_category,
+                self.modifiers,
+                self.isic_codes
+            )
+        
+        self.assertIn("Cannot re-rank empty candidate list", str(context.exception))
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    def test_confidence_validation(self, mock_llm):
+        """Test confidence level and score validation"""
+        # Mock LLM response with invalid confidence
+        mock_llm_response = """{
+            "selected_candidate_uuid": "ef-12345",
+            "confidence": "INVALID",
+            "confidence_score": 1.5,
+            "explanation": "Test explanation"
+        }"""
+        
+        mock_llm.return_value = mock_llm_response
+
+        # Execute re-ranking
+        result = re_rank_candidates(
+            self.mock_request,
+            self.mock_candidates,
+            self.augmented_query,
+            self.enhanced_category,
+            self.modifiers,
+            self.isic_codes
+        )
+
+        # Verify confidence validation
+        self.assertEqual(result.confidence, "MEDIUM")  # Should default to MEDIUM
+        self.assertEqual(result.confidence_score, 1.0)  # Should be clamped to 1.0
+
+
+if __name__ == '__main__':
+    unittest.main()
diff --git a/test_isic_fix.py b/test_isic_fix.py
new file mode 100644
index 0000000..5dbe9fe
--- /dev/null
+++ b/test_isic_fix.py
@@ -0,0 +1,53 @@
+#!/usr/bin/env python3
+"""
+Test the ISIC code fix
+"""
+import sys
+sys.path.append('/home/<USER>/app')
+
+from emissions_factor_matching.predictions import map_isic_classification
+from pydantic import BaseModel
+
+class TestRequest(BaseModel):
+    user_query_primary: str
+    user_query_secondary: str | None = None
+
+def test_isic_fix():
+    print("=== TESTING ISIC CODE FIX ===\n")
+    
+    # Test transport category
+    print("1. Testing transport category:")
+    enhanced_category = "SERVICE_TRANSPORT_ROAD_FREIGHT"
+    modifiers = ['diesel', '>32t', 'long-haul', 'heavy-duty']
+    
+    print(f"   Category: {enhanced_category}")
+    print(f"   Modifiers: {modifiers}")
+    
+    isic_codes = map_isic_classification(enhanced_category, modifiers)
+    print(f"   Result: {isic_codes}")
+    
+    if "4923" in isic_codes:
+        print("   ✅ SUCCESS: Found ISIC code 4923!")
+    else:
+        print("   ❌ FAILED: ISIC code 4923 not found")
+    
+    print()
+    
+    # Test chemical category
+    print("2. Testing chemical category:")
+    enhanced_category = "CHEMICAL_ORGANIC"
+    modifiers = ['industrial grade']
+    
+    print(f"   Category: {enhanced_category}")
+    print(f"   Modifiers: {modifiers}")
+    
+    isic_codes = map_isic_classification(enhanced_category, modifiers)
+    print(f"   Result: {isic_codes}")
+    
+    if len(isic_codes) > 0:
+        print(f"   ✅ SUCCESS: Found ISIC codes: {isic_codes}")
+    else:
+        print("   ❌ FAILED: No ISIC codes found")
+
+if __name__ == "__main__":
+    test_isic_fix()
