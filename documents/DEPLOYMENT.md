Azure OpenAI Deployments Analysis
Current Setup

1. GPT-4 Turbo (4o)
   Naming Convention Note:

Standardized naming convention: gpt-4o (hyphen format)
Both production and staging now use the same hyphen format for consistency
✅ README.md has been updated to use the standardized format (gpt-4o)

Staging Environment

Full URL: https://gpt-4o-west3.openai.azure.com/openai/deployments/gpt-4o/chat/completions?api-version=2024-08-01-preview
Base Endpoint: https://gpt-4o-west3.openai.azure.com/
Deployment Name: gpt-4o
API Version: 2024-08-01-preview
Environment Variables:

AZURE_OPENAI_ENDPOINT_US_EAST="https://gpt-4o-west3.openai.azure.com/"
AZURE_OPENAI_DEPLOYMENT="gpt-4o"

Production Environment

Base Endpoint: https://lca-openai-east2-instance.openai.azure.com/
Deployment Name: gpt-4o
Environment Variables:

AZURE_OPENAI_ENDPOINT_US_EAST="https://lca-openai-east2-instance.openai.azure.com/"
AZURE_OPENAI_DEPLOYMENT="gpt-4o"

✅ Using separate endpoint from staging

2. GPT-3.5 Turbo (Deprecated)
   ✅ GPT-3.5 Turbo has been phased out and replaced with GPT-4o
   ✅ All functions previously using GPT-3.5 have been migrated to use GPT-4o
   ✅ Environment variables for GPT-3.5 have been maintained for backward compatibility but are no longer actively used

3. Phi-3
   Naming Convention Note:

   Standardized naming convention: phi-3 (hyphen format)
   Both production and staging now use the same hyphen format for consistency

   Staging Environment

Endpoint: https://Phi-3-mini-4k-instruct-staging.eastus2.models.ai.azure.com
Environment Variables:

AZURE_PHI_3_ENDPOINT="https://Phi-3-mini-4k-instruct-staging.eastus2.models.ai.azure.com"
AZURE_PHI_3_KEY="[staging-key]"

Production Environment

Endpoint: https://Phi-3-mini-4k-instruct-production.eastus2.models.ai.azure.com
Environment Variables:

AZURE_PHI_3_ENDPOINT="https://Phi-3-mini-4k-instruct-production.eastus2.models.ai.azure.com"
AZURE_PHI_3_KEY="[production-key]"

✅ Using separate endpoints for staging and production
Usage in Codebase
Client Configuration
The codebase uses the following OpenAI clients:

Standard OpenAI Client (now used for GPT-4o):

openai_client = AzureOpenAI(
api_key=config.azure_openai_api_key,
api_version=config.azure_api_version,
azure_endpoint=config.azure_openai_endpoint,
)

US East Client (GPT-4o):

openai_client_us_east = AzureOpenAI(
api_key=config.azure_openai_api_key_us_east,
api_version=config.azure_api_version,
azure_endpoint=config.azure_openai_endpoint_us_east,
)

Phi-3 Client:

def get_phi_client():
"""
Get a Phi-3 client with the configured endpoint and key.
"""
return AsyncClientContextManager(
AsyncChatCompletionsClient(
endpoint=config.azure_phi_3_endpoint,
credential=AzureKeyCredential(config.azure_phi_3_key)
)
)
Areas Addressed
GPT-3.5 Turbo Deployments

✅ GPT-3.5 Turbo has been phased out and replaced with GPT-4o
✅ All functions previously using GPT-3.5 have been migrated to use GPT-4o

Phi-3 Deployments

✅ Separate deployments for staging and production have been implemented
✅ Standardized naming convention (phi_3) is now used consistently
Configuration Details
From updated config.py:

# API Version for Azure OpenAI

azure_api_version = "2024-02-01" # default

# Deployment Names

azure_openai_deployment = "gpt-4o" # default for all environments
Current Usage Analysis
GPT-4o Usage
Currently being used explicitly for:

File Extraction Tasks:

Bill of Materials extraction (get_bom_items_from_file())
Packaging level classification (get_packaging_level())
Product info extraction (get_product_info_from_file())

Emissions Factor Matching:

Technology representation comparison (get_technological_representation())
Common chemical names lookup (get_common_chemical_names())
Input category prediction (predict_input_category())
Product constituent analysis (predict_product_constituents())
Product description generation (get_product_description())
Activity matching (get_closest_match())

Product Category Prediction:

Category prediction (predict_product_category())
Product description generation (predict_product_description())

Product Manufacturing:

Manufacturing process prediction (predict_manufacturing_processes())
GPT-3.5 Turbo Usage (Deprecated)
✅ All functions have been migrated to use GPT-4o:

Previously Identified Functions Now Using GPT-4o:

Activity description enhancement (get_enhanced_activity_description())
Chemical feature extraction (get_chemical_features())

Key Implementation Changes:

Default model in config.py has been changed from "3_5_turbo" to "gpt-4o"
All completion calls now use config.azure_openai_deployment
Core functions like get_chat_completion() now default to GPT-4o
All API clients use the appropriate endpoints configured for GPT-4o

Migration Status:

✅ Complete migration from GPT-3.5 to GPT-4o
✅ All functions updated to use GPT-4o
✅ No active GPT-3.5 usage in production environment
Phi-3 Usage
Currently being used for specific lightweight tasks:

End-of-Life (EOL) Product Classification:

Product category EOL validation (get_valid_eol_product_category())
Returns XML-formatted responses for product disposal classification

Generic Option Selection:

Top match selection from options (get_top_match())
Used as a lightweight alternative for simple selection tasks

Implementation Details:

Uses async Azure Chat Completions Client
Custom XML-formatted responses with template parsing
Managed through get_phi_client() context manager
Response format: <RESPONSE><EXPLANATION>...</EXPLANATION><ANSWER>...</ANSWER></RESPONSE>
Now uses separate endpoints for staging and production environments

Key Characteristics:

Used for deterministic, template-based tasks
Structured output formats (XML)
Lower latency than GPT models
Cost-effective for simple classification tasks
Standardized naming convention (phi_3) across all environments
Implementation Status
✅ Completed Implementation:

Migration from GPT-3.5 to GPT-4o:

- Successfully migrated all functions to use GPT-4o
- Updated the 2 functions previously using GPT-3.5:
  - Migrated get_enhanced_activity_description() to use GPT-4o
  - Migrated get_chemical_features() to use GPT-4o
- Updated config.py to use GPT-4o as the default model
- Maintained backward compatibility by keeping GPT-3.5 environment variables

Phi-3 Implementation:

- Implemented separate endpoints for staging and production environments
- Updated Phi-3 client implementation in clients.py
- Standardized deployment names across environments:
  - Using gpt-4o (with hyphen) in both production and staging
  - Using phi-3 (with hyphen) in both production and staging

Environment-Specific Configuration:

- Implemented separate endpoints and API keys for staging and production
- Standardized deployment name format across all environments
- Updated environment variables in CI/CD pipeline for each environment
  Implemented Config.py Changes
  The following changes have been implemented in config.py:

Standardized Deployment Names:

- Using gpt-4o (hyphen) format as default across all environments
- Updated default value for azure_openai_deployment to "gpt-4o"
- Maintained consistent naming convention for all deployments

Updated Configuration:

```python
# Azure OpenAI deployment name
azure_openai_deployment: str = field(
    default_factory=lambda: get_optional_env_var(
        "AZURE_OPENAI_DEPLOYMENT", "gpt-4o"
    )
)
```

Environment-Specific Configuration:

- Production: Uses default "gpt-4o" (no env var override needed)
- Staging: Uses default "gpt-4o" (no env var override needed)
- Local: Uses default "gpt-4o" (can be overridden if needed)
  Appendix: Standardized Deployment Names
  Deployment Name Format Standardization

The deployment names have been standardized across all environments:

- Production: Uses hyphen format (gpt-4o)
- Staging: Now also uses hyphen format (gpt-4o)

This standardization is important because:

1. It provides consistency across environments
2. It simplifies configuration and deployment
3. It reduces the risk of errors due to format differences

Impact on Local Development

When developing locally:

- Use the standardized format gpt-4o (hyphen) for all environments
- No need to change AZURE_OPENAI_DEPLOYMENT based on environment
- Default config.py values will work with all environments

Technical Details

- API URL pattern: /openai/deployments/{deployment_name}/chat/completions
- The deployment_name in the URL must exactly match the name registered in Azure
- All environments now use the same format (hyphen) for consistency
- Azure deployments have been updated to match this standardized format
  Local Development Configuration
  Best practices for local development setup:

Separate Azure OpenAI Resource

- Create a dedicated Azure OpenAI resource for local development
- Use different API keys from staging/production
- Use the standardized naming convention (gpt-4o) for deployments

Example local .env:

# Local Development Environment Variables

AZURE_OPENAI_ENDPOINT="https://your-local-dev.openai.azure.com/"
AZURE_OPENAI_ENDPOINT_US_EAST="https://your-local-dev.openai.azure.com/" # Can use same endpoint
AZURE_OPENAI_API_KEY="your-local-dev-key"
AZURE_OPENAI_API_KEY_US_EAST="your-local-dev-key" # Can use same key
AZURE_OPENAI_DEPLOYMENT="gpt-4o" # Using standardized hyphen format

# Phi-3 Configuration

AZURE_PHI_3_ENDPOINT="https://your-phi-3-endpoint.models.ai.azure.com"
AZURE_PHI_3_KEY="your-phi-3-key"
Code Quality Improvements
✅ Implemented Config Variables Instead of Hardcoded Values

Previous code had hardcoded deployment names:

```python
completion = get_chat_completion(client, messages, model="gpt-4o")
```

Updated code now uses config variables:

```python
completion = get_chat_completion(client, messages, model=config.azure_openai_deployment)
```

Files updated with this change:

- emissions_factor_matching/predictions.py
- file_extraction/predictions.py
- All other files using direct "gpt_4o" strings

This change improves maintainability by:

1. Centralizing configuration in config.py
2. Making it easier to update deployment names across the codebase
3. Ensuring consistency in how models are referenced
