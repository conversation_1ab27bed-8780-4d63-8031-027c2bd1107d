# Comprehensive End-to-End Emission Factor (EF) Matching Flow

## 1. Introduction: The Core Challenge and Goal

The accurate calculation of environmental footprints (e.g., carbon emissions) for products and services is crucial. A fundamental step in this process is Emission Factor (EF) matching: linking user-described materials, components, processes, and activities to standardized EFs. An EF quantifies emissions per unit of activity (e.g., kg CO2e per kg of aluminum).

**Core Goal:** To select the most accurate and contextually appropriate Emission Factor from available databases for every unique user input, thereby ensuring reliable and trustworthy environmental footprint calculations.

This document outlines the multi-layered system designed to achieve this, including default matching logic, an external Machine Learning (ML) service, a Prediction Override mechanism for fine-grained control and correction, and insights into the frontend user interaction.

## 2. The Default Emission Factor Matching Flow

When a user, through the frontend UI, requests an EF match (e.g., by adding/editing a raw material or manufacturing process), the process is initiated. The frontend calls a backend API (typically a GraphQL query like `predictEmissionsFactors` with parameters such as `chemicalName`, `geography`, etc.). If no explicit override is already determined by the backend API handler for this query, the `EmissionsFactorsService` (the core backend service for EF matching) attempts to find a match in a specific order of precedence:

### 2.1. Step 1: Checking for a Prediction Override (Backend Application Layer)

Before `EmissionsFactorsService` even begins its internal matching logic, the calling API endpoint handler (or a service it uses, e.g., `RawMaterialsService`) is responsible for checking if a stored Prediction Override applies to the current request.

**Mechanism:**

- The API handler takes a key input from the user's request (e.g., `chemical_name` from the GraphQL query) and uses this as the query to consult the `PredictionOverrideService`.
- `PredictionOverrideService(tenant_id).get_emissions_factor_match_override(query=chemical_name)` is called.
- This service queries the `prediction_override` database table (which is tenant-specific) for an entry matching the query and a predefined `api_name` (e.g., "emissions-factor-matching").
- If a match is found:
  - The `override_value` (a JSON string containing identifiers like `activity_name`, `geography`, `reference_product_name`, `source`) is parsed.
  - These identifiers are used to fetch the complete `TenantEmissionsFactor` object from the database using `TenantEmissionsFactorRepo`.
  - This retrieved `TenantEmissionsFactor` object (let's call it `resolved_override_ef`) is then passed as the `emissions_factor_override` parameter when calling `EmissionsFactorsService.get_emissions_factor_match(...)`.

### 2.2. Step 2: Inside `EmissionsFactorsService.get_emissions_factor_match`

The `EmissionsFactorsService` then proceeds with the following logic:

#### 2.2.1. Priority 1: Applying a Passed-In Override

- **Check:** The service first checks if the `emissions_factor_override: TenantEmissionsFactor | None` parameter is populated.
- **Action:** If `emissions_factor_override` is not `None` (meaning the calling function found and passed a stored override as per Step 2.1, or an override was determined by some other contextual logic):
  - This `TenantEmissionsFactor` object is directly used as the primary match.
  - The `match_type` is set to `OVERRIDE`.
  - The subsequent fallback matching steps (curated match, ML model) for determining the primary match are bypassed.
- **Rationale:** An explicitly provided override represents the highest level of certainty or a specific user/admin directive and must take precedence.

#### 2.2.2. Priority 2: Checking for a "Curated Match" (SQL Database Lookup)

- **Condition:** This step is executed if `emissions_factor_override` was `None`.
- **Action:** The service calls its internal method `self._get_curated_match(ingredient_name, carbon_only)`.
  - This method uses `EmissionFactorRepo` to query the shared `emissions_factors` database table.
  - It looks for a record where the `raw_material` column matches the input `ingredient_name`. The `raw_material` field in the `EmissionsFactors` model is specifically designated for identifying these curated entries.
  - An additional filter applies: if `carbon_only` is `False`, the `source` of the curated EF must start with "Ecoinvent".
- **Outcome:** If a valid curated match is found, it's converted to an `Activity` object, and the `match_type` is set to `CURATED`.
- **Rationale:** Curated matches are high-quality, manually verified EFs for common materials, providing a reliable and quick matching option.

#### 2.2.3. Priority 3: Getting Recommendations from an External ML Service

- **Condition:** This step is executed if `emissions_factor_override` was `None` AND no curated match was found.
- **Action:** The service calls `await self.client.predict_activity_recommendations(...)`.
  - `self.client` is an instance of the `MLModels` class.
  - This method makes an HTTP POST request to an external ML service endpoint (e.g., `/emissions-factor-matching/activities/recommendations` at the `config.ML_MODELS_ENDPOINT`).
- **Payload to ML Service:** Includes `chemical_name`, `geography`, `number_of_matches`, and optional parameters like `product_category`, `valid_units`, `lcia_method`, `carbon_only`.
- **Expected Response from ML Service:** A JSON object corresponding to the `ActivityResponse` model, containing:
  - `matched_activity`: The ML service's top EF match recommendation.
  - `recommendations`: A list of alternative EF matches.
  - `explanation`: A textual explanation for the match.
  - `confidence`: A confidence score ("low", "medium", "high").
- **Outcome:** The `matched_activity` from the ML service's response becomes the primary EF selected by `EmissionsFactorsService`. The `match_type` is set to `RECOMMENDATION`.
- **Rationale:** The ML service handles a broader range of inputs, especially those that are ambiguous or lack a direct curated match, using more advanced techniques.

## 3. Deep Dive: The External ML Service for EF Matching

When the main application relies on the ML service, the following internal process occurs within that service (based on the "Understanding of Emission Factor Matching Service" document):

### 3.1. Core Components of the ML Service

- **Vector Database (ChromaDB):** Used for storing and querying EF data embeddings. It maintains collections like `emission_activities` (for standard EFs) and `collection_eol` (for end-of-life EFs).
- **Data Source:** Embeddings and EF data are sourced from "CarbonBright/emissions-factors-activities-overview" on HuggingFace (e.g., `chroma_with_instructor_embeddings_v2.zip`, `emissions_factors_with_geographies_v2.pkl`).
- **Embedding Model ("hkunlp/instructor-large"):** This model generates vector embeddings for textual descriptions of EFs and user queries, using specialized instruction prompts to guide the embedding process (e.g., `INSTRUCTION_PROMPT = "Retrieve the closest emissions activity name provided a product description..."`).

### 3.2. ML Service Request Processing Flow

- **Input Processing Pipeline:**
  - **CAS Number Resolution:** If a CAS number is not provided in the request, the service may attempt to derive it from the `chemical_name`.
  - **Category Prediction (`predict_input_category`):** Determines if the input `chemical_name` refers to a product or a chemical.
    - **For products:** Appends `product_category` (if provided by the user) and uses `predict_product_constituents` to identify constituent materials.
    - **For chemicals:** Retrieves common chemical names (via `get_common_chemical_names`) and a product description (via `get_product_description`).
  - This processed text forms the basis for the query.
- **Query Construction and Filtering (for Vector Search):**
  - A base filter is applied (e.g., `{"activity_type": {"$eq": "ordinary transforming activity"}}`).
  - Additional filters can be added based on request parameters like ISIC section (if product section is available), `valid_units`, `lcia_method`, and `carbon_only`.
- **Matching Process:**
  - **Vector Similarity Search:** The processed input query (now embedded using the instructor-large model) is used to perform a similarity search in ChromaDB against the stored EF embeddings. This typically returns a list of the top N (e.g., 25) potential matches.
  - **Ranking and Selection (using an OpenAI Model):** The candidates from the vector search are further processed. An OpenAI model (accessed via a function like `get_closest_match`) is used to:
    - Rank the candidates.
    - Select the single best `matched_activity`.
    - Generate the textual `explanation` for why this match was chosen.
    - Determine the `confidence` level of the match.
  - **Geography Matching (`get_geography_activity_match`):** Location-specific adjustments or matching for the specified `iso_code` (geography) are performed.
- **Response Generation:**
  - The ML service constructs and returns an `ActivityRecommendationsResponse` JSON object containing the `matched_activity`, `confidence`, `explanation`, and a list of alternative `recommendations`.

## 4. The Prediction Override System: Ensuring "Ideal EF Matches"

The Prediction Override system provides a mechanism to manually define and enforce the selection of a specific "Ideal EF Match" for a given search query, effectively overriding the default logic of curated matches and ML recommendations.

### 4.1. Purpose

- To correct known, persistent errors made by the automated matching system (curated or ML-based).
- To allow clients/tenants to enforce the use of their own custom or preferred EFs for specific inputs.
- To improve the accuracy and trustworthiness of EF matching for specific, well-understood scenarios.

### 4.2. Phase 1: Creating a Prediction Override Rule

- **Initiation (Admin/Curator Action):** An administrator or data curator identifies a scenario where a specific EF must be used for a particular search query and tenant.
- **Storing the Rule:**
  - This is typically done via an administrative UI or a dedicated tool/script, which internally calls `PredictionOverrideService.create_emissions_factor_match_override(...)`.
  - **Key Inputs to `PredictionOverrideService`:**
    - `tenant_id`: The tenant for whom the override applies.
    - `query`: The exact search term (e.g., "water") that will trigger this override.
    - `api_name`: A scope for the override (e.g., "emissions-factor-matching").
    - Details of the Ideal EF: `activity_name`, `geography`, `reference_product_name`, `source`.
  - **Action:** `PredictionOverrideService` constructs a JSON string from the ideal EF details and stores it in the `override_value` column of the `prediction_override` table, linked to the `tenant_id`, `query`, and `api_name`.

### 4.3. Phase 2: Applying an Existing Prediction Override

As detailed in Section 2.1, when a user makes a request that triggers an API call (e.g., GraphQL `predictEmissionsFactors`):

- The backend API endpoint handler (or a service it calls) first attempts to resolve an override by calling `PredictionOverrideService(...).get_emissions_factor_match_override(query=INPUT_FROM_USER_REQUEST)`.
- If a rule exists for the given `tenant_id`, `query`, and `api_name`, the corresponding `TenantEmissionsFactor` object is fetched.
- This fetched `TenantEmissionsFactor` is then passed as the `emissions_factor_override` parameter to `EmissionsFactorsService.get_emissions_factor_match(...)`.
- Inside `EmissionsFactorsService`, if this parameter is populated, it is used as the definitive match, and its `match_type` is set to `OVERRIDE`.

### 4.4. User Interface (UI) Interaction for EF Selection and Override Implications

Based on frontend analysis, the following describes the typical user interaction for selecting an EF within the application (e.g., in a "Select Emissions Factor" modal for a raw material or manufacturing process) and its current relation to the Prediction Override system:

- **Fetching Recommendations:**
  - When the user needs to select/edit an EF, the frontend UI (e.g., `EmissionsFactorSelector` component) initiates a GraphQL query (e.g., `predictEmissionsFactors`) to the backend.
  - This query includes parameters like `chemicalName`, `geography`, etc.
  - The backend processes this request as per the flow described in Section 2 (checking for existing overrides first, then curated, then ML).
  - The response to the frontend includes a `matchedActivity` (the top recommendation) and a list of other `recommendations`. This list is displayed in the UI modal.
- **User Selection and Update:**
  - The user can select an EF from the displayed list. This selection primarily updates the local state of the UI component.
  - When the user clicks an "Update" button, the selected EF information is typically passed to a parent component (e.g., `ProductInfoCell`).
  - This parent component then updates its local state and handles the persistence of this choice for the specific instance of the material/process being edited (e.g., saving the updated product bill of materials with the newly chosen EF ID for that line item).
- **Note on Creating New Prediction Overrides from Standard User UI:**
  - Based on current frontend analysis, the standard UI flow for a regular user selecting an EF for a specific item (e.g., a raw material in a product) and clicking "Update" does NOT automatically trigger a separate API call to the backend to create a new persistent `PredictionOverride` rule.
  - This means that if a user changes the system's suggested EF to a different one, this choice applies to the current item being edited but does not, by itself, "teach" the system to use this new choice for all future similar queries for that tenant.
- **Implication:** The creation and management of persistent `PredictionOverride` rules (as described in Section 4.2) are likely handled through:
  - A separate, dedicated administrative interface or section within the frontend, accessible to authorized users (admins/curators).
  - Internal data curation tools or scripts that directly interact with the `PredictionOverrideService` backend.
  - Manual processes by administrators, potentially informed by analyzing user choices or specific client requests.

## 5. Overall Hierarchy of Matching Logic (When Backend API is Called)

The backend system employs the following hierarchy to determine the final EF match for a given query:

1.  **Explicit Prediction Override (Highest Priority):** If the backend API endpoint handler (before calling `EmissionsFactorsService`) successfully resolves a stored `PredictionOverride` rule for the given tenant and query, the resulting `TenantEmissionsFactor` is passed to `EmissionsFactorsService` and used directly.
2.  **Curated Match (Medium Priority):** If no explicit override is passed to `EmissionsFactorsService`, it attempts to find a match in the shared `emissions_factors` table based on the `raw_material` field.
3.  **ML Service Recommendation (Fallback Priority):** If neither an explicit override is passed nor a curated match is found, `EmissionsFactorsService` calls the external ML service to get a match and recommendations.

## 6. Addressing the Goal: "Improve EF Matching Evaluation"

This multi-layered system, particularly with the inclusion of the Prediction Override mechanism, directly addresses the goal of improving EF matching evaluation and accuracy:

- **Direct Correction:** Overrides provide a direct way to fix known inaccuracies of the automated system (curated or ML). This is achieved by an admin/curator creating override rules via a dedicated process/UI.
- **Client-Specific Needs:** Tenant-specific overrides allow for customization according to individual client data or preferences.
- **Data for System Improvement:** Analyzing frequently created or used overrides (managed by admins/curators) can:
  - Identify weaknesses in the curated match list (highlighting EFs that should be added).
  - Pinpoint areas where the ML model is underperforming. This override data can serve as valuable feedback or even training data for future iterations of the ML models.
- **Evaluation Framework:** The existence of "Ideal EF Matches" (stored as overrides) allows for the quantitative evaluation of the default matching logic (curated + ML). For a given query with a known override, the system's default output can be compared against the ideal to measure accuracy and identify discrepancies.

By combining a robust default matching flow with a flexible (though likely admin-controlled) override system, the application aims to provide increasingly accurate, reliable, and context-aware Emission Factor matching. The standard user EF selection UI focuses on instance-specific choices, while persistent, rule-based overrides are managed through other channels.

### 6.1. Vector Search Inputs and Enrichment

The emission factor matching system's effectiveness heavily relies on the richness and accuracy of the inputs to the vector search. These inputs are not simply raw terms but rather semantically enhanced queries that provide comprehensive information for accurate matching.

#### 6.1.1. Input Processing Pipeline

The system begins with a basic set of user inputs:

- **chemical_name** (mandatory): The primary descriptor for the material or product
- **product_category** (optional): The category classification of the product, if applicable
- **iso_code** (optional): Geographic information for location-specific matching
- **cas_number** (optional): Chemical Abstracts Service registry number for precise chemical identification

#### 6.1.2. Semantic Enrichment Process

These inputs undergo a sophisticated enrichment process:

1. **Classification and Determination**:

   ```python
   category = predict_input_category(material)
   ```

   The system first determines whether the input is a chemical or a product, applying different enrichment strategies based on this classification.

2. **For Products**:

   - Incorporates the product category if provided
   - Predicts constituent materials using `predict_product_constituents()`
   - Generates a product description via `get_product_description()`
   - Creates a comprehensive query combining all of these elements

3. **For Chemicals**:
   - Retrieves common chemical names and synonyms with `get_common_chemical_names()`
   - Determines CAS number if not provided using `get_cas_number()`
   - Obtains a descriptive explanation of what the chemical is and how it's typically used
   - Constructs an enriched query that combines chemical names, synonyms, and descriptions

This enrichment process transforms a simple input term into a semantically rich query that captures various facets of the material or product, substantially improving the quality of vector matching.

#### 6.1.3. Constraint Application

The enriched query is then subjected to a series of constraints:

- **Always Applied**:

  - Activity Type Filter: `{"activity_type": {"$eq": "ordinary transforming activity"}}`

- **Conditionally Applied**:
  - ISIC Industry Section Filter: Applied if a match is found using `get_product_activity_isic_section()`
  - Unit Compatibility Filter: Applied if valid units are specified
  - LCIA Method Filter: Applied based on the specified life cycle impact assessment method
  - Geography-specific matching: Applied based on the provided ISO code and geography hierarchy

These constraints help narrow the vector search to relevant emission factors, ensuring both semantic relevance and technical compatibility.

### 6.2. Analysis of Input Classification System Gaps

While the current binary classification system (product vs. chemical) provides a foundation for semantic enrichment, a thorough analysis reveals several gaps that may affect matching precision.

#### 6.2.1. Current Binary Classification Implementation

The current classification logic is implemented as follows:

```python
def predict_input_category(input_str: str):
    prompt = (
        "Given an input, determine whether the input is a chemical or a more complex product.\n"
        "Output:\n"
        "CHEMICAL | PRODUCT"
    )
    # ... LLM interaction ...
    return category  # "CHEMICAL" or "PRODUCT"
```

This binary decision informs subsequent processing pipelines but may oversimplify the diversity of inputs encountered in real-world emissions factor matching.

#### 6.2.2. Identified Gaps in Input Classification

The current binary system (product vs. chemical) fails to adequately capture several important input types:

1. **Energy Inputs and Services**: Inputs like electricity, heat, natural gas, transportation services, or waste management services don't fit neatly into either chemical or product categories, yet have distinct emission factors.

2. **Natural Resources and Raw Materials**: Items like mineral ores, agricultural products, or water sources may not be accurately classified as either chemicals or products.

3. **Processes and Manufacturing Activities**: The system doesn't have a direct category for inputs that represent processes rather than materials (e.g., "Steel Welding" or "Heat Treatment").

4. **Mixed or Composite Materials**: While the system attempts to handle composite products by predicting constituents, it lacks a dedicated classification for standardized mixed materials that are neither pure chemicals nor finished products.

5. **Infrastructure and Capital Goods**: Capital goods and infrastructure elements have significant lifecycle impacts but don't fit well in the existing binary categories.

#### 6.2.3. Potential Missing Input Types

Based on these gaps, several additional input types could enhance the system:

1. **Energy Sources**: Electricity, fuels, heat, etc.
2. **Services**: Transportation, waste management, consulting services
3. **Natural Resources**: Raw materials in their unprocessed form
4. **Processes**: Manufacturing processes, treatments, and activities
5. **Mixed Materials**: Alloys, composites, and other standardized material mixtures
6. **Infrastructure/Capital Goods**: Buildings, machinery, and equipment
7. **Waste Streams**: Various types of waste requiring disposal or treatment
8. **Biological Entities**: Agricultural products, foods, biomass

#### 6.2.4. Impact on Evaluation and Matching Accuracy

These classification gaps may impact:

- **Matching Precision**: Certain input types may receive suboptimal semantic enrichment, leading to less precise matches.
- **Evaluation Frameworks**: Current evaluation against golden datasets may not adequately represent all input types.
- **Override Patterns**: A disproportionate number of overrides might be required for certain input categories, indicating systemic gaps.

#### 6.2.5. Recommended Enhancements

1. **Enhanced Input Classification**: Expand beyond the binary classification to include additional categories identified above.

2. **Specialized Enrichment Pipelines**: Create dedicated semantic enrichment pipelines for each input type.

3. **Category-Specific Constraints**: Develop specialized constraint sets based on input category.

4. **Expanded Evaluation Dataset**: Create a more diverse validation dataset covering all input types.

5. **Multi-stage Classification**: Implement a hierarchical classification system to better handle edge cases and ambiguous inputs.

Enhancing the input classification system would likely improve the emission factor matching precision and recall across a broader range of real-world scenarios.

## 7. Advanced ML/LLM Architecture for Emission Factor Matching

While sections 2 and 3 touched on the ML service, this section provides a deeper technical examination of how the ML components work together to match emission factors. The ML service implements a sophisticated Retrieval-Augmented Generation (RAG) architecture with several specialized components and techniques.

### 7.1. Vector Database and Embedding Framework

The EF matching service uses **ChromaDB** as its vector database, with two main collections:

- **`emission_activities`**: Stores embeddings for standard emission factor activities
- **`collection_eol`**: Contains embeddings for end-of-life emission factor activities

These collections are populated from pre-computed embeddings loaded from Hugging Face:

```
chroma_db_zip = hf_hub_download(
    repo_id="CarbonBright/emissions-factors-activities-overview",
    filename="chroma_with_instructor_embeddings_v2.zip",
    repo_type="dataset",
)
```

The embeddings are generated using the **"hkunlp/instructor-large"** model with task-specific instruction prompts:

```
INSTRUCTION_PROMPT = "Retrieve the closest emissions activity name provided a product description based on product inputs and manufacturing methods, consider suitable proxies:"

embedding_function = embedding_functions.InstructorEmbeddingFunction(
    model_name="hkunlp/instructor-large",
    instruction=INSTRUCTION_PROMPT,
)
```

This approach uses **instruction-tuned embeddings**, an advanced technique that creates more contextually appropriate vector representations by guiding the embedding model with specific instructions for the task at hand.

### 7.2. Multi-stage Query Processing Pipeline

The query processing involves several advanced stages to enrich and refine the user's input:

#### 7.2.1. Input Classification and Semantic Enrichment

First, the system determines whether the input refers to a chemical or a product:

```python
category = predict_input_category(material)
```

Based on this classification, different semantic enrichment strategies are applied:

**For Products:**

- Appends product category if provided
- Predicts constituent materials
- Creates an enriched query combining constituent materials and the product description

**For Chemicals:**

- Retrieves common chemical names and synonyms
- Obtains a descriptive explanation of the chemical
- Combines the chemical name, synonyms, and description into a rich semantic query

This query enrichment creates a more comprehensive representation that captures various aspects of the input, improving the semantic matching process.

#### 7.2.2. Intelligent Filter Construction

The system applies multiple domain-specific filters to the vector search based on:

- **Activity Type Filter**: `{"activity_type": {"$eq": "ordinary transforming activity"}}`
- **ISIC Section Filter**: Uses LLM to predict the appropriate industry section
- **Unit Compatibility Filter**: Filters by valid units if specified
- **LCIA Method Filter**: Filters by life cycle impact assessment method if specified

This combination of semantic matching with structured filtering represents an advanced hybrid retrieval approach.

### 7.3. Advanced RAG Implementation with Multi-stage Ranking

The most sophisticated part of the system is its multi-stage retrieval and ranking approach:

#### 7.3.1. First Stage: Vector Similarity Search

First, a vector-based similarity search retrieves an initial set of candidate matches:

```python
documents = collection.query(
    query_texts=[query_text],
    n_results=25,
    where=where
)
```

#### 7.3.2. Second Stage: LLM-Based Re-Ranking and Reasoning

Instead of relying solely on vector similarity, the system uses OpenAI's GPT-4 model to analyze the candidates and select the most appropriate match:

```python
closest_match = await get_closest_match(
    query_text,
    activities_for_selection,
)
```

The LLM performs several sophisticated tasks:

- Thoroughly analyzes semantic relationships between the query and candidates
- Selects the single best match based on technical understanding
- Provides a confidence rating ("low", "medium", "high")
- Generates an explanation justifying why this match was chosen

This represents an advanced form of RAG that combines the retrieval power of vector search with the reasoning capabilities of large language models.

#### 7.3.3. Third Stage: Geography-Aware Refinement

After selecting the best conceptual match, a geography-specific version is retrieved using a sophisticated geography hierarchy:

```python
matched_activity = get_activity_from_dataset(
    activity["activity_name"],
    iso_code,
    activity["similarity"],
    activity["metadata"]["reference_product_name"]
)
```

The `get_geography_activity_match` function implements a geography tree structure to find the closest geographical match by traversing from specific countries up to continents and global representations.

### 7.4. Technological Representation Quality Assessment

The system includes a unique feature to evaluate the quality of matches based on technological characteristics:

```python
async def get_technological_representation(process_name: str, activity_name: str):
    # Compares different technological categories
    process_design_comparison = await compare_category(
        "Production Process Design",
        process_name,
        activity_name,
    )
    # Similar comparisons for operating conditions, material quality, and process scale
```

This evaluates match quality across multiple dimensions:

- Production process design
- Operating conditions
- Material quality/type
- Process scale

Each dimension is analyzed by an LLM to determine similarity, providing a comprehensive technical quality assessment.

### 7.5. Performance Optimization and Caching

To ensure responsive performance, the system implements caching for resource-intensive operations:

```python
@model_api.post("/activities/recommendations")
@cached(cache)
async def get_recommended_activities(...):
    # Implementation
```

This prevents redundant processing of identical requests, optimizing resource usage while maintaining accuracy.

### 7.6. How This Advanced Architecture Improves EF Matching

This sophisticated ML/LLM architecture enables several key improvements to emission factor matching:

1. **Enhanced Semantic Understanding**: The instruction-tuned embeddings combined with query enrichment capture nuanced relationships between materials and emission factors.

2. **Hybrid Retrieval Approach**: The combination of vector search with structured filters balances semantic similarity with domain-specific constraints.

3. **Reasoning-Augmented Selection**: Using LLMs to analyze candidates enables selection based on deeper technical understanding rather than just surface-level similarity.

4. **Explainability**: The system provides human-readable explanations for its choices, increasing transparency and user trust.

5. **Geographical Specialization**: The geography hierarchy ensures that matches are adapted to the specific regional context of the query.

6. **Technical Quality Assessment**: The technological representation evaluation provides additional confidence in the technical appropriateness of matches.

By combining these advanced techniques, the ML service achieves a level of matching sophistication that goes beyond traditional keyword or simple vector-based approaches, addressing the complex challenges of emission factor matching.

### 7.7. Key Program Files for ML/LLM Processing

The following files constitute the core codebase for the ML/LLM-powered emission factor matching system:

#### Core Emission Factor Matching Files

- `emissions_factor_matching/api.py` - FastAPI endpoints for the emission factor matching service
- `emissions_factor_matching/dataset.py` - Vector database configuration and data loading
- `emissions_factor_matching/model.py` - Embedding model and instruction prompts
- `emissions_factor_matching/predictions.py` - Core ML/LLM prediction logic
- `emissions_factor_matching/geography.py` - Geography hierarchy and matching logic
- `emissions_factor_matching/prompt.py` - Prompts for LLM interactions

#### Dependencies and Shared Utilities

- `completions.py` - OpenAI API integration for LLM completions
- `clients.py` - Client configuration for Azure OpenAI and other services
- `config.py` - Configuration parameters for API keys and endpoints
- `completion_utils.py` - Parsing and processing utilities for LLM outputs
- `shared_predictions.py` - Shared prediction functions used across multiple services

#### Related ML Services

- `chemical_prediction/predictions.py` - Chemical identification and classification
- `product_category_prediction/predictions.py` - Product categorization
- `product_manufacturing/predictions.py` - Manufacturing process prediction

#### Testing and Validation

- `emissions_factor_matching/tests/test_activity_prediction.py` - Activity prediction tests
- `emissions_factor_matching/tests/test_predictions.py` - Prediction validation suite
- `emissions_factor_matching/tests/test_technological_representation.py` - Technology match quality tests
- `emissions_factor_matching/tests/test_geography.py` - Geography matching tests
