# JSON Handling in Cache

## Issue Context

In `utils/cache.py`, there was an implementation to handle JSON decode errors when retrieving data from the cache:

```python
return json.loads(value)
except json.JSONDecodeError:
    # If not JSO<PERSON>, return as is
```

## Code Review Feedback

The reviewer (thejasmn) provided the following feedback:

1. "We should keep the cache to be fairly general and not have to change because of specific issues we are having to deal with models etc."
   - The cache functionality should remain generic
   - It should not be modified to handle specific edge cases from models or other components

2. "This json stored into the cache is serialized from an object into json using json.dumps() and should be a valid json. So this change should not be needed."
   - All data stored in the cache is already serialized using `json.dumps()`
   - By design, this means the data should always be valid JSON when retrieved
   - Adding exception handling for invalid JSON could mask potential data integrity issues

## Required Change

The exception handling for `JSONDecodeError` should be removed because:

1. The cache should only contain properly serialized JSON data (using `json.dumps()` when storing)
2. If there's invalid JSO<PERSON> in the cache, it indicates an upstream issue that should be fixed where the data is being stored
3. By removing the exception handling, any issues with invalid data in the cache would be surfaced immediately rather than being masked

### Code to Remove
```python
except json.JSONDecodeError:
    # If not JSON, return as is
```

## Implementation Status

✅ **COMPLETED** - The JSONDecodeError exception handling has been removed from `utils/cache.py` in the `RedisCache.get()` method. The method now uses a simple `json.loads(value)` call, allowing any JSON decode errors to surface properly and maintain data integrity in the cache.

### Additional Fixes Applied

✅ **Data Consistency Fix** - Fixed the `RedisCache.set()` method to consistently serialize ALL data types to JSON (including strings), ensuring that the `get()` method can always parse cached data with `json.loads()`.

✅ **Parameter Type Fix** - Corrected the `refresh_expiry` parameter in `RedisCache.get()` from `bool` to `Optional[int]` to match its actual usage as an expiration time value.

### Changes Made
1. **Removed complex JSONDecodeError handling** from `get()` method
2. **Simplified JSON parsing** to use direct `json.loads(value)` call
3. **Ensured consistent serialization** by always using `json.dumps()` in `set()` method
4. **Fixed parameter types** for proper Redis expiry functionality
5. **Added comprehensive unit tests** to prevent regressions and document expected behavior

### Tests Added
- `test_cached_decorator_refresh_expiry()` - Tests refresh_expiry functionality with both enabled/disabled states
- `test_redis_cache_refresh_expiry_parameter_types()` - Tests that RedisCache.get() handles different parameter types correctly
- `test_cached_decorator_passes_correct_refresh_expiry_type()` - Documents and tests the fix for the boolean parameter bug

## Best Practices

1. Data Integrity:
   - Always ensure proper serialization when storing data in the cache
   - Use `json.dumps()` consistently when writing to cache
   - Don't silently handle data integrity issues

2. Error Handling:
   - Let JSON decode errors surface to help identify issues early
   - Fix data integrity issues at the source rather than handling them during retrieval
   - Maintain strict data format consistency in the cache

3. Cache Design:
   - Keep cache implementation generic and reusable
   - Avoid special case handling in cache layer
   - Push data format responsibility to the code that writes to cache
