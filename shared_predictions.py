from typing import List, Any
from completions import get_azure_completion
from completion_utils import parse_template_response
from clients import get_phi_client


async def get_top_match(options: List[Any], objective: str) -> int | None:
    """Get the index of the top match given a list of options and an objective."""
    messages = [
        {
            "role": "user",
            "content": f"Provided a list of options {objective} <INSTRUCTION>YOU WILL ONLY EVER SELECT A SINGLE OPTION AND YOU WILL ALWAYS SELECT AN OPTION FROM THE LIST OF OPTIONS, RETURN THE INDEX OF THE SELECTED OPTION</INSTRUCTION>"
        },
        {
            "role": "assistant",
            "content": "I will only return well formed xml. I will return a single index of the option which best meets the criteria provided above in the list of options provided, I will always pick one option. I will only ever provide a single integer with a short succinct explanation (max 35 words) of why I picked the option formated as the following <RESPONSE><EXPLANATION>EXPLANATION</EXPLANATION><INDEX>INDEX</INDEX></RESPONSE>."
        },
        {
            "role": "user",
            "content": "\n".join([f"{index}: '{option}'" for index, option in enumerate(options)])
        }
    ]
    async with get_phi_client() as client:
        response = await get_azure_completion(client, messages)
    if not response:
        return None

    response_dict = parse_template_response(response)

    try:
        return int(response_dict["INDEX"])
    except ValueError:
        return None
