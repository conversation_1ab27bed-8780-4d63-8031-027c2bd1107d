import time
import json
import argparse
import traceback
from emissions_factor_matching.dataset import collection, collection_eol

# --- Default Configuration ---
DEFAULT_QUERY_TEXT = 'Primary material: Hexafluoroethane-based polymer. Hexafluoroethane-based polymer for aerospace thermal insulation'
DEFAULT_WHERE_CLAUSE = {
    "$and": [
        {"activity_type": {"$eq": "ordinary transforming activity"}},
        {"isic_section": {"$eq": "C - Manufacturing"}}
    ]
}
DEFAULT_N_RESULTS = 10

def parse_args():
    parser = argparse.ArgumentParser(description='Run ChromaDB queries with configurable parameters')
    parser.add_argument('--collection_name', type=str, default="emission_activities",
                        help='Name of the ChromaDB collection to query (emission_activities or eol_activities)')
    parser.add_argument('--query_text', type=str, default=DEFAULT_QUERY_TEXT,
                        help='Query text to search for')
    parser.add_argument('--where_clause', type=str, default=json.dumps(DEFAULT_WHERE_CLAUSE),
                        help='JSON string of the where clause for filtering')
    parser.add_argument('--n_results', type=int, default=DEFAULT_N_RESULTS,
                        help='Number of results to return')
    parser.add_argument('--save_results', action='store_true',
                        help='Save full results to a JSON file')
    parser.add_argument('--output_file', type=str, default='chroma_query_results.json',
                        help='File to save results to if save_results is True')
    parser.add_argument('--display_count', type=int, default=5,
                        help='Number of top results to display')

    return parser.parse_args()

def run_chroma_query(args):
    """
    Uses the existing ChromaDB collection to run a query.
    """
    try:
        # Parse the where clause from JSON string
        where_clause = json.loads(args.where_clause)

        # Select the appropriate collection
        selected_collection = collection_eol if args.collection_name == "eol_activities" else collection
        collection_name = "eol_activities" if args.collection_name == "eol_activities" else "emission_activities"

        print(f"Using existing ChromaDB collection: '{collection_name}'")
        print(f"Collection contains {selected_collection.count()} items.")

        # Execute the Query
        print(f"\nExecuting query against collection '{collection_name}':")
        print(f"  Query Text: \"{args.query_text}\"")
        print(f"  Where Clause: {where_clause}")
        print(f"  N_Results: {args.n_results}")

        chroma_start_time = time.time()
        results = selected_collection.query(
            query_texts=[args.query_text],
            where=where_clause,
            n_results=args.n_results,
            include=['metadatas', 'documents', 'distances']
        )
        chroma_elapsed_time = (time.time() - chroma_start_time) * 1000
        print(f"\nVector search completed in {chroma_elapsed_time:.2f}ms")

        # Process and Print Results
        if not results or not results.get('ids') or not results['ids'][0]:
            print("\nNo results returned from ChromaDB or results format is unexpected.")
            if results:
                print(f"Raw results object: {results}")
            return

        num_retrieved = len(results['ids'][0])
        print(f"\nRetrieved {num_retrieved} results (displaying top {args.display_count} or all if fewer):")

        for i in range(min(num_retrieved, args.display_count)):
            print(f"\n--- Result {i+1} ---")
            doc_id = results['ids'][0][i]
            distance = results['distances'][0][i] if results['distances'] else "N/A"
            document_content = results['documents'][0][i] if results['documents'] else "N/A"
            metadata = results['metadatas'][0][i] if results['metadatas'] else "N/A"

            print(f"  ID: {doc_id}")
            print(f"  Distance (similarity score): {distance}")
            print(f"  Document Content: {document_content}")
            print(f"  Metadata: {metadata}")

        if num_retrieved > args.display_count:
            print(f"\n... and {num_retrieved - args.display_count} more results not displayed here.")

        # Save full results to a file if requested
        if args.save_results:
            with open(args.output_file, "w") as f:
                json.dump(results, f, indent=2)
            print(f"\nFull results saved to {args.output_file}")

    except Exception as e:
        print(f"\nAn error occurred: {e}")
        print("Traceback:")
        traceback.print_exc()

if __name__ == "__main__":
    args = parse_args()
    run_chroma_query(args)
