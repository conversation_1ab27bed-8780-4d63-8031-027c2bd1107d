from typing import List
from pydantic import BaseModel
from product_category_prediction.dataset import product_categories
from completions import get_chat_completion, get_structured_completion
from config import config
from utils import logger


class CategoryCompletionOutput(BaseModel):
    category: str


def predict_product_description(product_name: str) -> str:
    system_prompt = (
        "Given a product name predict what the product is, do not mention the product name.\n"
        "Example:\n"
        "Bunny Soft Fabric Conditioner -> Fabric Conditioner\n"
    )

    messages = [
        {
            "role": "system",
            "content": system_prompt,
        },
        {
            "role": "user",
            "content": product_name,
        }
    ]

    # This returns plain text, so we use the regular completion function
    completion = get_chat_completion(
        None,  # Client parameter is ignored in new implementation
        messages,
        deployment=config.azure_openai_deployment,
    )

    logger.info(f"Description Prediction: {completion}")

    return completion


def predict_product_category(product_name: str) -> List[str]:
    """
    Predict product category using Google Product Taxonomy.
    Now uses structured output with <PERSON><PERSON><PERSON><PERSON> for guaranteed valid responses.
    """
    predicted_category = []
    categories = "\n".join(list(product_categories.keys()))
    tree = product_categories
    product_description = predict_product_description(product_name)

    for _ in range(0, 10):  # Limit depth to 10, no possibility of infinite loop

        system_prompt = (
            "Given a product description and the current predicted category add to it by predicting the next category from a selection of categories in the Google Product Taxonomy. Consider the subcategories of a next category candidate to find the optimal match.\n"
            f"The categories are:\n{categories}\n"
            "Output the predicted category (only include the next predicted section, do not include the full category)\n"
        )

        messages = [
            {
                "role": "system",
                "content": system_prompt,
            },
            {
                "role": "user",
                "content": (
                    f"Description: {product_description}"
                    f"Current Category: {' > '.join(predicted_category)}"
                ),
            }
        ]

        # Use structured completion with Pydantic model
        result = get_structured_completion(
            messages=messages,
            response_model=CategoryCompletionOutput,
            deployment=config.azure_openai_deployment,
            temperature=0,
        )

        if result is None:
            logger.warning("Failed to get category prediction")
            break

        logger.info(f"Category Prediction: {result.category}")

        if not result.category in tree:
            logger.info(f"Category '{result.category}' not found in tree, stopping")
            break

        predicted_category.append(result.category)
        tree = tree[result.category]
        if not tree:
            logger.info("Reached leaf node in category tree")
            break

        categories = "\n- ".join(list(tree.keys()))

    return predicted_category