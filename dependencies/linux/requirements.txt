aiohttp==3.9.3
aiosignal==1.3.1
annotated-types==0.6.0
antlr4-python3-runtime==4.9.3
anyio==4.3.0
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
asgiref==3.8.1
asttokens==2.4.1
async-lru==2.0.4
async-timeout==4.0.3
attrs==23.2.0
azure-ai-inference==1.0.0b6
azure-core==1.32.0
Babel==2.15.0
backoff==2.2.1
bcrypt==4.1.3
beautifulsoup4==4.12.3
bleach==6.1.0
build==1.2.1
cachetools==5.3.3
certifi==2024.2.2
cffi==1.16.0
chardet==5.2.0
charset-normalizer==3.3.2
chroma-hnswlib==0.7.6
chromadb==0.5.7
click==8.1.7
colorama==0.4.6
coloredlogs==15.0.1
comm==0.2.2
contourpy==1.2.1
cryptography==42.0.7
cycler==0.12.1
dataclasses-json==0.6.6
datasets==2.18.0
debugpy==1.8.1
decorator==5.1.1
deepdiff==7.0.1
defusedxml==0.7.1
Deprecated==1.2.14
dill==0.3.8
distro==1.9.0
dnspython==2.6.1
effdet==0.4.1
email_validator==2.2.0
emoji==2.12.1
et-xmlfile==1.1.0
exceptiongroup==1.2.0
executing==2.0.1
fastapi==0.110.1
fastapi-cli==0.0.4
fastjsonschema==2.19.1
filelock==3.13.3
filetype==1.2.0
flatbuffers==24.3.25
fonttools==4.51.0
fqdn==1.5.1
frozenlist==1.4.1
fsspec==2024.2.0
geographiclib==2.0
geopy==2.4.1
gitdb==4.0.11
GitPython==3.1.43
google-api-core==2.19.0
google-auth==2.29.0
google-cloud-vision==3.7.2
googleapis-common-protos==1.63.1
grpcio==1.64.1
grpcio-status==1.62.2
h11==0.14.0
httpcore==1.0.5
httptools==0.6.1
httpx==0.27.0
huggingface-hub==0.22.2
humanfriendly==10.0
idna==3.6
importlib_metadata==7.1.0
importlib_resources==6.4.0
InstructorEmbedding==1.0.1
iopath==0.1.10
ipykernel==6.29.4
ipython==8.24.0
isodate==0.7.2
isoduration==20.11.0
jedi==0.19.1
Jinja2==3.1.3
joblib==1.4.2
json5==0.9.25
jsonpath-python==1.0.6
jsonpointer==2.4
jsonschema==4.22.0
jsonschema-specifications==2023.12.1
jupyter-events==0.10.0
jupyter-lsp==2.2.5
jupyter-server-mathjax==0.2.6
jupyter_client==8.6.1
jupyter_core==5.7.2
jupyter_server==2.14.0
jupyter_server_terminals==0.5.3
jupyterlab==4.2.0
jupyterlab_git==0.50.0
jupyterlab_pygments==0.3.0
jupyterlab_server==2.27.1
kiwisolver==1.4.5
kubernetes==30.1.0
langdetect==1.0.9
layoutparser==0.3.4
loguru==0.7.2
lxml==5.2.2
Markdown==3.6
markdown-it-py==3.0.0
MarkupSafe==2.1.5
marshmallow==3.21.2
matplotlib==3.8.4
matplotlib-inline==0.1.7
mdurl==0.1.2
mistune==3.0.2
mmh3==4.1.0
monotonic==1.6
mpmath==1.3.0
msg-parser==1.2.0
multidict==6.0.5
multiprocess==0.70.16
mypy-extensions==1.0.0
nbclient==0.10.0
nbconvert==7.16.4
nbdime==4.0.1
nbformat==5.10.4
nest-asyncio==1.6.0
networkx==3.3
nltk==3.8.1
notebook_shim==0.2.4
numpy==1.26.4
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.1.105
nvidia-cuda-nvrtc-cu12==12.1.105
nvidia-cuda-runtime-cu12==12.1.105
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==*********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==**********
nvidia-cusparse-cu12==**********
nvidia-nccl-cu12==2.19.3
nvidia-nvjitlink-cu12==12.5.40
nvidia-nvtx-cu12==12.1.105
oauthlib==3.2.2
olefile==0.47
omegaconf==2.3.0
onnx==1.16.1
onnxruntime==1.18.0
openai>=1.68.2
langchain-openai
langchain-core
opencv-python==*********
opencv-python-headless==*********
openpyxl==3.1.3
opentelemetry-api==1.25.0
opentelemetry-exporter-otlp-proto-common==1.25.0
opentelemetry-exporter-otlp-proto-grpc==1.25.0
opentelemetry-instrumentation==0.46b0
opentelemetry-instrumentation-asgi==0.46b0
opentelemetry-instrumentation-fastapi==0.46b0
opentelemetry-proto==1.25.0
opentelemetry-sdk==1.25.0
opentelemetry-semantic-conventions==0.46b0
opentelemetry-util-http==0.46b0
ordered-set==4.1.0
orjson==3.10.5
overrides==7.7.0
packaging==24.0
pandas==2.2.1
pandoc==2.4
pandocfilters==1.5.1
parso==0.8.4
pdf2image==1.17.0
pdfminer.six==20231228
pdfplumber==0.11.0
pexpect==4.9.0
pikepdf==9.0.0
pillow==10.3.0
pillow_heif==0.16.0
platformdirs==4.2.1
plotly==5.22.0
plumbum==1.8.3
ply==3.11
portalocker==2.8.2
posthog==3.5.0
prometheus_client==0.20.0
prompt-toolkit==3.0.43
proto-plus==1.23.0
protobuf==4.25.3
psutil==5.9.8
ptyprocess==0.7.0
pure-eval==0.2.2
pyarrow==15.0.2
pyarrow-hotfix==0.6
pyasn1==0.6.0
pyasn1_modules==0.4.0
pycocotools==2.0.7
pycparser==2.22
pydantic>=2.6.4,<3.0.0
pydantic_core==2.16.3
Pygments==2.18.0
pypandoc==1.13
pyparsing==3.1.2
pypdf==4.2.0
pypdfium2==4.30.0
PyPika==0.48.9
pyproject_hooks==1.1.0
pysqlite3-binary==0.5.3
pytesseract==0.3.10
python-dateutil==2.9.0.post0
python-docx==1.1.2
python-dotenv==1.0.1
python-iso639==2024.4.27
python-json-logger==2.0.7
python-magic==0.4.27
python-multipart==0.0.9
python-pptx==0.6.23
pytz==2024.1
PyYAML==6.0.1
pyzmq==26.0.3
rapidfuzz==3.9.3
redis==5.0.1
referencing==0.35.1
regex==2023.12.25
requests==2.31.0
requests-oauthlib==2.0.0
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rich==13.7.1
rpds-py==0.18.1
rsa==4.9
safetensors==0.4.2
scikit-learn==1.4.2
scipy==1.13.0
seaborn==0.13.2
Send2Trash==1.8.3
sentence-transformers==2.2.2
sentencepiece==0.2.0
sentry-sdk==2.6.0
shellingham==1.5.4
six==1.16.0
smmap==5.0.1
sniffio==1.3.1
soupsieve==2.5
stack-data==0.6.3
starlette==0.37.2
sympy==1.12
tabulate==0.9.0
tenacity==8.3.0
terminado==0.18.1
threadpoolctl==3.5.0
timm==1.0.3
tinycss2==1.3.0
tokenizers==0.15.2
tomli==2.0.1
torch==2.2.2
torchvision==0.17.2
tornado==6.4.1
tqdm==4.66.2
traitlets==5.14.3
transformers==4.39.3
triton==2.2.0
typer==0.12.3
types-python-dateutil==2.9.0.20240316
typing-inspect==0.9.0
typing_extensions==4.11.0
tzdata==2024.1
ujson==5.10.0
unstructured==0.14.4
unstructured-client==0.22.0
unstructured-inference==0.7.33
unstructured.pytesseract==0.3.12
uri-template==1.3.0
urllib3==2.2.1
uvicorn==0.29.0
uvloop==0.19.0
watchfiles==0.22.0
wcwidth==0.2.13
webcolors==1.13
webencodings==0.5.1
websocket-client==1.8.0
websockets==12.0
wrapt==1.16.0
xlrd==2.0.1
XlsxWriter==3.2.0
xxhash==3.4.1
yarl==1.9.4
zipp==3.19.2
diskcache==5.6.3
