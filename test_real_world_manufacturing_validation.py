#!/usr/bin/env python3
"""
Real-World Manufacturing EF Matching Validation

This script tests the new 9-phase AI-assisted emission factor matching system
against real-world manufacturing cases to validate improvements over the current system.

Based on manufacturing methods spreadsheet showing Current EF vs Ideal EF matches.
Designed to run inside Docker container: docker exec ml-models-app python test_real_world_manufacturing_validation.py
"""

import sys
sys.path.append('/home/<USER>/app')

__import__('pysqlite3')
sys.modules['sqlite3'] = sys.modules.pop('pysqlite3')

import json
from typing import Dict, List, Any
from dataclasses import dataclass
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ManufacturingTestCase:
    """Test case representing a real-world manufacturing scenario"""
    name: str
    manufacturing_method: str
    current_ef_match: str
    ideal_ef_match: str
    test_query: str
    expected_improvements: List[str]
    notes: str = ""

# Phase 1: Selected test cases from manufacturing methods spreadsheet
MANUFACTURING_TEST_CASES = [
    ManufacturingTestCase(
        name="Aluminum Die Cast",
        manufacturing_method="Aluminum Die Cast",
        current_ef_match="aluminum ingot, primary, to aluminum, cast alloy slab (customized for GLO)",
        ideal_ef_match="aluminum production, primary, cast alloy slab from continuous casting (RoW)",
        test_query="aluminum die cast manufacturing process",
        expected_improvements=["More specific casting process", "Better process specificity"],
        notes="Should prefer casting-specific processes over generic aluminum ingot"
    ),

    ManufacturingTestCase(
        name="Steel Hardware Manufacturing",
        manufacturing_method="Metal Steel AISI 12L14 Carbon Steel Hardware",
        current_ef_match="steel production, electric, low alloyed (customized for GLO)",
        ideal_ef_match="steel turning, primary roughing, computer numerical controlled (RoW)",
        test_query="steel hardware manufacturing AISI 12L14 carbon steel",
        expected_improvements=["Machining process recognition", "CNC operations"],
        notes="Should recognize machining/turning operations vs basic steel production"
    ),

    ManufacturingTestCase(
        name="Bronze Contouring",
        manufacturing_method="Metal Bronze Machined / Milled",
        current_ef_match="contouring, bronze (customized for GLO)",
        ideal_ef_match="steel turning, primary roughing, computer numerical controlled (RoW)",
        test_query="bronze machining milling contouring operations",
        expected_improvements=["Machining process specificity", "Material-process alignment"],
        notes="Should identify machining operations for bronze components"
    ),

    ManufacturingTestCase(
        name="Nylon Injection Molding",
        manufacturing_method="Polymeric Material Nylon (PA6.6) Extruded",
        current_ef_match="nylon 6-6 production",
        ideal_ef_match="extrusion of plastic sheets and thermoplastics, minor (RoW)",
        test_query="nylon PA6.6 extrusion plastic manufacturing",
        expected_improvements=["Process-specific recognition", "Extrusion vs generic production"],
        notes="Should distinguish extrusion process from generic nylon production"
    ),

    ManufacturingTestCase(
        name="Steel Wire Drawing",
        manufacturing_method="Metal Steel SVD P Music Wire Hardware",
        current_ef_match="wire drawing, steel (customized for GLO)",
        ideal_ef_match="wire drawing, steel (RoW)",
        test_query="steel music wire drawing manufacturing",
        expected_improvements=["Geographic optimization", "Process accuracy"],
        notes="Current match is good, should maintain or improve geographic matching"
    )
]

class ManufacturingValidationTester:
    """Tester for validating manufacturing EF matching improvements"""

    def __init__(self):
        self.results = []

    def test_case(self, test_case: ManufacturingTestCase) -> Dict[str, Any]:
        """Test a single manufacturing case through direct function calls"""
        logger.info(f"\n{'='*60}")
        logger.info(f"Testing: {test_case.name}")
        logger.info(f"Query: {test_case.test_query}")
        logger.info(f"Current EF: {test_case.current_ef_match}")
        logger.info(f"Ideal EF: {test_case.ideal_ef_match}")

        try:
            # Import the functions we need to test the 9-phase pipeline
            from emissions_factor_matching.predictions import (
                predict_enhanced_input_category,
                spot_modifiers,
                map_isic_classification,
                augment_query_text,
                construct_dynamic_filters,
                re_rank_candidates
            )
            from emissions_factor_matching.dataset import search_candidates_with_fallback
            from emissions_factor_matching.api import get_activity_from_dataset
            from pydantic import BaseModel

            # Create a request model
            class TestRequest(BaseModel):
                user_query_primary: str
                user_query_secondary: str | None = None
                lca_lifecycle_stage: str | None = None
                iso_code: str | None = None
                product_category_context: str | None = None
                carbon_only: bool = True  # Test with DEFRA sources

            request = TestRequest(user_query_primary=test_case.test_query)

            # Run the 9-phase pipeline
            logger.info("Running 9-phase AI pipeline...")

            # Phase 1.1: Enhanced Input Category Prediction
            enhanced_category = predict_enhanced_input_category(request)
            logger.info(f"Phase 1.1 - Enhanced category: {enhanced_category}")

            # Phase 1.2: Modifier Spotting
            modifiers = spot_modifiers(request, enhanced_category)
            logger.info(f"Phase 1.2 - Modifiers: {modifiers}")

            # Phase 1.3: ISIC Classification Mapping
            isic_codes = map_isic_classification(enhanced_category, modifiers, request.user_query_primary)
            logger.info(f"Phase 1.3 - ISIC codes: {isic_codes}")

            # Phase 1.4: Query Text Augmentation
            augmented_query = augment_query_text(request, enhanced_category, modifiers, isic_codes)
            logger.info(f"Phase 1.4 - Augmented query: '{augmented_query}'")

            # Phase 1.5: Dynamic Filter Construction
            dynamic_filters = construct_dynamic_filters(request, enhanced_category, modifiers, isic_codes)
            logger.info(f"Phase 1.5 - Dynamic filters: {dynamic_filters}")

            # Phase 1.6: ChromaDB Vector Search
            candidates = search_candidates_with_fallback(
                augmented_query=augmented_query,
                filters=dynamic_filters,
                n_results=25
            )
            logger.info(f"Phase 1.6 - Found {len(candidates)} candidates")

            if not candidates:
                return self._create_error_result(test_case, "No candidates found in vector search")

            # Phase 1.7: LLM Re-ranking & Justification
            matched_ef = re_rank_candidates(
                request_model=request,
                candidates=candidates,
                augmented_query=augmented_query,
                enhanced_category=enhanced_category,
                modifiers=modifiers,
                isic_codes=isic_codes
            )
            logger.info(f"Phase 1.7 - Selected: '{matched_ef.activity_name}' with {matched_ef.confidence} confidence")

            # Phase 1.8: Geography Matching & Record Retrieval
            matched_activity = get_activity_from_dataset(
                activity_name=matched_ef.activity_name,
                iso_code="GLO",  # Default geography
                similarity=matched_ef.confidence_score or 0.0,
                reference_product_name=matched_ef.reference_product_name,
                preferred_source=matched_ef.source  # Preserve the source selected by AI pipeline
            )
            logger.info(f"Phase 1.8 - Geography matched to '{matched_activity.geography}' with source '{matched_activity.source}'")

            # Create result data structure similar to API response
            result_data = {
                "matched_activity": {
                    "activity_name": matched_activity.activity_name,
                    "source": matched_activity.source,
                    "geography": matched_activity.geography,
                    "reference_product_name": matched_activity.reference_product_name
                },
                "confidence": matched_ef.confidence,
                "explanation": matched_ef.explanation
            }

            # Analyze the result
            analysis = self._analyze_result(test_case, result_data["matched_activity"], result_data)

            logger.info(f"New System Result: {result_data['matched_activity']['activity_name']}")
            logger.info(f"Confidence: {result_data['confidence']}")
            logger.info(f"Source: {result_data['matched_activity']['source']}")
            logger.info(f"Analysis: {analysis['improvement_assessment']}")

            return analysis

        except Exception as e:
            logger.error(f"Error testing {test_case.name}: {str(e)}")
            import traceback
            traceback.print_exc()
            return self._create_error_result(test_case, str(e))

    def _analyze_result(self, test_case: ManufacturingTestCase, matched_activity: Dict, full_result: Dict) -> Dict[str, Any]:
        """Analyze how the new result compares to current and ideal EF matches"""

        new_activity_name = matched_activity["activity_name"]
        new_source = matched_activity["source"]
        confidence = full_result["confidence"]
        explanation = full_result["explanation"]

        # Simple keyword-based analysis for improvement assessment
        improvement_indicators = []

        # Check if result is closer to ideal than current
        ideal_keywords = self._extract_keywords(test_case.ideal_ef_match)
        current_keywords = self._extract_keywords(test_case.current_ef_match)
        new_keywords = self._extract_keywords(new_activity_name)

        ideal_overlap = len(set(ideal_keywords) & set(new_keywords))
        current_overlap = len(set(current_keywords) & set(new_keywords))

        if ideal_overlap > current_overlap:
            improvement_indicators.append("Better keyword alignment with ideal EF")

        # Check for process specificity improvements
        process_terms = ["turning", "machining", "casting", "extrusion", "drawing", "molding", "roughing"]
        if any(term in new_activity_name.lower() for term in process_terms):
            improvement_indicators.append("Process-specific terminology detected")

        # Check source consistency (should be DEFRA for carbon_only=True)
        if new_source == "DEFRA":
            improvement_indicators.append("Correct source filtering (DEFRA for carbon_only)")

        # Overall assessment
        if len(improvement_indicators) >= 2:
            improvement_assessment = "SIGNIFICANT_IMPROVEMENT"
        elif len(improvement_indicators) == 1:
            improvement_assessment = "MODERATE_IMPROVEMENT"
        else:
            improvement_assessment = "NEEDS_ANALYSIS"

        return {
            "test_case_name": test_case.name,
            "new_activity_name": new_activity_name,
            "new_source": new_source,
            "confidence": confidence,
            "explanation": explanation,
            "improvement_indicators": improvement_indicators,
            "improvement_assessment": improvement_assessment,
            "ideal_keyword_overlap": ideal_overlap,
            "current_keyword_overlap": current_overlap,
            "success": True
        }

    def _extract_keywords(self, text: str) -> List[str]:
        """Extract meaningful keywords from EF activity names"""
        # Simple keyword extraction - can be enhanced
        stop_words = {"for", "to", "from", "of", "in", "at", "by", "with", "and", "or", "the", "a", "an"}
        words = text.lower().replace(",", " ").replace("(", " ").replace(")", " ").split()
        return [word for word in words if word not in stop_words and len(word) > 2]

    def _create_error_result(self, test_case: ManufacturingTestCase, error_msg: str) -> Dict[str, Any]:
        """Create error result structure"""
        return {
            "test_case_name": test_case.name,
            "error": error_msg,
            "success": False
        }

    def run_all_tests(self) -> Dict[str, Any]:
        """Run all manufacturing test cases and generate summary report"""
        logger.info("🚀 Starting Real-World Manufacturing EF Matching Validation")
        logger.info(f"Testing {len(MANUFACTURING_TEST_CASES)} manufacturing scenarios")

        results = []
        for test_case in MANUFACTURING_TEST_CASES:
            result = self.test_case(test_case)
            results.append(result)
            self.results.append(result)

        # Generate summary report
        summary = self._generate_summary_report(results)

        logger.info(f"\n{'='*60}")
        logger.info("📊 VALIDATION SUMMARY REPORT")
        logger.info(f"{'='*60}")
        logger.info(f"Total Test Cases: {summary['total_cases']}")
        logger.info(f"Successful Tests: {summary['successful_tests']}")
        logger.info(f"Failed Tests: {summary['failed_tests']}")
        logger.info(f"Significant Improvements: {summary['significant_improvements']}")
        logger.info(f"Moderate Improvements: {summary['moderate_improvements']}")
        logger.info(f"Overall Success Rate: {summary['success_rate']:.1%}")

        return summary

    def _generate_summary_report(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate summary statistics from test results"""
        total_cases = len(results)
        successful_tests = len([r for r in results if r.get("success", False)])
        failed_tests = total_cases - successful_tests

        successful_results = [r for r in results if r.get("success", False)]
        significant_improvements = len([r for r in successful_results if r.get("improvement_assessment") == "SIGNIFICANT_IMPROVEMENT"])
        moderate_improvements = len([r for r in successful_results if r.get("improvement_assessment") == "MODERATE_IMPROVEMENT"])

        return {
            "total_cases": total_cases,
            "successful_tests": successful_tests,
            "failed_tests": failed_tests,
            "significant_improvements": significant_improvements,
            "moderate_improvements": moderate_improvements,
            "success_rate": successful_tests / total_cases if total_cases > 0 else 0,
            "detailed_results": results
        }

def main():
    """Main execution function"""
    logger.info("🚀 Starting Real-World Manufacturing EF Matching Validation")
    logger.info("Running inside Docker container with direct function calls")

    tester = ManufacturingValidationTester()
    summary = tester.run_all_tests()

    # Save detailed results to file
    results_file = "/home/<USER>/app/manufacturing_validation_results.json"
    with open(results_file, "w") as f:
        json.dump(summary, f, indent=2)

    logger.info(f"\n✅ Validation complete! Detailed results saved to {results_file}")

    return summary

if __name__ == "__main__":
    print("Testing Real-World Manufacturing EF Matching with 9-Phase AI System\n")
    main()
