import asyncio
from langchain_openai import AzureChatOpenAI
from azure.ai.inference.aio import ChatCompletionsClient as AsyncChatCompletionsClient
from azure.core.credentials import AzureKeyCredential
from config import config

class AsyncClientContextManager:
    def __init__(self, client):
        self.client = client

    async def __aenter__(self):
        return self.client

    async def __aexit__(self, exc_type, exc_value, traceback):
        await self.client.close()
        await asyncio.sleep(0.250)

def get_phi_client():
    return AsyncClientContextManager(
        AsyncChatCompletionsClient(
            endpoint=config.azure_phi_3_endpoint,
            credential=AzureKeyCredential(config.azure_phi_3_key)
        )
    )

# LangChain Azure OpenAI client - replaces the old OpenAI client
def get_langchain_client(temperature: float = 0, deployment: str = None):
    """Get a LangChain Azure OpenAI client with structured output support"""
    return AzureChatOpenAI(
        azure_endpoint=config.azure_openai_endpoint,
        api_key=config.azure_openai_api_key,
        api_version=config.azure_api_version,
        azure_deployment=deployment or config.azure_openai_deployment,
        temperature=temperature,
    )

# Default client instances
langchain_client = get_langchain_client()
langchain_client_us_east = get_langchain_client()  # Using same endpoint for now

# Legacy compatibility - these will be removed after full migration
from openai import AzureOpenAI, AsyncAzureOpenAI

openai_client = AzureOpenAI(
    api_key=config.azure_openai_api_key,
    api_version=config.azure_api_version,
    azure_endpoint=config.azure_openai_endpoint,
)

def get_openai_async_client():
    client = AsyncAzureOpenAI(
        api_key=config.azure_openai_api_key,
        api_version=config.azure_api_version,
        azure_endpoint=config.azure_openai_endpoint,
    )
    return AsyncClientContextManager(client)

openai_client_us_east = AzureOpenAI(
    api_key=config.azure_openai_api_key,
    api_version=config.azure_api_version,
    azure_endpoint=config.azure_openai_endpoint,
)