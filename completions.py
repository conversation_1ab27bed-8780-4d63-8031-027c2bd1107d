from typing import List, TypedDict, Type, TypeVar, Optional
import time
import asyncio
from pydantic import BaseModel
from langchain_openai import AzureChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.messages import HumanMessage, SystemMessage
import sentry_sdk
from sentry_sdk.ai.monitoring import ai_track
from azure.core.exceptions import HttpResponseError
from clients import AsyncChatCompletionsClient, get_langchain_client
from config import config
from utils import logger

T = TypeVar('T', bound=BaseModel)

class Message(TypedDict):
    role: str
    content: str

class RateLimitExceeded(Exception):
    pass

@ai_track("Chat Completion")
async def _get_azure_completion(
    client: AsyncChatCompletionsClient,
    messages: List[Message]
) -> str | None:
    """Legacy Azure completion function for Phi-3 model"""
    retry_count = 0
    while True:
        with sentry_sdk.start_transaction(op="azure-chat-completion"):
            try:
                response = await client.complete(
                    body={
                        "messages": messages,
                        "max_tokens": 2048,
                        "temperature": 0,
                        "top_p": 0.1,
                        "presence_penalty": 0,
                        "frequency_penalty": 0
                    }
                )
                completion = response["choices"][0]["message"]["content"]

                logger.info(f"Completion: {completion}")
                return completion

            except HttpResponseError as error:
                if error.status_code == 429:
                    if retry_count > config.rate_limit_retry_count:
                        raise RateLimitExceeded(
                            "Exceeded number of retries, Rate Limit Exceeded."
                        ) from error

                    sleep_time = min(2**retry_count, 120)
                    retry_count += 1
                    logger.warning(f"Exceeded rate limit, retrying after {sleep_time}s.")
                    time.sleep(sleep_time)

async def get_azure_completion(
    client: AsyncChatCompletionsClient,
    messages: List[Message],
) -> str | None:
    """Legacy Azure completion function for Phi-3 model"""
    try:
        return await _get_azure_completion(client, messages)
    except Exception as error:
        logger.error(f"Error getting chat completion: {error}")
        return None

def convert_messages_to_langchain(messages: List[Message]):
    """Convert OpenAI message format to LangChain message format"""
    langchain_messages = []
    for msg in messages:
        if msg["role"] == "system":
            langchain_messages.append(SystemMessage(content=msg["content"]))
        elif msg["role"] == "user":
            langchain_messages.append(HumanMessage(content=msg["content"]))
    return langchain_messages

@ai_track("LangChain Structured Completion")
def get_structured_completion(
    messages: List[Message],
    response_model: Type[T],
    deployment: str = None,
    temperature: float = 0,
    max_retries: int = 3,
) -> T | None:
    """
    Get a structured completion using LangChain with guaranteed Pydantic model output.
    
    Args:
        messages: List of messages in OpenAI format
        response_model: Pydantic model class for the response
        deployment: Azure deployment name (optional)
        temperature: Temperature for the model
        max_retries: Number of retries for transient errors
    
    Returns:
        Instance of response_model or None if error
    """
    retry_count = 0
    while retry_count < max_retries:
        with sentry_sdk.start_transaction(op="langchain-structured-completion"):
            try:
                # Get LangChain client
                client = get_langchain_client(temperature=temperature, deployment=deployment)
                
                # Configure for structured output
                structured_llm = client.with_structured_output(response_model)
                
                # Convert messages to LangChain format
                langchain_messages = convert_messages_to_langchain(messages)
                
                # Get structured response
                result = structured_llm.invoke(langchain_messages)
                
                logger.info(f"Structured completion successful: {type(result).__name__}")
                return result
                
            except Exception as e:
                if "429" in str(e) or "rate limit" in str(e).lower():
                    if retry_count >= max_retries - 1:
                        logger.error("Exceeded max retries for rate limit")
                        return None
                    
                    sleep_time = min(2**retry_count, 120)
                    retry_count += 1
                    logger.warning(f"Rate limit hit, retrying after {sleep_time}s...")
                    time.sleep(sleep_time)
                else:
                    logger.error(f"Error in structured completion: {e}")
                    return None

@ai_track("LangChain Async Structured Completion")
async def get_async_structured_completion(
    messages: List[Message],
    response_model: Type[T],
    deployment: str = None,
    temperature: float = 0,
    max_retries: int = 3,
) -> T | None:
    """
    Async version of get_structured_completion.
    """
    retry_count = 0
    while retry_count < max_retries:
        with sentry_sdk.start_transaction(op="langchain-async-structured-completion"):
            try:
                # Get LangChain client
                client = get_langchain_client(temperature=temperature, deployment=deployment)
                
                # Configure for structured output
                structured_llm = client.with_structured_output(response_model)
                
                # Convert messages to LangChain format
                langchain_messages = convert_messages_to_langchain(messages)
                
                # Get structured response
                result = await structured_llm.ainvoke(langchain_messages)
                
                logger.info(f"Async structured completion successful: {type(result).__name__}")
                return result
                
            except Exception as e:
                if "429" in str(e) or "rate limit" in str(e).lower():
                    if retry_count >= max_retries - 1:
                        logger.error("Exceeded max retries for rate limit")
                        return None
                    
                    sleep_time = min(2**retry_count, 120)
                    retry_count += 1
                    logger.warning(f"Rate limit hit, retrying after {sleep_time}s...")
                    await asyncio.sleep(sleep_time)
                else:
                    logger.error(f"Error in async structured completion: {e}")
                    return None

# Legacy functions for backward compatibility - these use the new structured output internally
@ai_track("Chat Completion")
def get_chat_completion(
    client,  # This parameter is ignored now
    messages: List[Message],
    deployment: str = config.azure_openai_deployment,
    temperature: float = 0,
    n_validations: int = 0,
    strict: bool = False,
    model: str = None,
) -> str | None:
    """
    Legacy function for backward compatibility.
    Now uses LangChain for unstructured completions.
    """
    deployment = model if model is not None else deployment
    
    try:
        # Get LangChain client
        langchain_client = get_langchain_client(temperature=temperature, deployment=deployment)
        
        # Convert messages
        langchain_messages = convert_messages_to_langchain(messages)
        
        # Get completion
        response = langchain_client.invoke(langchain_messages)
        completion = response.content
        
        logger.info(f"Completion: {completion}")
        
        # Handle validations if needed (though this is less relevant with LangChain)
        for _ in range(n_validations):
            validation_response = langchain_client.invoke(langchain_messages)
            if completion != validation_response.content:
                logger.warning("Validation did not match chat completion returning None")
                return None
        
        return completion
        
    except Exception as error:
        logger.error(f"Error getting chat completion: {error}")
        return None

@ai_track("Async Chat Completion")
async def get_async_chat_completion(
    client,  # This parameter is ignored now
    messages: List[Message],
    deployment: str = config.azure_openai_deployment,
    temperature: float = 0,
    n_validations: int = 0,
    strict: bool = False,
    model: str = None,
) -> str | None:
    """
    Legacy async function for backward compatibility.
    Now uses LangChain for unstructured completions.
    """
    deployment = model if model is not None else deployment
    
    try:
        # Get LangChain client
        langchain_client = get_langchain_client(temperature=temperature, deployment=deployment)
        
        # Convert messages
        langchain_messages = convert_messages_to_langchain(messages)
        
        # Get completion
        response = await langchain_client.ainvoke(langchain_messages)
        completion = response.content
        
        logger.info(f"Completion: {completion}")
        
        # Handle validations if needed
        for _ in range(n_validations):
            validation_response = await langchain_client.ainvoke(langchain_messages)
            if completion != validation_response.content:
                logger.warning("Validation did not match chat completion returning None")
                return None
        
        return completion
        
    except Exception as error:
        logger.error(f"Error getting async chat completion: {error}")
        return None