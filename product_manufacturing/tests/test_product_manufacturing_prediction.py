from unittest import TestCase
from product_manufacturing.predictions import predict_manufacturing_processes

validation_manufacturing_methods = [
    (
        (
            "Foundations & Concealers",
            "L'Oreal True Match Foundation"
        ),
        [
            "Cosmetic Manufacturing and Processing"
        ],
    ),
    (
        (
            "Spring Water",
            "Evian"
        ),
        [
            "Beverage Bottling"
        ],
    ),
    (
        (
            "Shopping Totes",
            "Harvey Nichols Canvas Tote Bag",
        ),
        [
            "Textile Yarn Production",
            "Fabric Weaving",
            "Fabric Dyeing",
            "Garment Cut and Sew"
        ],
    ),
    (
        (
            "Coffee",
            "Nespresso Capsules Vertuo, Melozio, Medium Roast Coffee, 40-Count Coffee Pods",
        ),
        [
            "Coffee Processing"
        ],
    ),
    (
        (
            "All-Purpose Cleaners",
            "Delphis Eco X factor Surface Spray"
        ),
        [
            "Homecare Manufacturing & Processing"
        ]
    )
]

class TestProductManufacturingPrediction(TestCase):
    def test_product_manufacturing_predictions(self):
        for (
            (
                product_category,
                product_name,
            ),
            validation_processes
         ) in validation_manufacturing_methods:
            manufacturing_processes = predict_manufacturing_processes(
                product_category,
                product_name,
            )
            self.assertEqual(sorted(manufacturing_processes), sorted(validation_processes))
