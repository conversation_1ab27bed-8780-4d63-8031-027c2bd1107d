from typing import List
from pydantic import BaseModel, Field
from completions import get_chat_completion, get_structured_completion
from product_manufacturing.dataset import manufacturing_processes_df
from config import config
from utils import logger


class ManufacturingProcessesResponse(BaseModel):
    """Response model for manufacturing processes prediction"""
    processes: List[str] = Field(
        description="List of manufacturing processes in sequence for creating the product"
    )


def predict_manufacturing_processes(product_category: str, product_name: str) -> List[str]:
    """
    Predict the manufacturing processes for a product.
    Now uses structured output with LangChain for better reliability.
    """
    available_processes = manufacturing_processes_df["Process Name"].to_list()
    
    system_prompt = (
        "Given a product name and a product category, return "
        "a list of the manufacturing processes which happen in sequence for "
        "the creation of the product. If the product is a type of bag only return 'Cut and Sew'.\n\n"
        "Available manufacturing processes:\n"
        + "\n- ".join(available_processes) +
        "\n\n"
        "Return the processes as a list in the order they would occur.\n"
        "Example: For a fabric softener, return ['Homecare Manufacturing & Processing']"
    )

    messages = [
        {
            "role": "system",
            "content": system_prompt,
        },
        {
            "role": "user",
            "content": f"Product Category: {product_category}\nProduct Name: {product_name}",
        }
    ]

    # Use structured completion
    result = get_structured_completion(
        messages=messages,
        response_model=ManufacturingProcessesResponse,
        deployment=config.azure_openai_deployment,
        temperature=0,
    )

    if result:
        # Validate that returned processes are in the available list
        valid_processes = []
        for process in result.processes:
            if process in available_processes:
                valid_processes.append(process)
            else:
                logger.warning(f"Invalid process returned: {process}")
        
        return valid_processes if valid_processes else None
    
    return None