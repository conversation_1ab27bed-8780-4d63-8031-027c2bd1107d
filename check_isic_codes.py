#!/usr/bin/env python3
"""
Quick script to check what ISIC codes are available in our database
"""
import sys
sys.path.append('/home/<USER>/app')

from emissions_factor_matching.dataset import efs_with_geographies
import pandas as pd

def check_isic_codes():
    print("=== ISIC Codes Available in Database ===\n")
    
    # Check what columns exist
    print("Available columns:")
    for col in efs_with_geographies.columns:
        if 'isic' in col.lower() or 'ISIC' in col:
            print(f"  - {col}")
    print()
    
    # Check ISIC Sections
    if 'ISIC Section' in efs_with_geographies.columns:
        print("ISIC Sections available:")
        sections = efs_with_geographies['ISIC Section'].value_counts()
        for section, count in sections.items():
            print(f"  {section}: {count} activities")
        print()
    
    # Check ISIC Classifications  
    if 'ISIC Classification' in efs_with_geographies.columns:
        print("ISIC Classifications (first 20):")
        classifications = efs_with_geographies['ISIC Classification'].value_counts().head(20)
        for classification, count in classifications.items():
            print(f"  {classification}: {count} activities")
        print(f"... and {len(efs_with_geographies['ISIC Classification'].unique()) - 20} more")
        print()
        
        # Show transport-related ISIC codes specifically
        print("Transport-related ISIC codes (H section):")
        transport_codes = efs_with_geographies[
            efs_with_geographies['ISIC Section'] == 'H - Transportation and storage'
        ]['ISIC Classification'].value_counts()
        for code, count in transport_codes.items():
            print(f"  {code}: {count} activities")
        print()
        
        # Show manufacturing codes (C section)
        print("Manufacturing ISIC codes (C section - first 10):")
        manufacturing_codes = efs_with_geographies[
            efs_with_geographies['ISIC Section'] == 'C - Manufacturing'
        ]['ISIC Classification'].value_counts().head(10)
        for code, count in manufacturing_codes.items():
            print(f"  {code}: {count} activities")
        print()
    
    # Total counts
    print(f"Total activities in database: {len(efs_with_geographies)}")
    print(f"Unique ISIC sections: {len(efs_with_geographies['ISIC Section'].unique())}")
    print(f"Unique ISIC classifications: {len(efs_with_geographies['ISIC Classification'].unique())}")

if __name__ == "__main__":
    check_isic_codes()
