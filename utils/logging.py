from datetime import datetime
from loguru import logger

class Logger:
    def __init__(self) -> None:
        self.logger = logger

        current_date = datetime.now().strftime("%Y%m%d")
        log_file_path = f"logs/{current_date}.log"

        self.logger.add(
            log_file_path,
            rotation="100 MB",
            retention="1 day",
            compression="zip",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {module}:{function}:{line} | {message}",
        )

    def get_logger(self):
        return self.logger

logger = Logger().get_logger()
