import asyncio
import json
from functools import wraps
from typing import Any, Dict, List, Optional, Callable

import diskcache
import redis
from pydantic import BaseModel
from config import config
from utils import logger


def make_serializable(obj: Any) -> Dict[str, Any] | List[Any] | Any:
    """
    Recursively convert Pydantic objects and other non-JSON-serializable objects
    into a JSON-serializable form.

    Args:
        obj: Any Python object to be made serializable

    Returns:
        Dict[str, Any] | List[Any] | Any: A JSON-serializable version of the input where:
            - Pydantic models are converted to dict via model_dump()
            - Lists/tuples are converted to lists with serializable elements
            - Dicts have serializable keys and values
            - Other objects are returned as-is
    """
    if isinstance(obj, BaseModel):
        # Convert Pydantic model to dict.
        return obj.model_dump()
    elif isinstance(obj, (list, tuple)):
        # Process each element in the list or tuple.
        return [make_serializable(item) for item in obj]
    elif isinstance(obj, dict):
        # Process dictionary keys and values.
        return {k: make_serializable(v) for k, v in obj.items()}
    else:
        # Assume the object is already JSON-serializable.
        return obj


def serialize_args_kwargs(args: tuple, kwargs: dict) -> str:
    """
    Create a canonical JSON string from function arguments.

    Args:
        args: Tuple of positional arguments
        kwargs: Dictionary of keyword arguments

    Returns:
        str: A JSON string representation of the arguments with sorted keys
    """
    serializable_data = {
        "args": make_serializable(args),
        "kwargs": make_serializable(kwargs),
    }
    # Sort keys to ensure consistent ordering.
    return json.dumps(serializable_data, sort_keys=True)


class BaseCache:
    """Base cache interface that all cache implementations must follow."""

    def get(self, key: str, default: Any = None) -> Any:
        """
        Get a value from the cache.

        Args:
            key: Cache key to retrieve
            default: Value to return if key is not found

        Returns:
            Any: The cached value if found, otherwise the default value

        Raises:
            NotImplementedError: This is an abstract method
        """
        raise NotImplementedError

    def set(self, key: str, value: Any, expire: Optional[int] = None) -> None:
        """
        Set a value in the cache.

        Args:
            key: Cache key to set
            value: Value to cache
            expire: Optional expiration time in seconds

        Returns:
            None

        Raises:
            NotImplementedError: This is an abstract method
        """
        raise NotImplementedError

    def clear(self) -> None:
        """
        Clear all items from the cache.

        Returns:
            None

        Raises:
            NotImplementedError: This is an abstract method
        """
        raise NotImplementedError

    def volume(self) -> int:
        """
        Get the current volume of the cache in bytes.

        Returns:
            int: Size of the cache in bytes

        Raises:
            NotImplementedError: This is an abstract method
        """
        return None

    @property
    def directory(self) -> str:
        """
        Get the cache directory path.

        Returns:
            str: Path to the cache directory

        Raises:
            NotImplementedError: This is an abstract method
        """
        return None


class DiskCache(BaseCache):
    """
    Disk-based cache implementation using diskcache.

    Note: This cache implementation is intended for development and testing environment only.
    For production environments, use RedisCache instead.
    """

    def __init__(self, cache_dir: Optional[str] = None, prefix: Optional[str] = None, size_limit: int = 2 * (1024**3)) -> None:
        """
        Initialize a disk-based cache.

        Args:
            cache_dir: Optional directory path for cache storage
            prefix: Optional prefix for cache keys
            size_limit: Maximum size of cache in bytes (default 2GB)

        Returns:
            None
        """
        if cache_dir is None:
            cache_dir = config.cache_dir
        self.cache = diskcache.Cache(cache_dir, prefix=prefix, size_limit=size_limit)

    def get(self, key: str, default: Any = None) -> Any:
        """Get a value from the disk cache."""
        return self.cache.get(key, default=default)

    def set(self, key: str, value: Any, expire: Optional[int] = None) -> None:
        """Set a value in the disk cache with optional expiration."""
        self.cache.set(key, value, expire=expire)

    def clear(self) -> None:
        """Clear all items from the disk cache."""
        self.cache.clear()

    def volume(self) -> int:
        """Get the size of the disk cache in bytes."""
        return self.cache.volume()

    def __len__(self) -> int:
        """Get the number of items in the disk cache."""
        return len(self.cache)

    @property
    def directory(self) -> str:
        """Get the directory where the disk cache is stored."""
        return self.cache.directory


class RedisCache(BaseCache):
    """Redis-based cache implementation."""

    def __init__(self, redis_url: Optional[str] = None, prefix: Optional[str] = None):
        """
        Initialize a Redis cache with optional prefix.
        Args:
            redis_url: Redis connection URL
            prefix: Optional prefix for cache keys
        """
        if redis_url is None:
            redis_url = config.redis_url

        self.prefix = prefix or ""
        self.redis_client = redis.from_url(redis_url)
        self._directory = f"redis://{self.redis_client.connection_pool.connection_kwargs.get('host', 'localhost')}:{self.redis_client.connection_pool.connection_kwargs.get('port', 6379)}/{self.redis_client.connection_pool.connection_kwargs.get('db', 0)}"

    def _prefixed_key(self, key: str) -> str:
        """Add prefix to key if prefix is set."""
        return f"{self.prefix}:{key}" if self.prefix else key

    def _get_all_keys(self):
        """
        Get all keys with the current prefix.
        Note: This uses KEYS command which can be expensive in production Redis instances.
        Use with caution on large datasets.
        """
        logger.warning("_get_all_keys called on RedisCache - this operation can be expensive")
        if self.prefix:
            return self.redis_client.keys(f"{self.prefix}:*")
        return self.redis_client.keys("*")

    def get(self, key: str, default: Any = None, refresh_expiry: Optional[int] = None) -> Any:
        """
        Get a value from the Redis cache.

        Args:
            key: Cache key to retrieve
            default: Default value to return if key not found
            refresh_expiry: If provided (as integer), reset the key's expiry to this many seconds when accessed
        """
        prefixed_key = self._prefixed_key(key)
        value = self.redis_client.get(prefixed_key)

        if value is None:
            return default

        if refresh_expiry is not None:
            self.redis_client.expire(prefixed_key, refresh_expiry)
            logger.debug(f"Reset expiry for {key} to {refresh_expiry}s")

        # Parse the cached value as JSON
        # All cached data should be valid JSON since it's serialized with json.dumps() when stored
        return json.loads(value)

    def set(self, key: str, value: Any, expire: Optional[int] = None) -> None:
        """Set a value in the Redis cache with optional expiration."""
        prefixed_key = self._prefixed_key(key)

        # Always serialize value to JSON for consistency
        # This ensures all cached data can be parsed with json.loads() on retrieval
        value = json.dumps(make_serializable(value))

        if expire:
            self.redis_client.setex(prefixed_key, expire, value)
        else:
            self.redis_client.set(prefixed_key, value)

    def clear(self) -> None:
        """Clear all items from the Redis cache with the current prefix."""
        keys = self._get_all_keys()
        if keys:
            self.redis_client.delete(*keys)

    @property
    def directory(self) -> str:
        """Get a string representation of the Redis connection."""
        return self._directory


def create_cache(
    cache_type: str = "auto",
    cache_dir: Optional[str] = None,
    prefix: Optional[str] = None,
    size_limit: int = 2 * (1024**3)
) -> BaseCache:
    """
    Create a cache instance based on specified parameters.

    Args:
        cache_type: Type of cache to create ("auto", "disk", or "redis")
        cache_dir: Optional directory path for disk cache
        prefix: Optional prefix for cache keys
        size_limit: Maximum size limit for disk cache in bytes

    Returns:
        BaseCache: An instance of either DiskCache or RedisCache
    """
    if cache_type == "auto":
        if config.redis_url:
            cache_type = "redis"
        else:
            cache_type = "disk"

    if cache_type == "redis":
        if not config.redis_url:
            raise ValueError("Redis URL not configured. Please set REDIS_URL env.")
        return RedisCache(redis_url=config.redis_url, prefix=prefix)

    return DiskCache(cache_dir=cache_dir, prefix=prefix, size_limit=size_limit)


def cached(cache: BaseCache, expire: Optional[int] = None, refresh_expiry: bool = True, result_factory: Optional[Callable[[Dict], Any]] = None) -> Callable:
    """
    Decorator for caching function results.

    Args:
        cache: Cache instance to use
        expire: Cache expiration time in seconds. If None, uses config.cache_expiry
        refresh_expiry: If True, reset expiry time for cache hits on redis
        result_factory: Optional callable (e.g., a Pydantic model) to reconstruct the object from the cached dict.
    """
    # Use config.cache_expiry when expiry is not specified
    if expire is None:
        expire = config.cache_expiry

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Create a cache key from function name and arguments
            key_raw = serialize_args_kwargs(args, kwargs)
            key = f"{func.__name__}:{key_raw}"

            # Try to get result from cache
            cached_data = None
            if refresh_expiry and isinstance(cache, RedisCache):
                cached_data = cache.get(key, default=None, refresh_expiry=expire)
            else:
                cached_data = cache.get(key, default=None)

            if cached_data is not None:
                logger.info(f"Cache hit for {key}")
                # Reconstruct the object if a factory is provided and data is a dict
                if result_factory and isinstance(cached_data, dict):
                    try:
                        return result_factory(**cached_data)
                    except Exception as e:
                        logger.error(f"Failed to reconstruct object from cache for key {key} using {result_factory}: {e}")
                        # Fallback to re-fetching if reconstruction fails
                else:
                    return cached_data # Return raw data if no factory or not a dict

            # If not in cache, call the function
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)

            # Cache only if the result is not None, not empty string or empty list
            if result is not None and result != "" and result != [] and result != {}:
                # Cache the result
                cache.set(key, result, expire=expire)
                logger.info(f"Cache miss for {key}, cached result with expiry {expire}s")
            else:
                logger.info(f"Cache miss for {key}. But not caching result because it's empty")
            return result

        return wrapper

    return decorator
