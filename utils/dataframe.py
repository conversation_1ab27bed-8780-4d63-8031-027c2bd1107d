import pandas as pd


def get_dataframe(file_path: str, content_type: str) -> pd.DataFrame:
    if content_type == "text/csv":
        return pd.read_csv(file_path)
    elif content_type == "text/tab-separated-values":
        return pd.read_csv(file_path, sep="\t")
    elif content_type == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
        return pd.read_excel(file_path)
    else:
        raise ValueError(f"Unsupported content type: {content_type}")
