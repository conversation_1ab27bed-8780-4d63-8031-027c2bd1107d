FROM python:3.10-slim

WORKDIR /app

COPY dependencies/linux/setup.sh .

RUN chmod +x ./setup.sh
RUN ./setup.sh

RUN useradd --create-home appuser
WORKDIR /home/<USER>

COPY dependencies/linux/requirements.txt /requirements.txt

RUN pip install --no-deps -r /requirements.txt || pip install -r /requirements.txt --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org

USER appuser

WORKDIR /home/<USER>/app

ENV PORT=5000

COPY . .

EXPOSE $PORT

CMD ["python", "main.py"]
