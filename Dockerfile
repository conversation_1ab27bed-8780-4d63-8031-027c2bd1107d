FROM python:3.10-slim

WORKDIR /app

COPY dependencies/linux/setup.sh .

RUN chmod +x ./setup.sh
RUN ./setup.sh

RUN useradd --create-home appuser
WORKDIR /home/<USER>

COPY dependencies/linux/requirements.txt /requirements.txt

# Disable pip hash checking and install requirements
ENV PIP_DISABLE_PIP_VERSION_CHECK=1
ENV PIP_NO_CACHE_DIR=1
RUN pip install --upgrade pip && \
    pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org -r /requirements.txt

USER appuser

WORKDIR /home/<USER>/app

ENV PORT=5000

COPY . .

EXPOSE $PORT

CMD ["python", "main.py"]
