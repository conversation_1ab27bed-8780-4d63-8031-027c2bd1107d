2025-05-22 18:29:34.349 | INFO     | emissions_factor_matching.api:get_recommended_activities:206 - ajith_aravind ML Service request: material='Hexafluoroethane-based polymer for aerospace thermal insulation', iso=GLO, api_version=latest
2025-05-22 18:29:34.486 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:29:38.771 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 4287.72ms, model=gpt-4o, tokensΓëê118+1
2025-05-22 18:29:38.773 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:29:38.805 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:29:38.822 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:29:39.988 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1166.42ms, model=gpt-4o, tokensΓëê118+1
2025-05-22 18:29:39.989 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:29:39.993 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:29:40.000 | INFO     | emissions_factor_matching.api:get_recommended_activities:211 - ajith_aravind CAS number: NONE
2025-05-22 18:29:40.011 | INFO     | emissions_factor_matching.predictions:predict_input_category:93 - ajith_aravind Classifying input: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:29:40.017 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given an input, determine whether the input is a c...', model=gpt-4o
2025-05-22 18:29:41.882 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1865.30ms, model=gpt-4o, tokensΓëê43+1
2025-05-22 18:29:41.884 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: PRODUCT
2025-05-22 18:29:41.888 | INFO     | emissions_factor_matching.predictions:predict_input_category:121 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:29:41.893 | INFO     | emissions_factor_matching.api:get_recommended_activities:221 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:29:41.901 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:127 - ajith_aravind Determining constituents for product: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:29:41.903 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a product, determine the primary material of...', model=gpt-4o
2025-05-22 18:29:42.694 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 791.50ms, model=gpt-4o, tokensΓëê44+12
2025-05-22 18:29:42.696 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: Primary material: Hexafluoroethane-based polymer.
2025-05-22 18:29:42.701 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:153 - ajith_aravind Identified constituents: 'Primary material: Hexafluoroethane-based polymer.'
2025-05-22 18:29:42.705 | INFO     | emissions_factor_matching.api:get_recommended_activities:231 - ajith_aravind Product: 'Hexafluoroethane-based polymer for aerospace thermal insulation', constituent: 'Primary material: Hexafluoroethane-based polymer.'
2025-05-22 18:29:42.706 | INFO     | emissions_factor_matching.api:get_recommended_activities:232 - ajith_aravind Enriched query: 'Primary material: Hexafluoroethane-based polymer. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:29:42.707 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:194 - ajith_aravind Predicting ISIC section for product: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:29:42.709 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:29:43.485 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 776.08ms, model=gpt-4o, tokensΓëê213+0
2025-05-22 18:29:43.487 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:29:43.492 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:29:43.495 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:29:44.258 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 762.70ms, model=gpt-4o, tokensΓëê213+0
2025-05-22 18:29:44.260 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:29:44.263 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:29:44.266 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:243 - ajith_aravind Predicted ISIC section: C - Manufacturing
2025-05-22 18:29:44.271 | INFO     | emissions_factor_matching.api:get_recommended_activities:253 - ajith_aravind ISIC section filter applied: C - Manufacturing
2025-05-22 18:29:44.272 | INFO     | emissions_factor_matching.api:get_recommended_activities:288 - ajith_aravind Final query filters: {"$and": [{"activity_type": {"$eq": "ordinary transforming activity"}}, {"isic_section": {"$eq": "C - Manufacturing"}}]}
2025-05-22 18:29:44.274 | INFO     | emissions_factor_matching.api:get_recommended_activities:291 - ajith_aravind Executing vector search with query: 'Primary material: Hexafluoroethane-based polymer. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:30:43.438 | INFO     | emissions_factor_matching.api:get_recommended_activities:300 - ajith_aravind Vector search completed in 59163.08ms, returned 125 results
2025-05-22 18:30:43.439 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:302 - ajith_aravind Top 5 vector search results: ['hexafluoroethane production, from fluorination of tetrafluoroethane', 'hexafluoroethane production, from fluorination of tetrafluoroethane', 'polystyrene production, extruded, HFC-152a blown', 'polyurethane production, flexible foam, TDI-based, flame retardant', 'polyurethane production, flexible foam, TDI-based, high density']
2025-05-22 18:30:43.440 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:304 - ajith_aravind Top 5 similarity scores: [0.24572835862636566, 0.24572835862636566, 0.26604539155960083, 0.278313547372818, 0.2791008949279785]
2025-05-22 18:30:43.441 | INFO     | emissions_factor_matching.api:get_recommended_activities:324 - ajith_aravind Re-ranking 125 candidates using LLM
2025-05-22 18:30:43.443 | INFO     | emissions_factor_matching.predictions:get_closest_match:252 - ajith_aravind Re-ranking 125 candidates for 'Primary material: Hexafluoroethane-based polymer. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:30:43.519 | INFO     | completions:_get_async_completion:100 - ajith_aravind LLM call initiated: purpose='Given a list of emissions activities by the user d...', model=gpt-4o
2025-05-22 18:30:47.692 | INFO     | completions:_get_async_completion:119 - ajith_aravind LLM call completed in 4172.95ms, model=gpt-4o, tokensΓëê23796+123
2025-05-22 18:30:47.694 | DEBUG    | completions:_get_async_completion:120 - ajith_aravind Completion: ```json
2025-05-22 18:30:47.954 | ERROR    | emissions_factor_matching.predictions:get_closest_match:314 - ajith_aravind Failed to parse LLM response: Expecting value: line 1 column 1 (char 0)
2025-05-22 18:30:49.166 | ERROR    | emissions_factor_matching.predictions:get_closest_match:316 - ajith_aravind Raw LLM response: ```json
2025-05-22 18:30:49.191 | ERROR    | emissions_factor_matching.api:get_recommended_activities:334 - ajith_aravind LLM re-ranking failed to return a match
2025-05-22 18:35:21.303 | INFO     | emissions_factor_matching.api:get_recommended_activities:206 - ajith_aravind ML Service request: material='Hexafluoroethane-based polymer for aerospace thermal insulation', iso=GLO, api_version=latest
2025-05-22 18:35:21.355 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:35:24.463 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 3108.29ms, model=gpt-4o, tokensΓëê118+1
2025-05-22 18:35:24.464 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:35:24.482 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:35:24.491 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:35:25.501 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1009.77ms, model=gpt-4o, tokensΓëê118+1
2025-05-22 18:35:25.503 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:35:25.505 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:35:25.507 | INFO     | emissions_factor_matching.api:get_recommended_activities:211 - ajith_aravind CAS number: NONE
2025-05-22 18:35:25.515 | INFO     | emissions_factor_matching.predictions:predict_input_category:93 - ajith_aravind Classifying input: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:35:25.517 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given an input, determine whether the input is a c...', model=gpt-4o
2025-05-22 18:35:27.237 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1720.09ms, model=gpt-4o, tokensΓëê43+1
2025-05-22 18:35:27.239 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: PRODUCT
2025-05-22 18:35:27.243 | INFO     | emissions_factor_matching.predictions:predict_input_category:121 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:35:27.245 | INFO     | emissions_factor_matching.api:get_recommended_activities:221 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:35:27.247 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:127 - ajith_aravind Determining constituents for product: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:35:27.249 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a product, determine the primary material of...', model=gpt-4o
2025-05-22 18:35:28.622 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1373.45ms, model=gpt-4o, tokensΓëê44+15
2025-05-22 18:35:28.624 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: The primary material is a **hexafluoroethane-based polymer**.
2025-05-22 18:35:28.628 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:153 - ajith_aravind Identified constituents: 'The primary material is a **hexafluoroethane-based polymer**.'
2025-05-22 18:35:28.631 | INFO     | emissions_factor_matching.api:get_recommended_activities:231 - ajith_aravind Product: 'Hexafluoroethane-based polymer for aerospace thermal insulation', constituent: 'The primary material is a **hexafluoroethane-based polymer**.'
2025-05-22 18:35:28.632 | INFO     | emissions_factor_matching.api:get_recommended_activities:232 - ajith_aravind Enriched query: 'The primary material is a **hexafluoroethane-based polymer**. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:35:28.637 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:194 - ajith_aravind Predicting ISIC section for product: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:35:28.642 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:35:29.746 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1104.28ms, model=gpt-4o, tokensΓëê213+0
2025-05-22 18:35:29.748 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:35:29.752 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:35:29.755 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:35:30.565 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 809.98ms, model=gpt-4o, tokensΓëê213+0
2025-05-22 18:35:30.566 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:35:30.567 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:35:30.568 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:243 - ajith_aravind Predicted ISIC section: C - Manufacturing
2025-05-22 18:35:30.569 | INFO     | emissions_factor_matching.api:get_recommended_activities:253 - ajith_aravind ISIC section filter applied: C - Manufacturing
2025-05-22 18:35:30.570 | INFO     | emissions_factor_matching.api:get_recommended_activities:288 - ajith_aravind Final query filters: {"$and": [{"activity_type": {"$eq": "ordinary transforming activity"}}, {"isic_section": {"$eq": "C - Manufacturing"}}]}
2025-05-22 18:35:30.572 | INFO     | emissions_factor_matching.api:get_recommended_activities:291 - ajith_aravind Executing vector search with query: 'The primary material is a **hexafluoroethane-based polymer**. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:36:25.106 | INFO     | emissions_factor_matching.api:get_recommended_activities:300 - ajith_aravind Vector search completed in 54532.52ms, returned 125 results
2025-05-22 18:36:25.109 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:302 - ajith_aravind Top 5 vector search results: ['hexafluoroethane production, from fluorination of tetrafluoroethane', 'hexafluoroethane production, from fluorination of tetrafluoroethane', 'polystyrene production, extruded, HFC-152a blown', 'polyurethane production, flexible foam, TDI-based, high density', 'polyurethane production, flexible foam, TDI-based, flame retardant']
2025-05-22 18:36:25.110 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:304 - ajith_aravind Top 5 similarity scores: [0.25423571467399597, 0.25423571467399597, 0.2608909606933594, 0.2712603807449341, 0.2743716239929199]
2025-05-22 18:36:25.111 | INFO     | emissions_factor_matching.api:get_recommended_activities:324 - ajith_aravind Re-ranking 125 candidates using LLM
2025-05-22 18:36:25.112 | INFO     | emissions_factor_matching.predictions:get_closest_match:252 - ajith_aravind Re-ranking 125 candidates for 'The primary material is a **hexafluoroethane-based polymer**. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:36:25.175 | INFO     | completions:_get_async_completion:100 - ajith_aravind LLM call initiated: purpose='Given a list of emissions activities by the user d...', model=gpt-4o
2025-05-22 18:36:30.215 | INFO     | completions:_get_async_completion:119 - ajith_aravind LLM call completed in 5040.79ms, model=gpt-4o, tokensΓëê23751+96
2025-05-22 18:36:30.218 | DEBUG    | completions:_get_async_completion:120 - ajith_aravind Completion: ```json
2025-05-22 18:36:30.477 | ERROR    | emissions_factor_matching.predictions:get_closest_match:314 - ajith_aravind Failed to parse LLM response: Expecting value: line 1 column 1 (char 0)
2025-05-22 18:36:31.197 | ERROR    | emissions_factor_matching.predictions:get_closest_match:316 - ajith_aravind Raw LLM response: ```json
2025-05-22 18:36:31.205 | ERROR    | emissions_factor_matching.api:get_recommended_activities:334 - ajith_aravind LLM re-ranking failed to return a match
2025-05-22 18:36:34.027 | INFO     | emissions_factor_matching.api:get_recommended_activities:206 - ajith_aravind ML Service request: material='Hexafluoroethane-based polymer for aerospace thermal insulation', iso=GLO, api_version=latest
2025-05-22 18:36:34.029 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:36:36.168 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 2138.81ms, model=gpt-4o, tokensΓëê118+3
2025-05-22 18:36:36.175 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: CAS No.: NONE
2025-05-22 18:36:36.177 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:36:36.179 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:36:37.478 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1298.81ms, model=gpt-4o, tokensΓëê118+1
2025-05-22 18:36:37.480 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:36:37.483 | WARNING  | completions:get_chat_completion:279 - ajith_aravind Validation #1 did not match chat completion, returning None
2025-05-22 18:36:37.485 | INFO     | emissions_factor_matching.api:get_recommended_activities:211 - ajith_aravind CAS number: None
2025-05-22 18:36:37.486 | INFO     | emissions_factor_matching.predictions:predict_input_category:93 - ajith_aravind Classifying input: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:36:37.489 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given an input, determine whether the input is a c...', model=gpt-4o
2025-05-22 18:36:39.686 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 2197.09ms, model=gpt-4o, tokensΓëê43+1
2025-05-22 18:36:39.688 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: PRODUCT
2025-05-22 18:36:39.690 | INFO     | emissions_factor_matching.predictions:predict_input_category:121 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:36:39.692 | INFO     | emissions_factor_matching.api:get_recommended_activities:221 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:36:39.693 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:127 - ajith_aravind Determining constituents for product: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:36:39.696 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a product, determine the primary material of...', model=gpt-4o
2025-05-22 18:36:42.256 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 2559.87ms, model=gpt-4o, tokensΓëê44+15
2025-05-22 18:36:42.258 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: The primary material is a **hexafluoroethane-based polymer**.
2025-05-22 18:36:42.260 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:153 - ajith_aravind Identified constituents: 'The primary material is a **hexafluoroethane-based polymer**.'
2025-05-22 18:36:42.262 | INFO     | emissions_factor_matching.api:get_recommended_activities:231 - ajith_aravind Product: 'Hexafluoroethane-based polymer for aerospace thermal insulation', constituent: 'The primary material is a **hexafluoroethane-based polymer**.'
2025-05-22 18:36:42.263 | INFO     | emissions_factor_matching.api:get_recommended_activities:232 - ajith_aravind Enriched query: 'The primary material is a **hexafluoroethane-based polymer**. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:36:42.267 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:194 - ajith_aravind Predicting ISIC section for product: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:36:42.270 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:36:42.990 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 719.98ms, model=gpt-4o, tokensΓëê213+0
2025-05-22 18:36:42.992 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:36:42.994 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:36:42.997 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:36:43.803 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 806.63ms, model=gpt-4o, tokensΓëê213+0
2025-05-22 18:36:43.805 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:36:43.806 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:36:43.807 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:243 - ajith_aravind Predicted ISIC section: C - Manufacturing
2025-05-22 18:36:43.808 | INFO     | emissions_factor_matching.api:get_recommended_activities:253 - ajith_aravind ISIC section filter applied: C - Manufacturing
2025-05-22 18:36:43.809 | INFO     | emissions_factor_matching.api:get_recommended_activities:288 - ajith_aravind Final query filters: {"$and": [{"activity_type": {"$eq": "ordinary transforming activity"}}, {"isic_section": {"$eq": "C - Manufacturing"}}]}
2025-05-22 18:36:43.810 | INFO     | emissions_factor_matching.api:get_recommended_activities:291 - ajith_aravind Executing vector search with query: 'The primary material is a **hexafluoroethane-based polymer**. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:36:45.775 | INFO     | emissions_factor_matching.api:get_recommended_activities:300 - ajith_aravind Vector search completed in 1964.11ms, returned 125 results
2025-05-22 18:36:45.776 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:302 - ajith_aravind Top 5 vector search results: ['hexafluoroethane production, from fluorination of tetrafluoroethane', 'hexafluoroethane production, from fluorination of tetrafluoroethane', 'polystyrene production, extruded, HFC-152a blown', 'polyurethane production, flexible foam, TDI-based, high density', 'polyurethane production, flexible foam, TDI-based, flame retardant']
2025-05-22 18:36:45.777 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:304 - ajith_aravind Top 5 similarity scores: [0.25423571467399597, 0.25423571467399597, 0.2608909606933594, 0.2712603807449341, 0.2743716239929199]
2025-05-22 18:36:45.778 | INFO     | emissions_factor_matching.api:get_recommended_activities:324 - ajith_aravind Re-ranking 125 candidates using LLM
2025-05-22 18:36:45.779 | INFO     | emissions_factor_matching.predictions:get_closest_match:252 - ajith_aravind Re-ranking 125 candidates for 'The primary material is a **hexafluoroethane-based polymer**. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:36:45.803 | INFO     | completions:_get_async_completion:100 - ajith_aravind LLM call initiated: purpose='Given a list of emissions activities by the user d...', model=gpt-4o
2025-05-22 18:36:50.012 | INFO     | completions:_get_async_completion:119 - ajith_aravind LLM call completed in 4208.58ms, model=gpt-4o, tokensΓëê23751+97
2025-05-22 18:36:50.014 | DEBUG    | completions:_get_async_completion:120 - ajith_aravind Completion: ```json
2025-05-22 18:36:50.266 | ERROR    | emissions_factor_matching.predictions:get_closest_match:314 - ajith_aravind Failed to parse LLM response: Expecting value: line 1 column 1 (char 0)
2025-05-22 18:36:50.277 | ERROR    | emissions_factor_matching.predictions:get_closest_match:316 - ajith_aravind Raw LLM response: ```json
2025-05-22 18:36:50.289 | ERROR    | emissions_factor_matching.api:get_recommended_activities:334 - ajith_aravind LLM re-ranking failed to return a match
2025-05-22 18:36:54.386 | INFO     | emissions_factor_matching.api:get_recommended_activities:206 - ajith_aravind ML Service request: material='Hexafluoroethane-based polymer for aerospace thermal insulation', iso=GLO, api_version=latest
2025-05-22 18:36:54.388 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:36:56.380 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1991.82ms, model=gpt-4o, tokensΓëê118+1
2025-05-22 18:36:56.381 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:36:56.382 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:36:56.384 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:36:57.247 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 863.27ms, model=gpt-4o, tokensΓëê118+1
2025-05-22 18:36:57.249 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:36:57.251 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:36:57.254 | INFO     | emissions_factor_matching.api:get_recommended_activities:211 - ajith_aravind CAS number: NONE
2025-05-22 18:36:57.255 | INFO     | emissions_factor_matching.predictions:predict_input_category:93 - ajith_aravind Classifying input: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:36:57.257 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given an input, determine whether the input is a c...', model=gpt-4o
2025-05-22 18:36:58.893 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1635.71ms, model=gpt-4o, tokensΓëê43+1
2025-05-22 18:36:58.895 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: PRODUCT
2025-05-22 18:36:58.898 | INFO     | emissions_factor_matching.predictions:predict_input_category:121 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:36:58.899 | INFO     | emissions_factor_matching.api:get_recommended_activities:221 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:36:58.902 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:127 - ajith_aravind Determining constituents for product: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:36:58.905 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a product, determine the primary material of...', model=gpt-4o
2025-05-22 18:36:59.948 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1042.82ms, model=gpt-4o, tokensΓëê44+15
2025-05-22 18:36:59.950 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: The primary material is a **hexafluoroethane-based polymer**.
2025-05-22 18:36:59.952 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:153 - ajith_aravind Identified constituents: 'The primary material is a **hexafluoroethane-based polymer**.'
2025-05-22 18:36:59.954 | INFO     | emissions_factor_matching.api:get_recommended_activities:231 - ajith_aravind Product: 'Hexafluoroethane-based polymer for aerospace thermal insulation', constituent: 'The primary material is a **hexafluoroethane-based polymer**.'
2025-05-22 18:36:59.958 | INFO     | emissions_factor_matching.api:get_recommended_activities:232 - ajith_aravind Enriched query: 'The primary material is a **hexafluoroethane-based polymer**. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:36:59.961 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:194 - ajith_aravind Predicting ISIC section for product: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:36:59.964 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:37:00.819 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 855.23ms, model=gpt-4o, tokensΓëê213+0
2025-05-22 18:37:00.821 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:37:00.823 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:37:00.824 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:37:02.565 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1740.28ms, model=gpt-4o, tokensΓëê213+0
2025-05-22 18:37:02.567 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:37:02.568 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:37:02.570 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:243 - ajith_aravind Predicted ISIC section: C - Manufacturing
2025-05-22 18:37:02.572 | INFO     | emissions_factor_matching.api:get_recommended_activities:253 - ajith_aravind ISIC section filter applied: C - Manufacturing
2025-05-22 18:37:02.574 | INFO     | emissions_factor_matching.api:get_recommended_activities:288 - ajith_aravind Final query filters: {"$and": [{"activity_type": {"$eq": "ordinary transforming activity"}}, {"isic_section": {"$eq": "C - Manufacturing"}}]}
2025-05-22 18:37:02.575 | INFO     | emissions_factor_matching.api:get_recommended_activities:291 - ajith_aravind Executing vector search with query: 'The primary material is a **hexafluoroethane-based polymer**. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:37:04.438 | INFO     | emissions_factor_matching.api:get_recommended_activities:300 - ajith_aravind Vector search completed in 1861.34ms, returned 125 results
2025-05-22 18:37:04.440 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:302 - ajith_aravind Top 5 vector search results: ['hexafluoroethane production, from fluorination of tetrafluoroethane', 'hexafluoroethane production, from fluorination of tetrafluoroethane', 'polystyrene production, extruded, HFC-152a blown', 'polyurethane production, flexible foam, TDI-based, high density', 'polyurethane production, flexible foam, TDI-based, flame retardant']
2025-05-22 18:37:04.441 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:304 - ajith_aravind Top 5 similarity scores: [0.25423571467399597, 0.25423571467399597, 0.2608909606933594, 0.2712603807449341, 0.2743716239929199]
2025-05-22 18:37:04.442 | INFO     | emissions_factor_matching.api:get_recommended_activities:324 - ajith_aravind Re-ranking 125 candidates using LLM
2025-05-22 18:37:04.443 | INFO     | emissions_factor_matching.predictions:get_closest_match:252 - ajith_aravind Re-ranking 125 candidates for 'The primary material is a **hexafluoroethane-based polymer**. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:37:04.472 | INFO     | completions:_get_async_completion:100 - ajith_aravind LLM call initiated: purpose='Given a list of emissions activities by the user d...', model=gpt-4o
2025-05-22 18:37:10.440 | INFO     | completions:_get_async_completion:119 - ajith_aravind LLM call completed in 5968.39ms, model=gpt-4o, tokensΓëê23751+100
2025-05-22 18:37:10.441 | DEBUG    | completions:_get_async_completion:120 - ajith_aravind Completion: ```json
2025-05-22 18:37:11.292 | ERROR    | emissions_factor_matching.predictions:get_closest_match:314 - ajith_aravind Failed to parse LLM response: Expecting value: line 1 column 1 (char 0)
2025-05-22 18:37:11.305 | ERROR    | emissions_factor_matching.predictions:get_closest_match:316 - ajith_aravind Raw LLM response: ```json
2025-05-22 18:37:11.320 | ERROR    | emissions_factor_matching.api:get_recommended_activities:334 - ajith_aravind LLM re-ranking failed to return a match
2025-05-22 18:37:20.336 | INFO     | emissions_factor_matching.api:get_recommended_activities:206 - ajith_aravind ML Service request: material='Hexafluoroethane-based polymer for aerospace thermal insulation', iso=GLO, api_version=latest
2025-05-22 18:37:20.339 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:37:22.288 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1948.76ms, model=gpt-4o, tokensΓëê118+1
2025-05-22 18:37:22.290 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:37:22.298 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:37:22.320 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:37:23.337 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1016.30ms, model=gpt-4o, tokensΓëê118+1
2025-05-22 18:37:23.339 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:37:23.344 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:37:23.414 | INFO     | emissions_factor_matching.api:get_recommended_activities:211 - ajith_aravind CAS number: NONE
2025-05-22 18:37:23.421 | INFO     | emissions_factor_matching.predictions:predict_input_category:93 - ajith_aravind Classifying input: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:37:23.424 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given an input, determine whether the input is a c...', model=gpt-4o
2025-05-22 18:37:25.138 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1713.20ms, model=gpt-4o, tokensΓëê43+1
2025-05-22 18:37:25.142 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: PRODUCT
2025-05-22 18:37:25.146 | INFO     | emissions_factor_matching.predictions:predict_input_category:121 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:37:25.150 | INFO     | emissions_factor_matching.api:get_recommended_activities:221 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:37:25.156 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:127 - ajith_aravind Determining constituents for product: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:37:25.158 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a product, determine the primary material of...', model=gpt-4o
2025-05-22 18:37:26.882 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1723.70ms, model=gpt-4o, tokensΓëê44+12
2025-05-22 18:37:26.884 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: Primary material: Hexafluoroethane-based polymer
2025-05-22 18:37:26.888 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:153 - ajith_aravind Identified constituents: 'Primary material: Hexafluoroethane-based polymer'
2025-05-22 18:37:26.893 | INFO     | emissions_factor_matching.api:get_recommended_activities:231 - ajith_aravind Product: 'Hexafluoroethane-based polymer for aerospace thermal insulation', constituent: 'Primary material: Hexafluoroethane-based polymer'
2025-05-22 18:37:26.898 | INFO     | emissions_factor_matching.api:get_recommended_activities:232 - ajith_aravind Enriched query: 'Primary material: Hexafluoroethane-based polymer Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:37:26.909 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:194 - ajith_aravind Predicting ISIC section for product: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:37:26.912 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:37:28.096 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1184.26ms, model=gpt-4o, tokensΓëê213+0
2025-05-22 18:37:28.099 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:37:28.102 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:37:28.105 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:37:29.262 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1157.27ms, model=gpt-4o, tokensΓëê213+0
2025-05-22 18:37:29.287 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:37:29.291 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:37:29.294 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:243 - ajith_aravind Predicted ISIC section: C - Manufacturing
2025-05-22 18:37:29.298 | INFO     | emissions_factor_matching.api:get_recommended_activities:253 - ajith_aravind ISIC section filter applied: C - Manufacturing
2025-05-22 18:37:29.299 | INFO     | emissions_factor_matching.api:get_recommended_activities:288 - ajith_aravind Final query filters: {"$and": [{"activity_type": {"$eq": "ordinary transforming activity"}}, {"isic_section": {"$eq": "C - Manufacturing"}}]}
2025-05-22 18:37:29.300 | INFO     | emissions_factor_matching.api:get_recommended_activities:291 - ajith_aravind Executing vector search with query: 'Primary material: Hexafluoroethane-based polymer Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:37:32.397 | INFO     | emissions_factor_matching.api:get_recommended_activities:300 - ajith_aravind Vector search completed in 3095.54ms, returned 125 results
2025-05-22 18:37:32.399 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:302 - ajith_aravind Top 5 vector search results: ['hexafluoroethane production, from fluorination of tetrafluoroethane', 'hexafluoroethane production, from fluorination of tetrafluoroethane', 'polystyrene production, extruded, HFC-152a blown', 'polyurethane production, rigid foam', 'polyurethane production, flexible foam, TDI-based, high density']
2025-05-22 18:37:32.401 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:304 - ajith_aravind Top 5 similarity scores: [0.25008144974708557, 0.25008144974708557, 0.26972097158432007, 0.2831781208515167, 0.2852359712123871]
2025-05-22 18:37:32.402 | INFO     | emissions_factor_matching.api:get_recommended_activities:324 - ajith_aravind Re-ranking 125 candidates using LLM
2025-05-22 18:37:32.404 | INFO     | emissions_factor_matching.predictions:get_closest_match:252 - ajith_aravind Re-ranking 125 candidates for 'Primary material: Hexafluoroethane-based polymer Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 18:37:32.443 | INFO     | completions:_get_async_completion:100 - ajith_aravind LLM call initiated: purpose='Given a list of emissions activities by the user d...', model=gpt-4o
2025-05-22 18:37:37.812 | INFO     | completions:_get_async_completion:119 - ajith_aravind LLM call completed in 5368.60ms, model=gpt-4o, tokensΓëê23814+109
2025-05-22 18:37:37.814 | DEBUG    | completions:_get_async_completion:120 - ajith_aravind Completion: ```json
2025-05-22 18:37:38.069 | ERROR    | emissions_factor_matching.predictions:get_closest_match:314 - ajith_aravind Failed to parse LLM response: Expecting value: line 1 column 1 (char 0)
2025-05-22 18:37:38.082 | ERROR    | emissions_factor_matching.predictions:get_closest_match:316 - ajith_aravind Raw LLM response: ```json
2025-05-22 18:37:38.093 | ERROR    | emissions_factor_matching.api:get_recommended_activities:334 - ajith_aravind LLM re-ranking failed to return a match
2025-05-22 18:40:53.265 | INFO     | emissions_factor_matching.api:get_recommended_activities:206 - ajith_aravind ML Service request: material='steel', iso=GLO, api_version=latest
2025-05-22 18:40:53.267 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:40:56.192 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 2925.05ms, model=gpt-4o, tokensΓëê103+1
2025-05-22 18:40:56.193 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:40:56.196 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:40:56.198 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:40:56.946 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 748.61ms, model=gpt-4o, tokensΓëê103+1
2025-05-22 18:40:56.948 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:40:56.952 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:40:56.958 | INFO     | emissions_factor_matching.api:get_recommended_activities:211 - ajith_aravind CAS number: NONE
2025-05-22 18:40:56.961 | INFO     | emissions_factor_matching.predictions:predict_input_category:93 - ajith_aravind Classifying input: 'steel'
2025-05-22 18:40:56.963 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given an input, determine whether the input is a c...', model=gpt-4o
2025-05-22 18:40:58.369 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1405.75ms, model=gpt-4o, tokensΓëê29+1
2025-05-22 18:40:58.373 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: PRODUCT
2025-05-22 18:40:58.376 | INFO     | emissions_factor_matching.predictions:predict_input_category:121 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:40:58.379 | INFO     | emissions_factor_matching.api:get_recommended_activities:221 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:40:58.382 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:127 - ajith_aravind Determining constituents for product: 'steel'
2025-05-22 18:40:58.384 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a product, determine the primary material of...', model=gpt-4o
2025-05-22 18:41:00.079 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1695.61ms, model=gpt-4o, tokensΓëê29+7
2025-05-22 18:41:00.080 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: The primary material is steel.
2025-05-22 18:41:00.085 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:153 - ajith_aravind Identified constituents: 'The primary material is steel.'
2025-05-22 18:41:00.086 | INFO     | emissions_factor_matching.api:get_recommended_activities:231 - ajith_aravind Product: 'steel', constituent: 'The primary material is steel.'
2025-05-22 18:41:00.088 | INFO     | emissions_factor_matching.api:get_recommended_activities:232 - ajith_aravind Enriched query: 'The primary material is steel. steel'
2025-05-22 18:41:00.090 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:194 - ajith_aravind Predicting ISIC section for product: 'steel'
2025-05-22 18:41:00.091 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:41:01.134 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1043.39ms, model=gpt-4o, tokensΓëê198+0
2025-05-22 18:41:01.136 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:41:01.138 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:41:01.139 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:41:02.104 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 965.22ms, model=gpt-4o, tokensΓëê198+0
2025-05-22 18:41:02.106 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:41:02.108 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:41:02.110 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:243 - ajith_aravind Predicted ISIC section: C - Manufacturing
2025-05-22 18:41:02.112 | INFO     | emissions_factor_matching.api:get_recommended_activities:253 - ajith_aravind ISIC section filter applied: C - Manufacturing
2025-05-22 18:41:02.113 | INFO     | emissions_factor_matching.api:get_recommended_activities:288 - ajith_aravind Final query filters: {"$and": [{"activity_type": {"$eq": "ordinary transforming activity"}}, {"isic_section": {"$eq": "C - Manufacturing"}}]}
2025-05-22 18:41:02.115 | INFO     | emissions_factor_matching.api:get_recommended_activities:291 - ajith_aravind Executing vector search with query: 'The primary material is steel. steel'
2025-05-22 18:41:04.746 | INFO     | emissions_factor_matching.api:get_recommended_activities:300 - ajith_aravind Vector search completed in 2630.33ms, returned 125 results
2025-05-22 18:41:04.747 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:302 - ajith_aravind Top 5 vector search results: ['reinforcing steel production', 'metal working, average for steel product manufacturing', 'steel production, electric, low-alloyed', 'steel production, electric, low-alloyed', 'steel production, chromium steel 18/8, hot rolled']
2025-05-22 18:41:04.748 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:304 - ajith_aravind Top 5 similarity scores: [0.16215269267559052, 0.1647111177444458, 0.21167069673538208, 0.21167069673538208, 0.21939167380332947]
2025-05-22 18:41:04.751 | INFO     | emissions_factor_matching.api:get_recommended_activities:324 - ajith_aravind Re-ranking 125 candidates using LLM
2025-05-22 18:41:04.752 | INFO     | emissions_factor_matching.predictions:get_closest_match:252 - ajith_aravind Re-ranking 125 candidates for 'The primary material is steel. steel'
2025-05-22 18:41:04.779 | INFO     | completions:_get_async_completion:100 - ajith_aravind LLM call initiated: purpose='Given a list of emissions activities by the user d...', model=gpt-4o
2025-05-22 18:41:09.037 | INFO     | completions:_get_async_completion:119 - ajith_aravind LLM call completed in 4257.40ms, model=gpt-4o, tokensΓëê22578+96
2025-05-22 18:41:09.038 | DEBUG    | completions:_get_async_completion:120 - ajith_aravind Completion: ```json
2025-05-22 18:41:09.293 | ERROR    | emissions_factor_matching.predictions:get_closest_match:314 - ajith_aravind Failed to parse LLM response: Expecting value: line 1 column 1 (char 0)
2025-05-22 18:41:09.300 | ERROR    | emissions_factor_matching.predictions:get_closest_match:316 - ajith_aravind Raw LLM response: ```json
2025-05-22 18:41:09.310 | ERROR    | emissions_factor_matching.api:get_recommended_activities:334 - ajith_aravind LLM re-ranking failed to return a match
2025-05-22 18:41:11.554 | INFO     | emissions_factor_matching.api:get_recommended_activities:206 - ajith_aravind ML Service request: material='steel', iso=GLO, api_version=latest
2025-05-22 18:41:11.558 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:41:13.542 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1984.13ms, model=gpt-4o, tokensΓëê103+1
2025-05-22 18:41:13.543 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:41:13.545 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:41:13.549 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:41:14.372 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 822.69ms, model=gpt-4o, tokensΓëê103+1
2025-05-22 18:41:14.373 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:41:14.376 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:41:14.381 | INFO     | emissions_factor_matching.api:get_recommended_activities:211 - ajith_aravind CAS number: NONE
2025-05-22 18:41:14.382 | INFO     | emissions_factor_matching.predictions:predict_input_category:93 - ajith_aravind Classifying input: 'steel'
2025-05-22 18:41:14.384 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given an input, determine whether the input is a c...', model=gpt-4o
2025-05-22 18:41:15.874 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1489.46ms, model=gpt-4o, tokensΓëê29+1
2025-05-22 18:41:15.875 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: PRODUCT
2025-05-22 18:41:15.880 | INFO     | emissions_factor_matching.predictions:predict_input_category:121 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:41:15.883 | INFO     | emissions_factor_matching.api:get_recommended_activities:221 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:41:15.888 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:127 - ajith_aravind Determining constituents for product: 'steel'
2025-05-22 18:41:15.889 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a product, determine the primary material of...', model=gpt-4o
2025-05-22 18:41:16.623 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 733.35ms, model=gpt-4o, tokensΓëê29+11
2025-05-22 18:41:16.625 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: Steel is primarily composed of iron and carbon.
2025-05-22 18:41:16.629 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:153 - ajith_aravind Identified constituents: 'Steel is primarily composed of iron and carbon.'
2025-05-22 18:41:16.635 | INFO     | emissions_factor_matching.api:get_recommended_activities:231 - ajith_aravind Product: 'steel', constituent: 'Steel is primarily composed of iron and carbon.'
2025-05-22 18:41:16.639 | INFO     | emissions_factor_matching.api:get_recommended_activities:232 - ajith_aravind Enriched query: 'Steel is primarily composed of iron and carbon. steel'
2025-05-22 18:41:16.642 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:194 - ajith_aravind Predicting ISIC section for product: 'steel'
2025-05-22 18:41:16.644 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:41:17.575 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 931.49ms, model=gpt-4o, tokensΓëê198+0
2025-05-22 18:41:17.577 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:41:17.581 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:41:17.586 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:41:18.263 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 676.84ms, model=gpt-4o, tokensΓëê198+0
2025-05-22 18:41:18.266 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:41:18.269 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:41:18.272 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:243 - ajith_aravind Predicted ISIC section: C - Manufacturing
2025-05-22 18:41:18.274 | INFO     | emissions_factor_matching.api:get_recommended_activities:253 - ajith_aravind ISIC section filter applied: C - Manufacturing
2025-05-22 18:41:18.276 | INFO     | emissions_factor_matching.api:get_recommended_activities:288 - ajith_aravind Final query filters: {"$and": [{"activity_type": {"$eq": "ordinary transforming activity"}}, {"isic_section": {"$eq": "C - Manufacturing"}}]}
2025-05-22 18:41:18.277 | INFO     | emissions_factor_matching.api:get_recommended_activities:291 - ajith_aravind Executing vector search with query: 'Steel is primarily composed of iron and carbon. steel'
2025-05-22 18:41:22.902 | INFO     | emissions_factor_matching.api:get_recommended_activities:300 - ajith_aravind Vector search completed in 4624.04ms, returned 125 results
2025-05-22 18:41:22.905 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:302 - ajith_aravind Top 5 vector search results: ['metal working, average for steel product manufacturing', 'steel production, electric, low-alloyed', 'steel production, electric, low-alloyed', 'reinforcing steel production', 'steel production, electric, chromium steel 18/8']
2025-05-22 18:41:22.908 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:304 - ajith_aravind Top 5 similarity scores: [0.24956488609313965, 0.2593756914138794, 0.2593756914138794, 0.2608494162559509, 0.2718929350376129]
2025-05-22 18:41:22.910 | INFO     | emissions_factor_matching.api:get_recommended_activities:324 - ajith_aravind Re-ranking 125 candidates using LLM
2025-05-22 18:41:22.911 | INFO     | emissions_factor_matching.predictions:get_closest_match:252 - ajith_aravind Re-ranking 125 candidates for 'Steel is primarily composed of iron and carbon. steel'
2025-05-22 18:41:22.952 | INFO     | completions:_get_async_completion:100 - ajith_aravind LLM call initiated: purpose='Given a list of emissions activities by the user d...', model=gpt-4o
2025-05-22 18:41:31.015 | INFO     | completions:_get_async_completion:119 - ajith_aravind LLM call completed in 8063.03ms, model=gpt-4o, tokensΓëê22776+102
2025-05-22 18:41:31.018 | DEBUG    | completions:_get_async_completion:120 - ajith_aravind Completion: ```json
2025-05-22 18:41:31.272 | ERROR    | emissions_factor_matching.predictions:get_closest_match:314 - ajith_aravind Failed to parse LLM response: Expecting value: line 1 column 1 (char 0)
2025-05-22 18:41:31.283 | ERROR    | emissions_factor_matching.predictions:get_closest_match:316 - ajith_aravind Raw LLM response: ```json
2025-05-22 18:41:31.294 | ERROR    | emissions_factor_matching.api:get_recommended_activities:334 - ajith_aravind LLM re-ranking failed to return a match
2025-05-22 18:41:35.429 | INFO     | emissions_factor_matching.api:get_recommended_activities:206 - ajith_aravind ML Service request: material='steel', iso=GLO, api_version=latest
2025-05-22 18:41:35.430 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:41:37.354 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1924.00ms, model=gpt-4o, tokensΓëê103+1
2025-05-22 18:41:37.356 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:41:37.357 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:41:37.359 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:41:38.198 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 838.66ms, model=gpt-4o, tokensΓëê103+1
2025-05-22 18:41:38.200 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:41:38.201 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:41:38.204 | INFO     | emissions_factor_matching.api:get_recommended_activities:211 - ajith_aravind CAS number: NONE
2025-05-22 18:41:38.207 | INFO     | emissions_factor_matching.predictions:predict_input_category:93 - ajith_aravind Classifying input: 'steel'
2025-05-22 18:41:38.209 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given an input, determine whether the input is a c...', model=gpt-4o
2025-05-22 18:41:39.920 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1710.14ms, model=gpt-4o, tokensΓëê29+1
2025-05-22 18:41:39.922 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: PRODUCT
2025-05-22 18:41:39.927 | INFO     | emissions_factor_matching.predictions:predict_input_category:121 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:41:39.930 | INFO     | emissions_factor_matching.api:get_recommended_activities:221 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:41:39.945 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:127 - ajith_aravind Determining constituents for product: 'steel'
2025-05-22 18:41:39.947 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a product, determine the primary material of...', model=gpt-4o
2025-05-22 18:41:40.935 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 988.47ms, model=gpt-4o, tokensΓëê29+7
2025-05-22 18:41:40.937 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: The primary material is steel.
2025-05-22 18:41:40.939 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:153 - ajith_aravind Identified constituents: 'The primary material is steel.'
2025-05-22 18:41:40.941 | INFO     | emissions_factor_matching.api:get_recommended_activities:231 - ajith_aravind Product: 'steel', constituent: 'The primary material is steel.'
2025-05-22 18:41:40.943 | INFO     | emissions_factor_matching.api:get_recommended_activities:232 - ajith_aravind Enriched query: 'The primary material is steel. steel'
2025-05-22 18:41:40.945 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:194 - ajith_aravind Predicting ISIC section for product: 'steel'
2025-05-22 18:41:40.953 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:41:41.770 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 816.79ms, model=gpt-4o, tokensΓëê198+0
2025-05-22 18:41:41.772 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:41:41.774 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:41:41.776 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:41:42.889 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1112.37ms, model=gpt-4o, tokensΓëê198+0
2025-05-22 18:41:42.892 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:41:42.894 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:41:42.896 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:243 - ajith_aravind Predicted ISIC section: C - Manufacturing
2025-05-22 18:41:42.920 | INFO     | emissions_factor_matching.api:get_recommended_activities:253 - ajith_aravind ISIC section filter applied: C - Manufacturing
2025-05-22 18:41:42.924 | INFO     | emissions_factor_matching.api:get_recommended_activities:288 - ajith_aravind Final query filters: {"$and": [{"activity_type": {"$eq": "ordinary transforming activity"}}, {"isic_section": {"$eq": "C - Manufacturing"}}]}
2025-05-22 18:41:42.926 | INFO     | emissions_factor_matching.api:get_recommended_activities:291 - ajith_aravind Executing vector search with query: 'The primary material is steel. steel'
2025-05-22 18:41:44.856 | INFO     | emissions_factor_matching.api:get_recommended_activities:300 - ajith_aravind Vector search completed in 1929.24ms, returned 125 results
2025-05-22 18:41:44.858 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:302 - ajith_aravind Top 5 vector search results: ['reinforcing steel production', 'metal working, average for steel product manufacturing', 'steel production, electric, low-alloyed', 'steel production, electric, low-alloyed', 'steel production, chromium steel 18/8, hot rolled']
2025-05-22 18:41:44.860 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:304 - ajith_aravind Top 5 similarity scores: [0.16215269267559052, 0.1647111177444458, 0.21167069673538208, 0.21167069673538208, 0.21939167380332947]
2025-05-22 18:41:44.862 | INFO     | emissions_factor_matching.api:get_recommended_activities:324 - ajith_aravind Re-ranking 125 candidates using LLM
2025-05-22 18:41:44.864 | INFO     | emissions_factor_matching.predictions:get_closest_match:252 - ajith_aravind Re-ranking 125 candidates for 'The primary material is steel. steel'
2025-05-22 18:41:44.901 | INFO     | completions:_get_async_completion:100 - ajith_aravind LLM call initiated: purpose='Given a list of emissions activities by the user d...', model=gpt-4o
2025-05-22 18:41:50.912 | INFO     | completions:_get_async_completion:119 - ajith_aravind LLM call completed in 6010.83ms, model=gpt-4o, tokensΓëê22578+101
2025-05-22 18:41:50.914 | DEBUG    | completions:_get_async_completion:120 - ajith_aravind Completion: ```json
2025-05-22 18:41:51.168 | ERROR    | emissions_factor_matching.predictions:get_closest_match:314 - ajith_aravind Failed to parse LLM response: Expecting value: line 1 column 1 (char 0)
2025-05-22 18:41:51.177 | ERROR    | emissions_factor_matching.predictions:get_closest_match:316 - ajith_aravind Raw LLM response: ```json
2025-05-22 18:41:51.192 | ERROR    | emissions_factor_matching.api:get_recommended_activities:334 - ajith_aravind LLM re-ranking failed to return a match
2025-05-22 18:41:59.651 | INFO     | emissions_factor_matching.api:get_recommended_activities:206 - ajith_aravind ML Service request: material='steel', iso=GLO, api_version=latest
2025-05-22 18:41:59.653 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:42:01.537 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1884.25ms, model=gpt-4o, tokensΓëê103+1
2025-05-22 18:42:01.539 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:42:01.541 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:42:01.543 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=gpt-4o
2025-05-22 18:42:02.474 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 930.56ms, model=gpt-4o, tokensΓëê103+1
2025-05-22 18:42:02.477 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 18:42:02.479 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:42:02.483 | INFO     | emissions_factor_matching.api:get_recommended_activities:211 - ajith_aravind CAS number: NONE
2025-05-22 18:42:02.487 | INFO     | emissions_factor_matching.predictions:predict_input_category:93 - ajith_aravind Classifying input: 'steel'
2025-05-22 18:42:02.489 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given an input, determine whether the input is a c...', model=gpt-4o
2025-05-22 18:42:04.135 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1645.26ms, model=gpt-4o, tokensΓëê29+1
2025-05-22 18:42:04.136 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: PRODUCT
2025-05-22 18:42:04.140 | INFO     | emissions_factor_matching.predictions:predict_input_category:121 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:42:04.149 | INFO     | emissions_factor_matching.api:get_recommended_activities:221 - ajith_aravind Input classified as: PRODUCT
2025-05-22 18:42:04.151 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:127 - ajith_aravind Determining constituents for product: 'steel'
2025-05-22 18:42:04.155 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a product, determine the primary material of...', model=gpt-4o
2025-05-22 18:42:05.269 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1113.18ms, model=gpt-4o, tokensΓëê29+7
2025-05-22 18:42:05.271 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: The primary material is steel.
2025-05-22 18:42:05.274 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:153 - ajith_aravind Identified constituents: 'The primary material is steel.'
2025-05-22 18:42:05.277 | INFO     | emissions_factor_matching.api:get_recommended_activities:231 - ajith_aravind Product: 'steel', constituent: 'The primary material is steel.'
2025-05-22 18:42:05.278 | INFO     | emissions_factor_matching.api:get_recommended_activities:232 - ajith_aravind Enriched query: 'The primary material is steel. steel'
2025-05-22 18:42:05.281 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:194 - ajith_aravind Predicting ISIC section for product: 'steel'
2025-05-22 18:42:05.283 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:42:06.288 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1004.97ms, model=gpt-4o, tokensΓëê198+0
2025-05-22 18:42:06.290 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:42:06.293 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 18:42:06.296 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=gpt-4o
2025-05-22 18:42:07.343 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1047.54ms, model=gpt-4o, tokensΓëê198+0
2025-05-22 18:42:07.345 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 18:42:07.349 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 18:42:07.358 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:243 - ajith_aravind Predicted ISIC section: C - Manufacturing
2025-05-22 18:42:07.362 | INFO     | emissions_factor_matching.api:get_recommended_activities:253 - ajith_aravind ISIC section filter applied: C - Manufacturing
2025-05-22 18:42:07.363 | INFO     | emissions_factor_matching.api:get_recommended_activities:288 - ajith_aravind Final query filters: {"$and": [{"activity_type": {"$eq": "ordinary transforming activity"}}, {"isic_section": {"$eq": "C - Manufacturing"}}]}
2025-05-22 18:42:07.364 | INFO     | emissions_factor_matching.api:get_recommended_activities:291 - ajith_aravind Executing vector search with query: 'The primary material is steel. steel'
2025-05-22 18:42:10.135 | INFO     | emissions_factor_matching.api:get_recommended_activities:300 - ajith_aravind Vector search completed in 2769.92ms, returned 125 results
2025-05-22 18:42:10.137 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:302 - ajith_aravind Top 5 vector search results: ['reinforcing steel production', 'metal working, average for steel product manufacturing', 'steel production, electric, low-alloyed', 'steel production, electric, low-alloyed', 'steel production, chromium steel 18/8, hot rolled']
2025-05-22 18:42:10.139 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:304 - ajith_aravind Top 5 similarity scores: [0.16215269267559052, 0.1647111177444458, 0.21167069673538208, 0.21167069673538208, 0.21939167380332947]
2025-05-22 18:42:10.141 | INFO     | emissions_factor_matching.api:get_recommended_activities:324 - ajith_aravind Re-ranking 125 candidates using LLM
2025-05-22 18:42:10.151 | INFO     | emissions_factor_matching.predictions:get_closest_match:252 - ajith_aravind Re-ranking 125 candidates for 'The primary material is steel. steel'
2025-05-22 18:42:10.232 | INFO     | completions:_get_async_completion:100 - ajith_aravind LLM call initiated: purpose='Given a list of emissions activities by the user d...', model=gpt-4o
2025-05-22 18:42:17.713 | INFO     | completions:_get_async_completion:119 - ajith_aravind LLM call completed in 7480.26ms, model=gpt-4o, tokensΓëê22578+101
2025-05-22 18:42:17.715 | DEBUG    | completions:_get_async_completion:120 - ajith_aravind Completion: ```json
2025-05-22 18:42:17.972 | ERROR    | emissions_factor_matching.predictions:get_closest_match:314 - ajith_aravind Failed to parse LLM response: Expecting value: line 1 column 1 (char 0)
2025-05-22 18:42:17.983 | ERROR    | emissions_factor_matching.predictions:get_closest_match:316 - ajith_aravind Raw LLM response: ```json
2025-05-22 18:42:17.994 | ERROR    | emissions_factor_matching.api:get_recommended_activities:334 - ajith_aravind LLM re-ranking failed to return a match
