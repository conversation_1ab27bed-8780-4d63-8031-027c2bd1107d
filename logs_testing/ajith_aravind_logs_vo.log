2025-05-22 15:28:18.394 | INFO     | emissions_factor_matching.api:get_recommended_activities:206 - ajith_aravind ML Service request: material='Hexafluoroethane-based polymer for aerospace thermal insulation', iso=GLO, api_version=latest
2025-05-22 15:28:18.395 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=3_5_turbo
2025-05-22 15:28:19.879 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1483.29ms, model=3_5_turbo, tokensΓëê118+3
2025-05-22 15:28:19.880 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: CAS No.: NONE
2025-05-22 15:28:19.883 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 15:28:19.886 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=3_5_turbo
2025-05-22 15:28:20.840 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 953.89ms, model=3_5_turbo, tokensΓëê118+1
2025-05-22 15:28:20.841 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 15:28:20.844 | WARNING  | completions:get_chat_completion:279 - ajith_aravind Validation #1 did not match chat completion, returning None
2025-05-22 15:28:20.845 | INFO     | emissions_factor_matching.api:get_recommended_activities:211 - ajith_aravind CAS number: None
2025-05-22 15:28:20.845 | INFO     | emissions_factor_matching.predictions:predict_input_category:93 - ajith_aravind Classifying input: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 15:28:20.847 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given an input, determine whether the input is a c...', model=gpt-4o
2025-05-22 15:28:22.336 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1489.08ms, model=gpt-4o, tokensΓëê43+1
2025-05-22 15:28:22.337 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: PRODUCT
2025-05-22 15:28:22.339 | INFO     | emissions_factor_matching.predictions:predict_input_category:121 - ajith_aravind Input classified as: PRODUCT
2025-05-22 15:28:22.340 | INFO     | emissions_factor_matching.api:get_recommended_activities:221 - ajith_aravind Input classified as: PRODUCT
2025-05-22 15:28:22.341 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:127 - ajith_aravind Determining constituents for product: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 15:28:22.342 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a product, determine the primary material of...', model=gpt-4o
2025-05-22 15:28:23.223 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 881.14ms, model=gpt-4o, tokensΓëê44+2
2025-05-22 15:28:23.224 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: Polymer.
2025-05-22 15:28:23.227 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:153 - ajith_aravind Identified constituents: 'Polymer.'
2025-05-22 15:28:23.228 | INFO     | emissions_factor_matching.api:get_recommended_activities:231 - ajith_aravind Product: 'Hexafluoroethane-based polymer for aerospace thermal insulation', constituent: 'Polymer.'
2025-05-22 15:28:23.230 | INFO     | emissions_factor_matching.api:get_recommended_activities:232 - ajith_aravind Enriched query: 'Polymer. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 15:28:23.230 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:194 - ajith_aravind Predicting ISIC section for product: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 15:28:23.231 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=3_5_turbo
2025-05-22 15:28:23.898 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 666.01ms, model=3_5_turbo, tokensΓëê213+0
2025-05-22 15:28:23.899 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 15:28:23.903 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 15:28:23.904 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=3_5_turbo
2025-05-22 15:28:24.562 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 657.86ms, model=3_5_turbo, tokensΓëê213+0
2025-05-22 15:28:24.563 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 15:28:24.566 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 15:28:24.567 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:243 - ajith_aravind Predicted ISIC section: C - Manufacturing
2025-05-22 15:28:24.568 | INFO     | emissions_factor_matching.api:get_recommended_activities:253 - ajith_aravind ISIC section filter applied: C - Manufacturing
2025-05-22 15:28:24.569 | INFO     | emissions_factor_matching.api:get_recommended_activities:288 - ajith_aravind Final query filters: {"$and": [{"activity_type": {"$eq": "ordinary transforming activity"}}, {"isic_section": {"$eq": "C - Manufacturing"}}]}
2025-05-22 15:28:24.569 | INFO     | emissions_factor_matching.api:get_recommended_activities:291 - ajith_aravind Executing vector search with query: 'Polymer. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 15:28:28.133 | INFO     | emissions_factor_matching.api:get_recommended_activities:300 - ajith_aravind Vector search completed in 3562.69ms, returned 125 results
2025-05-22 15:28:28.133 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:302 - ajith_aravind Top 5 vector search results: ['polystyrene production, extruded, HFC-152a blown', 'polyurethane production, flexible foam, TDI-based, high density', 'polyurethane production, flexible foam, MDI-based', 'polyurethane production, rigid foam', 'polymer foaming']
2025-05-22 15:28:28.134 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:304 - ajith_aravind Top 5 similarity scores: [0.26948586106300354, 0.27395153045654297, 0.2765253186225891, 0.2771849036216736, 0.27818676829338074]
2025-05-22 15:28:28.134 | INFO     | emissions_factor_matching.api:get_recommended_activities:324 - ajith_aravind Re-ranking 125 candidates using LLM
2025-05-22 15:28:28.136 | INFO     | emissions_factor_matching.predictions:get_closest_match:252 - ajith_aravind Re-ranking 125 candidates for 'Polymer. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 15:28:28.158 | INFO     | completions:_get_async_completion:100 - ajith_aravind LLM call initiated: purpose='Given a list of emissions activities by the user d...', model=gpt-4o
2025-05-22 15:28:34.345 | INFO     | completions:_get_async_completion:119 - ajith_aravind LLM call completed in 6187.87ms, model=gpt-4o, tokensΓëê23487+118
2025-05-22 15:28:34.347 | DEBUG    | completions:_get_async_completion:120 - ajith_aravind Completion: ```json
2025-05-22 15:28:34.600 | ERROR    | emissions_factor_matching.predictions:get_closest_match:314 - ajith_aravind Failed to parse LLM response: Expecting value: line 1 column 1 (char 0)
2025-05-22 15:28:35.992 | ERROR    | emissions_factor_matching.predictions:get_closest_match:316 - ajith_aravind Raw LLM response: ```json
2025-05-22 15:28:35.996 | ERROR    | emissions_factor_matching.api:get_recommended_activities:334 - ajith_aravind LLM re-ranking failed to return a match
2025-05-22 15:28:38.439 | INFO     | emissions_factor_matching.api:get_recommended_activities:206 - ajith_aravind ML Service request: material='Hexafluoroethane-based polymer for aerospace thermal insulation', iso=GLO, api_version=latest
2025-05-22 15:28:38.442 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=3_5_turbo
2025-05-22 15:28:39.964 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1521.98ms, model=3_5_turbo, tokensΓëê118+3
2025-05-22 15:28:39.965 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: CAS No.: NONE
2025-05-22 15:28:39.969 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 15:28:39.971 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=3_5_turbo
2025-05-22 15:28:41.032 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1060.63ms, model=3_5_turbo, tokensΓëê118+1
2025-05-22 15:28:41.036 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 15:28:41.038 | WARNING  | completions:get_chat_completion:279 - ajith_aravind Validation #1 did not match chat completion, returning None
2025-05-22 15:28:41.040 | INFO     | emissions_factor_matching.api:get_recommended_activities:211 - ajith_aravind CAS number: None
2025-05-22 15:28:41.041 | INFO     | emissions_factor_matching.predictions:predict_input_category:93 - ajith_aravind Classifying input: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 15:28:41.042 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given an input, determine whether the input is a c...', model=gpt-4o
2025-05-22 15:28:42.762 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1720.04ms, model=gpt-4o, tokensΓëê43+1
2025-05-22 15:28:42.763 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: PRODUCT
2025-05-22 15:28:42.765 | INFO     | emissions_factor_matching.predictions:predict_input_category:121 - ajith_aravind Input classified as: PRODUCT
2025-05-22 15:28:42.767 | INFO     | emissions_factor_matching.api:get_recommended_activities:221 - ajith_aravind Input classified as: PRODUCT
2025-05-22 15:28:42.767 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:127 - ajith_aravind Determining constituents for product: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 15:28:42.768 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a product, determine the primary material of...', model=gpt-4o
2025-05-22 15:28:43.580 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 811.38ms, model=gpt-4o, tokensΓëê44+12
2025-05-22 15:28:43.581 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: Primary material: Hexafluoroethane-based polymer.
2025-05-22 15:28:43.583 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:153 - ajith_aravind Identified constituents: 'Primary material: Hexafluoroethane-based polymer.'
2025-05-22 15:28:43.587 | INFO     | emissions_factor_matching.api:get_recommended_activities:231 - ajith_aravind Product: 'Hexafluoroethane-based polymer for aerospace thermal insulation', constituent: 'Primary material: Hexafluoroethane-based polymer.'
2025-05-22 15:28:43.588 | INFO     | emissions_factor_matching.api:get_recommended_activities:232 - ajith_aravind Enriched query: 'Primary material: Hexafluoroethane-based polymer. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 15:28:43.588 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:194 - ajith_aravind Predicting ISIC section for product: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 15:28:43.590 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=3_5_turbo
2025-05-22 15:28:44.477 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 886.99ms, model=3_5_turbo, tokensΓëê213+0
2025-05-22 15:28:44.478 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 15:28:44.481 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 15:28:44.483 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=3_5_turbo
2025-05-22 15:28:45.542 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1059.50ms, model=3_5_turbo, tokensΓëê213+0
2025-05-22 15:28:45.544 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 15:28:45.546 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 15:28:45.547 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:243 - ajith_aravind Predicted ISIC section: C - Manufacturing
2025-05-22 15:28:45.548 | INFO     | emissions_factor_matching.api:get_recommended_activities:253 - ajith_aravind ISIC section filter applied: C - Manufacturing
2025-05-22 15:28:45.549 | INFO     | emissions_factor_matching.api:get_recommended_activities:288 - ajith_aravind Final query filters: {"$and": [{"activity_type": {"$eq": "ordinary transforming activity"}}, {"isic_section": {"$eq": "C - Manufacturing"}}]}
2025-05-22 15:28:45.550 | INFO     | emissions_factor_matching.api:get_recommended_activities:291 - ajith_aravind Executing vector search with query: 'Primary material: Hexafluoroethane-based polymer. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 15:28:48.860 | INFO     | emissions_factor_matching.api:get_recommended_activities:300 - ajith_aravind Vector search completed in 3308.36ms, returned 125 results
2025-05-22 15:28:48.860 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:302 - ajith_aravind Top 5 vector search results: ['hexafluoroethane production, from fluorination of tetrafluoroethane', 'hexafluoroethane production, from fluorination of tetrafluoroethane', 'polystyrene production, extruded, HFC-152a blown', 'polyurethane production, flexible foam, TDI-based, flame retardant', 'polyurethane production, flexible foam, TDI-based, high density']
2025-05-22 15:28:48.861 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:304 - ajith_aravind Top 5 similarity scores: [0.24572835862636566, 0.24572835862636566, 0.26604539155960083, 0.278313547372818, 0.2791008949279785]
2025-05-22 15:28:48.862 | INFO     | emissions_factor_matching.api:get_recommended_activities:324 - ajith_aravind Re-ranking 125 candidates using LLM
2025-05-22 15:28:48.862 | INFO     | emissions_factor_matching.predictions:get_closest_match:252 - ajith_aravind Re-ranking 125 candidates for 'Primary material: Hexafluoroethane-based polymer. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 15:28:48.887 | INFO     | completions:_get_async_completion:100 - ajith_aravind LLM call initiated: purpose='Given a list of emissions activities by the user d...', model=gpt-4o
2025-05-22 15:28:53.947 | INFO     | completions:_get_async_completion:119 - ajith_aravind LLM call completed in 5060.40ms, model=gpt-4o, tokensΓëê23796+98
2025-05-22 15:28:53.948 | DEBUG    | completions:_get_async_completion:120 - ajith_aravind Completion: ```json
2025-05-22 15:28:54.202 | ERROR    | emissions_factor_matching.predictions:get_closest_match:314 - ajith_aravind Failed to parse LLM response: Expecting value: line 1 column 1 (char 0)
2025-05-22 15:28:54.473 | ERROR    | emissions_factor_matching.predictions:get_closest_match:316 - ajith_aravind Raw LLM response: ```json
2025-05-22 15:28:54.479 | ERROR    | emissions_factor_matching.api:get_recommended_activities:334 - ajith_aravind LLM re-ranking failed to return a match
2025-05-22 15:28:58.493 | INFO     | emissions_factor_matching.api:get_recommended_activities:206 - ajith_aravind ML Service request: material='Hexafluoroethane-based polymer for aerospace thermal insulation', iso=GLO, api_version=latest
2025-05-22 15:28:58.494 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=3_5_turbo
2025-05-22 15:28:59.926 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1432.25ms, model=3_5_turbo, tokensΓëê118+3
2025-05-22 15:28:59.928 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: CAS No.: NONE
2025-05-22 15:28:59.929 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 15:28:59.931 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=3_5_turbo
2025-05-22 15:29:01.005 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1074.72ms, model=3_5_turbo, tokensΓëê118+1
2025-05-22 15:29:01.007 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 15:29:01.008 | WARNING  | completions:get_chat_completion:279 - ajith_aravind Validation #1 did not match chat completion, returning None
2025-05-22 15:29:01.009 | INFO     | emissions_factor_matching.api:get_recommended_activities:211 - ajith_aravind CAS number: None
2025-05-22 15:29:01.009 | INFO     | emissions_factor_matching.predictions:predict_input_category:93 - ajith_aravind Classifying input: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 15:29:01.010 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given an input, determine whether the input is a c...', model=gpt-4o
2025-05-22 15:29:02.434 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1423.71ms, model=gpt-4o, tokensΓëê43+1
2025-05-22 15:29:02.435 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: PRODUCT
2025-05-22 15:29:02.437 | INFO     | emissions_factor_matching.predictions:predict_input_category:121 - ajith_aravind Input classified as: PRODUCT
2025-05-22 15:29:02.438 | INFO     | emissions_factor_matching.api:get_recommended_activities:221 - ajith_aravind Input classified as: PRODUCT
2025-05-22 15:29:02.439 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:127 - ajith_aravind Determining constituents for product: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 15:29:02.440 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a product, determine the primary material of...', model=gpt-4o
2025-05-22 15:29:03.462 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1022.33ms, model=gpt-4o, tokensΓëê44+15
2025-05-22 15:29:03.463 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: The primary material is a **hexafluoroethane-based polymer**.
2025-05-22 15:29:03.464 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:153 - ajith_aravind Identified constituents: 'The primary material is a **hexafluoroethane-based polymer**.'
2025-05-22 15:29:03.465 | INFO     | emissions_factor_matching.api:get_recommended_activities:231 - ajith_aravind Product: 'Hexafluoroethane-based polymer for aerospace thermal insulation', constituent: 'The primary material is a **hexafluoroethane-based polymer**.'
2025-05-22 15:29:03.466 | INFO     | emissions_factor_matching.api:get_recommended_activities:232 - ajith_aravind Enriched query: 'The primary material is a **hexafluoroethane-based polymer**. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 15:29:03.467 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:194 - ajith_aravind Predicting ISIC section for product: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 15:29:03.467 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=3_5_turbo
2025-05-22 15:29:04.263 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 795.92ms, model=3_5_turbo, tokensΓëê213+0
2025-05-22 15:29:04.265 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 15:29:04.266 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 15:29:04.267 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=3_5_turbo
2025-05-22 15:29:04.933 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 665.52ms, model=3_5_turbo, tokensΓëê213+0
2025-05-22 15:29:04.934 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 15:29:04.935 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 15:29:04.935 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:243 - ajith_aravind Predicted ISIC section: C - Manufacturing
2025-05-22 15:29:04.936 | INFO     | emissions_factor_matching.api:get_recommended_activities:253 - ajith_aravind ISIC section filter applied: C - Manufacturing
2025-05-22 15:29:04.937 | INFO     | emissions_factor_matching.api:get_recommended_activities:288 - ajith_aravind Final query filters: {"$and": [{"activity_type": {"$eq": "ordinary transforming activity"}}, {"isic_section": {"$eq": "C - Manufacturing"}}]}
2025-05-22 15:29:04.938 | INFO     | emissions_factor_matching.api:get_recommended_activities:291 - ajith_aravind Executing vector search with query: 'The primary material is a **hexafluoroethane-based polymer**. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 15:29:07.772 | INFO     | emissions_factor_matching.api:get_recommended_activities:300 - ajith_aravind Vector search completed in 2833.12ms, returned 125 results
2025-05-22 15:29:07.772 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:302 - ajith_aravind Top 5 vector search results: ['hexafluoroethane production, from fluorination of tetrafluoroethane', 'hexafluoroethane production, from fluorination of tetrafluoroethane', 'polystyrene production, extruded, HFC-152a blown', 'polyurethane production, flexible foam, TDI-based, high density', 'polyurethane production, flexible foam, TDI-based, flame retardant']
2025-05-22 15:29:07.773 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:304 - ajith_aravind Top 5 similarity scores: [0.25423571467399597, 0.25423571467399597, 0.2608909606933594, 0.2712603807449341, 0.2743716239929199]
2025-05-22 15:29:07.774 | INFO     | emissions_factor_matching.api:get_recommended_activities:324 - ajith_aravind Re-ranking 125 candidates using LLM
2025-05-22 15:29:07.775 | INFO     | emissions_factor_matching.predictions:get_closest_match:252 - ajith_aravind Re-ranking 125 candidates for 'The primary material is a **hexafluoroethane-based polymer**. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 15:29:07.797 | INFO     | completions:_get_async_completion:100 - ajith_aravind LLM call initiated: purpose='Given a list of emissions activities by the user d...', model=gpt-4o
2025-05-22 15:29:15.020 | INFO     | completions:_get_async_completion:119 - ajith_aravind LLM call completed in 7222.97ms, model=gpt-4o, tokensΓëê23751+105
2025-05-22 15:29:15.021 | DEBUG    | completions:_get_async_completion:120 - ajith_aravind Completion: ```json
2025-05-22 15:29:15.273 | ERROR    | emissions_factor_matching.predictions:get_closest_match:314 - ajith_aravind Failed to parse LLM response: Expecting value: line 1 column 1 (char 0)
2025-05-22 15:29:15.281 | ERROR    | emissions_factor_matching.predictions:get_closest_match:316 - ajith_aravind Raw LLM response: ```json
2025-05-22 15:29:15.287 | ERROR    | emissions_factor_matching.api:get_recommended_activities:334 - ajith_aravind LLM re-ranking failed to return a match
2025-05-22 15:29:22.990 | INFO     | emissions_factor_matching.api:get_recommended_activities:206 - ajith_aravind ML Service request: material='Hexafluoroethane-based polymer for aerospace thermal insulation', iso=GLO, api_version=latest
2025-05-22 15:29:22.991 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=3_5_turbo
2025-05-22 15:29:24.371 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1380.18ms, model=3_5_turbo, tokensΓëê118+3
2025-05-22 15:29:24.373 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: CAS No.: NONE
2025-05-22 15:29:24.374 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 15:29:24.375 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a chemical return the cas number in the form...', model=3_5_turbo
2025-05-22 15:29:25.692 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1316.48ms, model=3_5_turbo, tokensΓëê118+1
2025-05-22 15:29:25.693 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: NONE
2025-05-22 15:29:25.694 | WARNING  | completions:get_chat_completion:279 - ajith_aravind Validation #1 did not match chat completion, returning None
2025-05-22 15:29:25.695 | INFO     | emissions_factor_matching.api:get_recommended_activities:211 - ajith_aravind CAS number: None
2025-05-22 15:29:25.695 | INFO     | emissions_factor_matching.predictions:predict_input_category:93 - ajith_aravind Classifying input: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 15:29:25.696 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given an input, determine whether the input is a c...', model=gpt-4o
2025-05-22 15:29:27.311 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1614.82ms, model=gpt-4o, tokensΓëê43+1
2025-05-22 15:29:27.313 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: PRODUCT
2025-05-22 15:29:27.313 | INFO     | emissions_factor_matching.predictions:predict_input_category:121 - ajith_aravind Input classified as: PRODUCT
2025-05-22 15:29:27.314 | INFO     | emissions_factor_matching.api:get_recommended_activities:221 - ajith_aravind Input classified as: PRODUCT
2025-05-22 15:29:27.315 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:127 - ajith_aravind Determining constituents for product: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 15:29:27.316 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Given a product, determine the primary material of...', model=gpt-4o
2025-05-22 15:29:28.450 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1133.65ms, model=gpt-4o, tokensΓëê44+15
2025-05-22 15:29:28.451 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: The primary material is a **hexafluoroethane-based polymer**.
2025-05-22 15:29:28.452 | INFO     | emissions_factor_matching.predictions:predict_product_constituents:153 - ajith_aravind Identified constituents: 'The primary material is a **hexafluoroethane-based polymer**.'
2025-05-22 15:29:28.453 | INFO     | emissions_factor_matching.api:get_recommended_activities:231 - ajith_aravind Product: 'Hexafluoroethane-based polymer for aerospace thermal insulation', constituent: 'The primary material is a **hexafluoroethane-based polymer**.'
2025-05-22 15:29:28.454 | INFO     | emissions_factor_matching.api:get_recommended_activities:232 - ajith_aravind Enriched query: 'The primary material is a **hexafluoroethane-based polymer**. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 15:29:28.455 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:194 - ajith_aravind Predicting ISIC section for product: 'Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 15:29:28.456 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=3_5_turbo
2025-05-22 15:29:29.229 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 773.09ms, model=3_5_turbo, tokensΓëê213+0
2025-05-22 15:29:29.230 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 15:29:29.230 | INFO     | completions:get_chat_completion:266 - ajith_aravind Performing 1 validation(s) for LLM response
2025-05-22 15:29:29.231 | INFO     | completions:_get_completion:203 - ajith_aravind LLM call initiated: purpose='Respond with the ISIC Section for a product provid...', model=3_5_turbo
2025-05-22 15:29:30.325 | INFO     | completions:_get_completion:222 - ajith_aravind LLM call completed in 1094.18ms, model=3_5_turbo, tokensΓëê213+0
2025-05-22 15:29:30.327 | DEBUG    | completions:_get_completion:223 - ajith_aravind Completion: C
2025-05-22 15:29:30.329 | INFO     | completions:get_chat_completion:282 - ajith_aravind Validation #1 successful
2025-05-22 15:29:30.331 | INFO     | emissions_factor_matching.predictions:get_product_activity_isic_section:243 - ajith_aravind Predicted ISIC section: C - Manufacturing
2025-05-22 15:29:30.332 | INFO     | emissions_factor_matching.api:get_recommended_activities:253 - ajith_aravind ISIC section filter applied: C - Manufacturing
2025-05-22 15:29:30.333 | INFO     | emissions_factor_matching.api:get_recommended_activities:288 - ajith_aravind Final query filters: {"$and": [{"activity_type": {"$eq": "ordinary transforming activity"}}, {"isic_section": {"$eq": "C - Manufacturing"}}]}
2025-05-22 15:29:30.334 | INFO     | emissions_factor_matching.api:get_recommended_activities:291 - ajith_aravind Executing vector search with query: 'The primary material is a **hexafluoroethane-based polymer**. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 15:29:31.775 | INFO     | emissions_factor_matching.api:get_recommended_activities:300 - ajith_aravind Vector search completed in 1440.67ms, returned 125 results
2025-05-22 15:29:31.776 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:302 - ajith_aravind Top 5 vector search results: ['hexafluoroethane production, from fluorination of tetrafluoroethane', 'hexafluoroethane production, from fluorination of tetrafluoroethane', 'polystyrene production, extruded, HFC-152a blown', 'polyurethane production, flexible foam, TDI-based, high density', 'polyurethane production, flexible foam, TDI-based, flame retardant']
2025-05-22 15:29:31.776 | DEBUG    | emissions_factor_matching.api:get_recommended_activities:304 - ajith_aravind Top 5 similarity scores: [0.25423571467399597, 0.25423571467399597, 0.2608909606933594, 0.2712603807449341, 0.2743716239929199]
2025-05-22 15:29:31.777 | INFO     | emissions_factor_matching.api:get_recommended_activities:324 - ajith_aravind Re-ranking 125 candidates using LLM
2025-05-22 15:29:31.778 | INFO     | emissions_factor_matching.predictions:get_closest_match:252 - ajith_aravind Re-ranking 125 candidates for 'The primary material is a **hexafluoroethane-based polymer**. Hexafluoroethane-based polymer for aerospace thermal insulation'
2025-05-22 15:29:31.802 | INFO     | completions:_get_async_completion:100 - ajith_aravind LLM call initiated: purpose='Given a list of emissions activities by the user d...', model=gpt-4o
2025-05-22 15:29:35.836 | INFO     | completions:_get_async_completion:119 - ajith_aravind LLM call completed in 4033.86ms, model=gpt-4o, tokensΓëê23751+125
2025-05-22 15:29:35.837 | DEBUG    | completions:_get_async_completion:120 - ajith_aravind Completion: ```json
2025-05-22 15:29:36.088 | ERROR    | emissions_factor_matching.predictions:get_closest_match:314 - ajith_aravind Failed to parse LLM response: Expecting value: line 1 column 1 (char 0)
2025-05-22 15:29:36.092 | ERROR    | emissions_factor_matching.predictions:get_closest_match:316 - ajith_aravind Raw LLM response: ```json
2025-05-22 15:29:36.098 | ERROR    | emissions_factor_matching.api:get_recommended_activities:334 - ajith_aravind LLM re-ranking failed to return a match
