import os
import torch
from torchvision import datasets, transforms
from torchvision.models import resnet50, ResNet50_Weights

transform = transforms.Compose([
    transforms.Resize(256),
    transforms.CenterCrop(224),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
])

if not os.path.exists('./dataset-resized'):
    print(
        """
        Training Dataset Not Downloaded.

        Run ./download_dataset.sh before attempting to train model
        """,
    )
    exit(1)

dataset = datasets.ImageFolder(root='./dataset-resized', transform=transform)

train_size = int(0.8 * len(dataset))
val_size = len(dataset) - train_size

train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])

train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=32, shuffle=True, num_workers=4)
val_loader = torch.utils.data.DataLoader(val_dataset, batch_size=32, shuffle=False, num_workers=4)