from fastapi import (
    FastAPI,
    UploadFile,
    File,
)
import io
from PIL import Image
import torch
from torchvision import transforms
from package_material_classifier_simplified.model import model, device
from package_material_classifier_simplified.labels import labels

transform = transforms.Compose([
    transforms.Resize(256),
    transforms.CenterCrop(224),
    transforms.ToTensor(),
    transforms.Normalize(
        mean=[0.485, 0.456, 0.406],
        std=[0.229, 0.224, 0.225],
    ),
])

model_api = FastAPI()

@model_api.post('/predict-package-material')
async def predict_class(file: UploadFile = File(...)):
    file_contents = await file.read()

    image = Image.open(io.BytesIO(file_contents))
    image = transform(image).unsqueeze(0)
    image = image.to(device)

    with torch.no_grad():
        outputs = model(image)
    
    _, predicted_indice = torch.max(outputs, 1)

    return {
        "data": {
            "prediction": labels[predicted_indice],
        },
    } 

