import os
from typing import Any
from dataclasses import dataclass, field
from dotenv import load_dotenv

load_dotenv()


def get_required_env_var(env_var_name, arg_type: type = str):
    if env_var_name not in os.environ:
        raise EnvironmentError(
            f'Required environment variable "{env_var_name}" is not set.'
        )
    return arg_type(os.environ[env_var_name])


def get_optional_env_var(env_var_name: str, default_value: Any | None = None, arg_type: type = str):
    if not default_value:
        if not os.environ.get(env_var_name):
            return None

    if arg_type is bool:
        return os.environ.get(env_var_name, default_value).lower() in ["true", "1"]
    return arg_type(os.environ.get(env_var_name, default_value))


@dataclass
class Config:
    azure_openai_api_key: str = field(
        default_factory=lambda: get_required_env_var("AZURE_OPENAI_API_KEY")
    )
    azure_openai_endpoint: str = field(
        default_factory=lambda: get_required_env_var("AZURE_OPENAI_ENDPOINT")
    )
    azure_api_version: str = field(
        default_factory=lambda: get_optional_env_var("AZURE_API_VERSION", "2024-08-01-preview")
    )
    azure_openai_deployment: str = field(
        default_factory=lambda: get_optional_env_var(
            "AZURE_OPENAI_DEPLOYMENT", "gpt-4o"
        )
    )
    azure_phi_3_key: str = field(
        default_factory=lambda: get_required_env_var("AZURE_PHI_3_KEY")
    )
    azure_phi_3_endpoint: str = field(
        default_factory=lambda: get_required_env_var("AZURE_PHI_3_ENDPOINT")
    )
    hf_token: str = field(default_factory=lambda: get_required_env_var("HF_TOKEN"))
    mapbox_access_token: str = field(default_factory=lambda: get_required_env_var("MAPBOX_ACCESS_TOKEN"))
    port: int = field(default_factory=lambda: get_optional_env_var("PORT", 5000, int))
    rate_limit_retry_count: str = field(
        default_factory=lambda: get_optional_env_var("RATE_LIMIT_RETRY_COUNT", 5, int)
    )
    sentry_dsn: str | None = field(default_factory=lambda: get_optional_env_var("SENTRY_DSN"))
    workers: int = field(default_factory=lambda: get_optional_env_var("WORKERS", 2, int))
    cache_dir: str = field(default_factory=lambda: get_optional_env_var("CACHE_DIR", "/tmp/"))
    redis_url: str | None = field(default_factory=lambda: get_optional_env_var("REDIS_URL"))
    cache_expiry: int = field(default_factory=lambda: get_optional_env_var("CACHE_EXPIRY", 3600*24*365, int))
    chroma_needed: bool = field(default_factory=lambda: get_optional_env_var("CHROMA_NEEDED", "true", bool))
    debug: bool = field(default_factory=lambda: get_optional_env_var("DEBUG", "false", bool))
    skip_parts: str = field(default_factory=lambda: get_optional_env_var("SKIP_PARTS", ""))

config = Config()
