#!/usr/bin/env python3
"""
Check ChromaDB metadata structure to understand ISIC field names
"""
import sys
sys.path.append('/home/<USER>/app')

from emissions_factor_matching.dataset import collection
import json

def check_chroma_metadata():
    print("=== CHECKING CHROMADB METADATA STRUCTURE ===\n")
    
    # Query a few transport-related activities to see metadata structure
    results = collection.query(
        query_texts=["freight transport truck"],
        where={"activity_type": {"$eq": "ordinary transforming activity"}},
        n_results=3,
        include=['metadatas', 'documents', 'distances']
    )
    
    print("Sample ChromaDB metadata for transport activities:")
    print(f"Found {len(results['ids'][0])} results\n")
    
    for i in range(len(results['ids'][0])):
        print(f"--- Result {i+1} ---")
        print(f"ID: {results['ids'][0][i]}")
        print(f"Document: {results['documents'][0][i]}")
        print(f"Distance: {results['distances'][0][i]:.4f}")
        
        metadata = results['metadatas'][0][i]
        print("Metadata keys:")
        for key in sorted(metadata.keys()):
            print(f"  {key}: {metadata[key]}")
        print()
        
        # Check for ISIC-related fields
        isic_fields = [key for key in metadata.keys() if 'isic' in key.lower()]
        if isic_fields:
            print(f"ISIC-related fields: {isic_fields}")
            for field in isic_fields:
                print(f"  {field}: {metadata[field]}")
        else:
            print("No ISIC-related fields found")
        print()

if __name__ == "__main__":
    check_chroma_metadata()
