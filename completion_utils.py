"""Shared utils for chat completions."""
from typing import TypeVar, Type
import json
import re
from pydantic import BaseModel
import xml.etree.ElementTree as ET


T = TypeVar('T', bound=BaseModel)

def parse_json(completion: str, model: Type[T]) -> T:
    # Remove markdown code block delimiters if present
    if completion and isinstance(completion, str):
        # Handle ```json code blocks
        if completion.strip().startswith("```json"):
            # Find the end of the code block
            end_delimiter = completion.rfind("```")
            if end_delimiter > 5:  # 5 is the length of "```json"
                completion = completion[completion.find("{"):end_delimiter].strip()
            else:
                # If no end delimiter, just remove the start and try to find JSON
                completion = completion[completion.find("{"):].strip()

        # Handle ```code blocks without json specifier
        elif completion.strip().startswith("```") and not completion.strip().startswith("```json"):
            # Find the end of the code block
            end_delimiter = completion.rfind("```")
            if end_delimiter > 3:  # 3 is the length of "```"
                completion = completion[completion.find("{"):end_delimiter].strip()
            else:
                # If no end delimiter, just remove the start and try to find JSON
                completion = completion[completion.find("{"):].strip()

    # Remove comments
    comment_pattern = r'//.*?\n'
    cleaned_completion = re.sub(comment_pattern, '', completion)

    try:
        completion_dict = json.loads(cleaned_completion)
        return model(**completion_dict)
    except json.JSONDecodeError as e:
        from utils import logger
        logger.error(f"Failed to parse JSON: {e}")
        logger.error(f"Raw response: {completion}")
        raise

def parse_template_response(completion: str) -> dict[str, str]:
    root = ET.fromstring(completion)
    response_dict = {child.tag: child.text for child in root}

    return response_dict
