## LCA ML Models

**In house trained ML Models for LCA Tasks**

---

Setup

```bash
<NAME_EMAIL>:CarbonBright/ml_models.git
cd ml_models


export PORT=5006
export WORKERS=1
export AZURE_OPENAI_DEPLOYMENT=gpt-4o

export HF_TOKEN=..
export MAPBOX_ACCESS_TOKEN=..
export AZURE_OPENAI_API_KEY=
export AZURE_OPENAI_API_KEY_US_EAST=
export AZURE_OPENAI_ENDPOINT=..
export AZURE_OPENAI_ENDPOINT_US_EAST=..
export AZURE_PHI_3_ENDPOINT=..
export AZURE_PHI_3_KEY=..


huggingface-cli login

# Make sure you are using python3.10
python3 -m venv venv
source venv/bin/activate
# Use linux requirements.txt for linux
# For mac -
pip3 install -r ./dependencies/macos/requirements.txt

export SSL_CERT_FILE=$(python -m certifi)

# set DEBUG=true for remote debugging

python3 main.py
```

### [Package Material Classifier (simplified)](https://huggingface.co/CarbonBright/packaging-base-material-classification)

Objective: Given a product image predict the package material.

Labels

- cardboard
- glass
- metal
- paper
- plastic

Avg. Accuracy: 89.66%

Endpoints

`POST /api/package-material-classifier-simplified/predict-package-material/`

Request: `file=@path_to_your_file.jpg`

Response:

```json
{
	"data": {
		"prediction": "cardboard"
	}
}
```

### [Emissions Factor Matching](https://huggingface.co/datasets/CarbonBright/emissions-factors-activities-overview)

Objective: Use activity embeddings to find the closest match for a chemical.

Endpoints

`POST /api/emissions-factor-matching/activity/`

Request:

```json
{
	"cas_number": "013463-67-7",
	"chemical_name": "Titanium dioxide"
}
```

Response:

```json
{
	"activity_uuid": "543550dd-7f17-5f78-b997-ae1410671af4",
	"activity_name": "market for titanium dioxide",
	"product_uuid": "b419e26a-249f-4286-a6f8-188e7dfba951",
	"reference_product_name": "titanium dioxide",
	"product_information": "'titanium dioxide' is a metal. It is a chemical substance that is in form of white powder. It can be used in the following applications and/or processes: variety of industrial and consumer products, including paints, coatings, adhesives, paper, plastics and rubber, printing inks, coated fabrics and textiles, as well as ceramics, floor coverings, roofing materials, cosmetics, toothpaste, soap, water treatment agents, pharmaceuticals, food colorants, automotive products, sunscreen and catalysts."
}
```

`POST /api/emissions-factor-matching/activities/recommendations`

Request:

```json
{
	"cas_number": "013463-67-7",
	"chemical_name": "Titanium dioxide",
	"number_of_matches": 10
}
```

Response:

```json
{
  "activities": List[Activity]
}
```
