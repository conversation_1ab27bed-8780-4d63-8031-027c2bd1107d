"""Pydantic models for emissions factor matching structured outputs"""
from typing import List, Optional, Literal
from pydantic import BaseModel, Field


class InputCategoryResponse(BaseModel):
    """Response model for input category prediction"""
    category: str = Field(description="The predicted input category code")


class ModifiersResponse(BaseModel):
    """Response model for modifier spotting"""
    modifiers: List[str] = Field(
        default_factory=list,
        description="List of identified modifiers from the input"
    )


class ISICClassificationResponse(BaseModel):
    """Response model for ISIC classification"""
    result: List[str] = Field(
        default_factory=list,
        description="List of relevant ISIC codes"
    )


class ClosestMatchResponse(BaseModel):
    """Response model for closest match finding"""
    activity_uuid: str = Field(description="UUID of the matched activity")
    confidence: Literal["low", "medium", "high"] = Field(description="Confidence level of the match")
    match_explanation: str = Field(description="Explanation for why this match was selected")


class TechnologicalRepresentationResponse(BaseModel):
    """Response model for technological representation assessment"""
    reason: str = Field(description="Explanation of the technological similarity/difference")
    similar: bool = Field(description="Whether the technologies are similar")


class RerankingResponse(BaseModel):
    """Response model for LLM reranking of candidates"""
    selected_candidate_uuid: str = Field(description="UUID of the selected candidate")
    confidence: Literal["HIGH", "MEDIUM", "LOW"] = Field(description="Confidence level")
    confidence_score: float = Field(ge=0.0, le=1.0, description="Numerical confidence score")
    explanation: str = Field(description="Detailed explanation of the selection")
    ranking_rationale: str = Field(description="Rationale for the ranking")
    alternative_considerations: str = Field(description="Considerations about alternative candidates")