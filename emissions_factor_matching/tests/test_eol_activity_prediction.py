from unittest import TestCase
from fastapi.testclient import TestClient
from utils import logger
from emissions_factor_matching.api import model_api
from utils import logger

real_world_validations = [
    (("PET", "landfill"), "treatment of waste polyethylene terephthalate, sanitary landfill"),
    (("PET", "recycling"), "treatment of waste polyethylene/polypropylene, recycling"),
    (("PET", "inceneration"), "treatment of waste polyethylene terephthalate, municipal incineration"),
    (("aluminium", "recycilng"), "treatment of aluminium scrap, post-consumer, prepared for recycling, at remelter"),
    (("glass", "recycling"), "treatment of waste glass from unsorted public collection, sorting"),
    (("RPET 500D", "landfill"), "treatment of waste polyethylene, unsanitary landfill, wet infiltration class (500mm)"),
    (("RPET 500D", "recycling"), "waste polyethylene terephthalate, for recycling, sorted, Recycled Content cut-off"),
    (("Nylon", "landfill"), "treatment of waste polystyrene, unsanitary landfill, moist infiltration class (300mm)"),
    (("Zipper Pullers", "landfill"), "treatment of municipal solid waste, sanitary landfill"),
    (("Sewn Label Non-Woven", "landfill"), "treatment of waste plastic, mixture, sanitary landfill"),
    (("Sewn Label Non-Woven", "recycling"), "waste polyethylene, for recycling, unsorted, Recycled Content cut-off"),
    (("Polyformaldehyde thermoplastic", "landfill"), "treatment of waste polyethylene/polypropylene product, collection for final disposal"),
    (("Polyformaldehyde thermoplastic", "recycling"), "treatment of waste polyethylene terephthalate, bottle, recycling")
]

client = TestClient(model_api)

class TestEOLEmissionsFactorMatchingAPI(TestCase):
    def test_real_world_validations(self):
        """Real World Benchmark"""
        for (material, disposal_method), validation_activity in real_world_validations:
            response = client.post(
                "/eol/activity",
                json={
                    "material": material,
                    "disposal_method": disposal_method,
                },
                headers={"api-version": "beta"}
            )

            match = response.json()
            self.assertEqual(match["activity_name"] == validation_activity)
