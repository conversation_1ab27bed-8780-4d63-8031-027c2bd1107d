from typing import Dict, Any, List
import json
import time
from emissions_factor_matching.dataset import efs_with_geographies
from emissions_factor_matching.prompt import get_enhanced_input_category_prompt, get_modifier_spotting_prompt, get_llm_reranking_prompt, get_isic_classification_prompt
from emissions_factor_matching.model import Candidate, MatchedEF
from emissions_factor_matching.models import (
    InputCategoryResponse,
    ModifiersResponse,
    ISICClassificationResponse,
    ClosestMatchResponse,
    TechnologicalRepresentationResponse,
    RerankingResponse
)
from completions import (
    get_chat_completion, 
    get_async_chat_completion,
    get_structured_completion,
    get_async_structured_completion
)
from clients import openai_client, openai_client_us_east, get_openai_async_client
from config import config
from utils import logger


def get_cas_number(chemical_name: str):
    prompt = (
        "Given a chemical return the cas number in the form (xxxxxx-xx-x).\n"
        "If the chemical does not have a CAS Number or is not a chemical return \"NONE\"\n\n"
        "Examples:\n\n"
        "Chemical: Sodium Chloride\n"
        "CAS No.: 7647-14-5\n\n"
        "Chemical: Koolaid\n\n"
        "CAS No.: NONE\n\n"
        "Chemical: Coffee\n"
        "CAS No.: NONE\n\n"
        "Chemical: Water\n"
        "CAS No.: 7732-18-5\n\n"
        "Note: Valid output should only either be a CAS No. or \"NONE\" with no additional output"
    )

    messages = [
        {
            "role": "system",
            "content": prompt,
        },
        {
            "role": "user",
            "content": (
                f"Chemical: {chemical_name}\n"
                "Cas No.: "
            )
        },
    ]

    return get_chat_completion(openai_client, messages, deployment=config.azure_openai_deployment, n_validations=1)


def get_common_chemical_names(chemical_name: str):
    prompt = (
        "Provide a comma seperated list of common chemical synonyms for a given ingredient.\n"
        "Avoid using informal names, they should be valid ingredient synonyms (max 5).\n"
        "If an ingredient is not a chemical respond with \"NONE\"\n"
        "Examples:\n\n"
        "Chemical: \"Sugar\"\n"
        "sucrose, glucose, fructose\n\n"
        "Chemical: \"Coffee\"\n"
        "NONE\n\n"
        "Chemical: \"Sodium Carbonate\"\n"
        "soda ash\n\n"
        "Chemical: \"Fish\"\n"
        "NONE\n\n"
        "Chemical: \"Strawberry\"\n"
        "NONE\n\n"
        "Chemical: \"Artificial Flavoring\"\n"
        "NONE\n\n"
        "Chemical: \"salt\"\n"
        "sodium chloride\n\n"
        "Chemical: \"mango\"\n"
        "NONE"
    )

    messages = [
        {
            "role": "system",
            "content": prompt
        },
        {
            "role": "user",
            "content": (
                f"Chemical: {chemical_name}\n"
            )
        }
    ]

    return get_chat_completion(
        openai_client_us_east,
        messages,
        deployment=config.azure_openai_deployment,
        n_validations=0,
    )

def predict_input_category(input_str: str):
    """Legacy function for backward compatibility - calls enhanced version"""
    # For backward compatibility, convert single string to request-like structure
    from pydantic import BaseModel

    class TempRequest(BaseModel):
        user_query_primary: str
        user_query_secondary: str | None = None
        lca_lifecycle_stage: str | None = None
        iso_code: str | None = None

    request = TempRequest(user_query_primary=input_str)
    result = predict_enhanced_input_category(request)
    return result

def predict_enhanced_input_category(request: Any):
    """
    Predict the input category for the enhanced emissions factor matching.
    Now uses structured output with LangChain.
    """
    from pydantic import BaseModel

    # Extract fields from request
    if isinstance(request, BaseModel):
        user_query_primary = request.user_query_primary
        user_query_secondary = getattr(request, 'user_query_secondary', None)
        lca_lifecycle_stage = getattr(request, 'lca_lifecycle_stage', None)
        iso_code = getattr(request, 'iso_code', None)
    elif isinstance(request, dict):
        user_query_primary = request.get('user_query_primary')
        user_query_secondary = request.get('user_query_secondary')
        lca_lifecycle_stage = request.get('lca_lifecycle_stage')
        iso_code = request.get('iso_code')
    else:
        # Handle string input for backward compatibility
        user_query_primary = str(request)
        user_query_secondary = None
        lca_lifecycle_stage = None
        iso_code = None

    if not user_query_primary:
        logger.error("No primary query provided")
        return None

    prompt = get_enhanced_input_category_prompt()

    messages = [
        {
            "role": "system",
            "content": prompt
        },
        {
            "role": "user",
            "content": f"User query: {user_query_primary}"
                      + (f"\nAdditional context: {user_query_secondary}" if user_query_secondary else "")
                      + (f"\nLCA lifecycle stage: {lca_lifecycle_stage}" if lca_lifecycle_stage else "")
                      + (f"\nISO country code: {iso_code}" if iso_code else "")
        }
    ]

    # Use structured completion
    result = get_structured_completion(
        messages=messages,
        response_model=InputCategoryResponse,
        deployment=config.azure_openai_deployment,
        temperature=0,
    )

    if result:
        return result.category
    else:
        logger.warning("Failed to get structured response for input category")
        return None


def predict_category(input_str: str):
    """Alias for predict_input_category for backward compatibility"""
    return predict_input_category(input_str)


def spot_modifiers(input_description: str, lca_lifecycle_stage: str = None, iso_code: str = None) -> List[str]:
    """
    Extracts modifiers from the input description.
    Now uses structured output with LangChain.
    """
    # Build the base query
    query = f"Input: {input_description}"
    
    # Add optional parameters if provided
    if lca_lifecycle_stage:
        query += f"\nLCA Lifecycle Stage: {lca_lifecycle_stage}"
    if iso_code:
        query += f"\nISO Country Code: {iso_code}"

    prompt = get_modifier_spotting_prompt()

    messages = [
        {
            "role": "system",
            "content": prompt
        },
        {
            "role": "user",
            "content": query
        }
    ]

    # Use structured completion
    result = get_structured_completion(
        messages=messages,
        response_model=ModifiersResponse,
        deployment=config.azure_openai_deployment,
        temperature=0,
    )

    if result:
        return result.modifiers
    else:
        logger.warning("Failed to get structured response for modifiers")
        return []


def map_isic_classification(user_query: str, iso_code: str = None) -> List[str]:
    """
    Maps user query to ISIC codes.
    Now uses structured output with LangChain.
    """
    # Build the query
    query = f"User query: {user_query}"
    if iso_code:
        query += f"\nISO Country Code: {iso_code}"

    prompt = get_isic_classification_prompt()

    messages = [
        {
            "role": "system",
            "content": prompt
        },
        {
            "role": "user",
            "content": query
        }
    ]

    # Use structured completion
    result = get_structured_completion(
        messages=messages,
        response_model=ISICClassificationResponse,
        deployment=config.azure_openai_deployment,
        temperature=0,
    )

    if result:
        return result.result
    else:
        logger.warning("Failed to get structured response for ISIC classification")
        return []


def prepare_results(
    results_dict: Dict[str, List[Candidate]],
    modifier_used: bool,
    isic_codes: List[str],
) -> List[MatchedEF]:
    sorted_results = {}
    for phase_name, candidates in results_dict.items():
        sorted_candidates = sorted(candidates, key=lambda c: c.similarity_score, reverse=True)
        sorted_results[phase_name] = sorted_candidates

    # Flatten results from all phases
    all_results = []
    for phase_name, candidates in sorted_results.items():
        for candidate in candidates:
            all_results.append(
                MatchedEF(
                    activity_uuid=candidate.activity_uuid,
                    similarity_score=candidate.similarity_score,
                    ef_activity_name=candidate.ef_activity_name,
                    ef_reference_unit=candidate.ef_reference_unit,
                    phase=phase_name,
                    modifier_used=modifier_used,
                    isic_used=bool(isic_codes),
                )
            )

    # Sort all results by similarity score
    all_results = sorted(all_results, key=lambda r: r.similarity_score, reverse=True)

    # Keep top 15 overall
    top_results = all_results[:15]

    # Remove duplicates based on activity_uuid while preserving order
    seen_uuids = set()
    unique_results = []
    for result in top_results:
        if result.activity_uuid not in seen_uuids:
            seen_uuids.add(result.activity_uuid)
            unique_results.append(result)

    return unique_results


def hybrid_predictions(
    user_query_primary: str, 
    user_query_secondary: str = None, 
    lca_lifecycle_stage: str = None, 
    iso_code: str = None
) -> List[MatchedEF]:
    """
    Main function for the multi-phase approach.
    Returns a list of MatchedEF objects with phase information.
    """
    # Initialize results dictionary
    results_dict = {}
    
    # Phase 1: Base Search
    from emissions_factor_matching.dataset import search_emissions_factors_vectordb
    
    # Always do base search first
    base_results = search_emissions_factors_vectordb(user_query_primary, iso_code=iso_code, filter_metadata={})
    results_dict["phase_1_base"] = base_results
    
    # Check if we need Phase 2
    has_good_matches = any(candidate.similarity_score >= 0.8 for candidate in base_results)
    
    if has_good_matches:
        # Good matches found, prepare and return results
        return prepare_results(results_dict, modifier_used=False, isic_codes=[])
    
    # Phase 2: Enhanced Search with modifiers and ISIC codes
    # Extract modifiers
    modifiers = spot_modifiers(user_query_primary, lca_lifecycle_stage, iso_code)
    
    # Get ISIC codes
    isic_codes = map_isic_classification(user_query_primary, iso_code)
    
    # Search with modifiers if found
    if modifiers:
        modifier_results = []
        for modifier in modifiers:
            results = search_emissions_factors_vectordb(modifier, iso_code=iso_code, filter_metadata={})
            modifier_results.extend(results)
        
        # Remove duplicates based on activity_uuid
        seen_uuids = set()
        unique_modifier_results = []
        for result in modifier_results:
            if result.activity_uuid not in seen_uuids:
                seen_uuids.add(result.activity_uuid)
                unique_modifier_results.append(result)
        
        results_dict["phase_2_modifiers"] = unique_modifier_results
    
    # Search with ISIC codes if found
    if isic_codes:
        isic_results = []
        for isic_code in isic_codes:
            # Create filter for ISIC code
            filter_metadata = {"isic_code": isic_code}
            results = search_emissions_factors_vectordb(
                user_query_primary, 
                iso_code=iso_code, 
                filter_metadata=filter_metadata
            )
            isic_results.extend(results)
        
        # Remove duplicates
        seen_uuids = set()
        unique_isic_results = []
        for result in isic_results:
            if result.activity_uuid not in seen_uuids:
                seen_uuids.add(result.activity_uuid)
                unique_isic_results.append(result)
        
        results_dict["phase_2_isic"] = unique_isic_results
    
    # Prepare final results
    return prepare_results(results_dict, modifier_used=bool(modifiers), isic_codes=isic_codes)


def search_emissions_factors(user_input: str) -> List[MatchedEF]:
    """Legacy function for backward compatibility"""
    return hybrid_predictions(user_query_primary=user_input)


def match_emission_factor(
    user_query: str,
    activity_name: str, 
    geography: str = None,
    lca_stage: str = None
) -> dict:
    """Legacy function for backward compatibility"""
    # This is a simplified version - actual implementation would need more logic
    results = hybrid_predictions(
        user_query_primary=user_query,
        lca_lifecycle_stage=lca_stage,
        iso_code=geography
    )
    
    if results:
        top_match = results[0]
        return {
            "activity_uuid": top_match.activity_uuid,
            "confidence": "high" if top_match.similarity_score > 0.8 else "medium",
            "match_explanation": f"Matched with similarity score {top_match.similarity_score}"
        }
    return None


def get_closest_match(
    prompt: str,
    data: List[tuple]
) -> dict | None:
    """
    Gets the closest match from a list of candidates.
    Now uses structured output with LangChain.
    """
    messages = [
        {
            "role": "system",
            "content": prompt
        },
        {
            "role": "user",
            "content": json.dumps(data, ensure_ascii=False, indent=2)
        }
    ]

    # Use structured completion
    result = get_structured_completion(
        messages=messages,
        response_model=ClosestMatchResponse,
        deployment=config.azure_openai_deployment,
        temperature=0,
    )

    if result:
        return {
            "activity_uuid": result.activity_uuid,
            "confidence": result.confidence,
            "match_explanation": result.match_explanation
        }
    else:
        logger.warning("Failed to get structured response for closest match")
        return None


async def get_technological_representation(process_name: str, activity_name: str):
    """
    Predict the Technological representativeness.
    Now uses structured output with LangChain.
    """
    prompt = (
        "You will be given a user's manufacturing process or technology and a target ecoinvent activity name.\n"
        "Your task is to determine if they represent similar technologies or processes.\n\n"
        
        "Consider these aspects:\n"
        "- Manufacturing methods and processes\n"
        "- Technology level (modern vs traditional)\n"
        "- Scale of operation\n"
        "- Core transformation process\n\n"
        
        "Respond with:\n"
        "- reason: A brief explanation of why they are similar or different\n"
        "- similar: true if they represent similar technologies, false otherwise\n\n"
        
        "Examples:\n"
        "User: 'CNC machining of aluminum parts'\n"
        "Activity: 'metal working, average for aluminium product manufacturing'\n"
        "Response: {\"reason\": \"Both involve aluminum processing, though CNC is more specific than average metal working\", \"similar\": true}\n\n"
        
        "User: 'Hand weaving of cotton fabric'\n"  
        "Activity: 'textile weaving, industrial scale'\n"
        "Response: {\"reason\": \"Hand weaving is traditional/artisanal while industrial weaving uses modern machinery at scale\", \"similar\": false}\n"
    )

    messages = [
        {
            "role": "system",
            "content": prompt
        },
        {
            "role": "user",
            "content": f"User process: '{process_name}'\nActivity name: '{activity_name}'"
        }
    ]

    # Use async structured completion
    async with get_openai_async_client() as client:
        result = await get_async_structured_completion(
            messages=messages,
            response_model=TechnologicalRepresentationResponse,
            deployment=config.azure_openai_deployment,
            temperature=0,
        )

    if result:
        return {
            "reason": result.reason,
            "similar": result.similar
        }
    else:
        logger.warning("Failed to get structured response for technological representation")
        return None


def ask_anthropic(prompt: str):
    # Placeholder function - actual implementation would use Anthropic API
    return "Not implemented"


def eol_activity_lookup(
    user_query: str, 
    iso_code: str = None,
    limit: int = 10
) -> List[MatchedEF]:
    """
    Searches for End-of-Life (EoL) activities based on user query.
    Uses the EOL-specific vector database.
    """
    from emissions_factor_matching.dataset import search_emissions_factors_vectordb_eol
    
    # Search in EOL database
    results = search_emissions_factors_vectordb_eol(
        user_query, 
        iso_code=iso_code,
        filter_metadata={},
        limit=limit
    )
    
    # Convert to MatchedEF format
    matched_results = []
    for candidate in results:
        matched_results.append(
            MatchedEF(
                activity_uuid=candidate.activity_uuid,
                similarity_score=candidate.similarity_score,
                ef_activity_name=candidate.ef_activity_name,
                ef_reference_unit=candidate.ef_reference_unit,
                phase="eol_search",
                modifier_used=False,
                isic_used=False,
            )
        )
    
    return matched_results


def re_rank_candidates(
    user_query: str,
    candidates_list: List[Dict[str, Any]],
    lca_lifecycle_stage: str = None,
    iso_code: str = None
) -> RerankingResponse | None:
    """
    Use LLM to re-rank and select the best candidate from a list.
    Now uses structured output with LangChain.
    """
    # Get the reranking prompt
    prompt = get_llm_reranking_prompt(
        user_query=user_query,
        candidates=candidates_list,
        lca_stage=lca_lifecycle_stage,
        iso_code=iso_code
    )
    
    messages = [
        {
            "role": "system",
            "content": "You are an expert in environmental life cycle assessment and emission factor matching."
        },
        {
            "role": "user",
            "content": prompt
        }
    ]
    
    # Use structured completion
    result = get_structured_completion(
        messages=messages,
        response_model=RerankingResponse,
        deployment=config.azure_openai_deployment,
        temperature=0,
    )
    
    return result