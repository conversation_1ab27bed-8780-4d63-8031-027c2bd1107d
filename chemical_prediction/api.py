from fastapi import FastAPI, HTTPException
from chemical_prediction.predictions import predict_hscode, predict_material_classification, get_valid_eol_product_category
from emissions_factor_matching.predictions import get_cas_number
from utils.cache import create_cache, cached

model_api = FastAPI()
cache = create_cache(prefix='chem_')

@model_api.get("/predict-hscode/{chemical_name:path}")
@cached(cache)
def predict_chemical_hscode(chemical_name: str):
    hscode = predict_hscode(chemical_name)
    return {"hscode": hscode}

@model_api.get("/predict-cas-number/{chemical_name:path}")
@cached(cache)
def predict_cas_number(chemical_name: str):
    cas_number = get_cas_number(chemical_name)
    return {"cas_number": cas_number}

@model_api.get("/predict-has-eol-disposal/{product_category:path}")
@cached(cache)
async def predict_has_eol_disposal(product_category: str):
    try:
        has_disposal_method = await get_valid_eol_product_category(product_category)
        return {"has_eol_disposal": has_disposal_method}
    except Exception as error:
        raise HTTPException(500, detail=f"Error determining if product category has EOL disposal method: {error}") from error

@model_api.get("/predict-eol-material-classification/{material:path}")
@cached(cache)
def predict_eol_material_classification(material: str):
    try:
        eol_material_classification = predict_material_classification(material)
        if eol_material_classification == "NONE":
            return {"classification": None}
        return {"classification": eol_material_classification}
    except Exception as error:
        raise HTTPException(
            500,
            detail=f"Error predicting EOL material classification: {error}"
        ) from error
