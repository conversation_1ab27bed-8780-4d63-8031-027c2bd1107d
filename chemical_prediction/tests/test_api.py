from unittest import TestCase
from fastapi.testclient import TestClient
from utils import logger
from chemical_prediction.api import model_api


class TestAPI(TestCase):
    client = TestClient(model_api)

    def test_xml_unsafe_string_has_eol_disposal(self):
        categories = [
            "Backpacks & Luggage",
            "Lotion & Cream",
            "Cosmetics & Beauty Care",
            "Cosmetics & Beauty Care > Shampoo",
            "Vehicles >3.5t",
            "Vehicles <3.5t",
        ]
        for category in categories:
            response = self.client.get(f"/predict-has-eol-disposal/{category}")
            logger.info(response.json())
            self.assertEqual(response.status_code, 200)
            self.assertIsNotNone(response.json()["has_eol_disposal"])
