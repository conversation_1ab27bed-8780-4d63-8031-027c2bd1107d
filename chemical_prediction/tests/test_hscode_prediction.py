from unittest import TestCase
from chemical_prediction.predictions import predict_hscode

validation_hscodes = [
    (
        "Sodium Chloride",
        ["25010000", "25010020", "25010091"],  # Accept any of these valid codes for sodium chloride
    ),
    (
        "Zinc Oxide",
        "28170000",
    ),
    (
        "Sodium lauryl sulfate",
        ["34021100", "34021190"],  # Accept both valid codes for sodium lauryl sulfate
    ),
    (
        "Sodium hydroxide",
        "28151100",
    ),
    (
        "citric acid",
        "29181400",
    ),
]

class TestChemicalHSCodePrediction(TestCase):
    def test_hscode_predictions(self):
        for chemical_name, validation_hscode in validation_hscodes:
            hscode = predict_hscode(chemical_name)

            # Handle the case where validation_hscode is a list (multiple valid codes)
            if isinstance(validation_hscode, list):
                self.assertIn(hscode, validation_hscode,
                             f"HS code {hscode} for {chemical_name} is not in the list of valid codes: {validation_hscode}")
            else:
                self.assertEqual(hscode, validation_hscode,
                                f"HS code {hscode} for {chemical_name} does not match expected: {validation_hscode}")