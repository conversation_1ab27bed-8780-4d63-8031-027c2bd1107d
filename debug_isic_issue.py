#!/usr/bin/env python3
"""
Debug script to understand the ISIC code issue
"""
import sys
sys.path.append('/home/<USER>/app')

from emissions_factor_matching.dataset import efs_with_geographies
import pandas as pd

def debug_isic_issue():
    print("=== DEBUGGING ISIC CODE ISSUE ===\n")
    
    # Check the exact format of ISIC codes
    print("1. Sample ISIC Classification values:")
    sample_isic = efs_with_geographies['ISIC Classification'].dropna().head(10)
    for i, code in enumerate(sample_isic):
        print(f"   {i+1}. '{code}' (type: {type(code)})")
    print()
    
    # Check if 4923 exists in different formats
    print("2. Checking for 4923 in different formats:")
    all_isic = efs_with_geographies['ISIC Classification'].dropna().unique()
    
    # Check exact match
    exact_match = '4923' in all_isic
    print(f"   Exact '4923' exists: {exact_match}")
    
    # Check with description
    desc_matches = [code for code in all_isic if '4923' in str(code)]
    print(f"   Codes containing '4923': {desc_matches}")
    
    # Check transport codes specifically
    print("\n3. All transport-related codes (H section):")
    transport_data = efs_with_geographies[
        efs_with_geographies['ISIC Section'] == 'H - Transportation and storage'
    ]
    transport_codes = transport_data['ISIC Classification'].unique()
    for code in sorted(transport_codes):
        count = len(transport_data[transport_data['ISIC Classification'] == code])
        print(f"   '{code}': {count} activities")
    
    # Check the exact code we're looking for
    print(f"\n4. Checking exact code '4923':")
    exact_4923 = efs_with_geographies[
        efs_with_geographies['ISIC Classification'] == '4923'
    ]
    print(f"   Found {len(exact_4923)} activities with exact '4923'")
    
    # Check with description format
    print(f"\n5. Checking code with description format:")
    desc_4923 = efs_with_geographies[
        efs_with_geographies['ISIC Classification'].str.contains('4923', na=False)
    ]
    print(f"   Found {len(desc_4923)} activities containing '4923'")
    
    if len(desc_4923) > 0:
        print(f"   Example: '{desc_4923['ISIC Classification'].iloc[0]}'")
    
    # Show the available_isic_codes logic
    print(f"\n6. Testing the available_isic_codes logic:")
    available_isic_codes = set(efs_with_geographies['ISIC Classification'].dropna().unique())
    print(f"   Total unique ISIC codes: {len(available_isic_codes)}")
    print(f"   '4923' in available_isic_codes: {'4923' in available_isic_codes}")
    
    # Check if it's the full description format
    freight_codes = [code for code in available_isic_codes if 'freight' in str(code).lower()]
    print(f"   Codes with 'freight': {freight_codes}")

if __name__ == "__main__":
    debug_isic_issue()
