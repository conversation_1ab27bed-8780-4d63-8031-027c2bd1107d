diff --git a/check_chroma_metadata.py b/check_chroma_metadata.py
new file mode 100644
index 0000000..2dc564c
--- /dev/null
+++ b/check_chroma_metadata.py
@@ -0,0 +1,48 @@
+#!/usr/bin/env python3
+"""
+Check ChromaDB metadata structure to understand ISIC field names
+"""
+import sys
+sys.path.append('/home/<USER>/app')
+
+from emissions_factor_matching.dataset import collection
+import json
+
+def check_chroma_metadata():
+    print("=== CHECKING CHROMADB METADATA STRUCTURE ===\n")
+    
+    # Query a few transport-related activities to see metadata structure
+    results = collection.query(
+        query_texts=["freight transport truck"],
+        where={"activity_type": {"$eq": "ordinary transforming activity"}},
+        n_results=3,
+        include=['metadatas', 'documents', 'distances']
+    )
+    
+    print("Sample ChromaDB metadata for transport activities:")
+    print(f"Found {len(results['ids'][0])} results\n")
+    
+    for i in range(len(results['ids'][0])):
+        print(f"--- Result {i+1} ---")
+        print(f"ID: {results['ids'][0][i]}")
+        print(f"Document: {results['documents'][0][i]}")
+        print(f"Distance: {results['distances'][0][i]:.4f}")
+        
+        metadata = results['metadatas'][0][i]
+        print("Metadata keys:")
+        for key in sorted(metadata.keys()):
+            print(f"  {key}: {metadata[key]}")
+        print()
+        
+        # Check for ISIC-related fields
+        isic_fields = [key for key in metadata.keys() if 'isic' in key.lower()]
+        if isic_fields:
+            print(f"ISIC-related fields: {isic_fields}")
+            for field in isic_fields:
+                print(f"  {field}: {metadata[field]}")
+        else:
+            print("No ISIC-related fields found")
+        print()
+
+if __name__ == "__main__":
+    check_chroma_metadata()
diff --git a/check_isic_codes.py b/check_isic_codes.py
new file mode 100644
index 0000000..2e44e60
--- /dev/null
+++ b/check_isic_codes.py
@@ -0,0 +1,62 @@
+#!/usr/bin/env python3
+"""
+Quick script to check what ISIC codes are available in our database
+"""
+import sys
+sys.path.append('/home/<USER>/app')
+
+from emissions_factor_matching.dataset import efs_with_geographies
+import pandas as pd
+
+def check_isic_codes():
+    print("=== ISIC Codes Available in Database ===\n")
+    
+    # Check what columns exist
+    print("Available columns:")
+    for col in efs_with_geographies.columns:
+        if 'isic' in col.lower() or 'ISIC' in col:
+            print(f"  - {col}")
+    print()
+    
+    # Check ISIC Sections
+    if 'ISIC Section' in efs_with_geographies.columns:
+        print("ISIC Sections available:")
+        sections = efs_with_geographies['ISIC Section'].value_counts()
+        for section, count in sections.items():
+            print(f"  {section}: {count} activities")
+        print()
+    
+    # Check ISIC Classifications  
+    if 'ISIC Classification' in efs_with_geographies.columns:
+        print("ISIC Classifications (first 20):")
+        classifications = efs_with_geographies['ISIC Classification'].value_counts().head(20)
+        for classification, count in classifications.items():
+            print(f"  {classification}: {count} activities")
+        print(f"... and {len(efs_with_geographies['ISIC Classification'].unique()) - 20} more")
+        print()
+        
+        # Show transport-related ISIC codes specifically
+        print("Transport-related ISIC codes (H section):")
+        transport_codes = efs_with_geographies[
+            efs_with_geographies['ISIC Section'] == 'H - Transportation and storage'
+        ]['ISIC Classification'].value_counts()
+        for code, count in transport_codes.items():
+            print(f"  {code}: {count} activities")
+        print()
+        
+        # Show manufacturing codes (C section)
+        print("Manufacturing ISIC codes (C section - first 10):")
+        manufacturing_codes = efs_with_geographies[
+            efs_with_geographies['ISIC Section'] == 'C - Manufacturing'
+        ]['ISIC Classification'].value_counts().head(10)
+        for code, count in manufacturing_codes.items():
+            print(f"  {code}: {count} activities")
+        print()
+    
+    # Total counts
+    print(f"Total activities in database: {len(efs_with_geographies)}")
+    print(f"Unique ISIC sections: {len(efs_with_geographies['ISIC Section'].unique())}")
+    print(f"Unique ISIC classifications: {len(efs_with_geographies['ISIC Classification'].unique())}")
+
+if __name__ == "__main__":
+    check_isic_codes()
diff --git a/chemical_prediction/predictions.py b/chemical_prediction/predictions.py
index cf16417..ef8a0bb 100644
--- a/chemical_prediction/predictions.py
+++ b/chemical_prediction/predictions.py
@@ -1,18 +1,38 @@
-from openai import AzureOpenAI
-from completions import get_chat_completion
-from completion_utils import parse_template_response
+from typing import Literal
+from pydantic import BaseModel, Field
+from completions import get_chat_completion, get_structured_completion
 from config import config
 from clients import get_phi_client
 from completions import get_azure_completion
+from completion_utils import parse_template_response
 from utils.strings import make_xml_safe
 
-client = AzureOpenAI(
-    api_key=config.azure_openai_api_key,
-    api_version=config.azure_api_version,
-    azure_endpoint=config.azure_openai_endpoint,
-)
 
-async def get_valid_eol_product_category(product_category: str) -> str:
+class EOLProductCategoryResponse(BaseModel):
+    """Response model for EOL product category validation"""
+    explanation: str = Field(description="Explanation of why the product is or isn't EOL eligible")
+    is_eol_eligible: bool = Field(description="Whether the product is NOT a liquid, soluble, or topical product")
+
+
+class HSCodeResponse(BaseModel):
+    """Response model for HS code prediction"""
+    hs_code: str = Field(description="The harmonized system code for the chemical")
+
+
+class MaterialClassificationResponse(BaseModel):
+    """Response model for material classification"""
+    classification: Literal['Plastic', 'Paper', 'Cardboard', 'Aluminium', 'Glass', 'Steel', 'Wood', 'NONE'] = Field(
+        description="The material classification"
+    )
+
+
+async def get_valid_eol_product_category(product_category: str) -> bool:
+    """
+    Determine if a product category is valid for EOL (End of Life) processing.
+    Returns True if the product is NOT a liquid, soluble, or topical product.
+    
+    Currently still uses Phi-3 with XML parsing for compatibility.
+    """
     messages = [
         {
             "role": "system",
@@ -26,20 +46,28 @@ async def get_valid_eol_product_category(product_category: str) -> str:
             "content": f"Product Category: '{make_xml_safe(product_category)}'",
         }
     ]
+    
+    # Still using Phi-3 for this specific use case
     async with get_phi_client() as phi_client:
         completion = await get_azure_completion(phi_client, messages)
+    
     response_dict = parse_template_response(completion)
     return response_dict["ANSWER"].strip().lower() == "false"
 
+
 def predict_hscode(chemical_name: str) -> str:
+    """
+    Predict the Harmonized System (HS) code for a chemical.
+    Now uses structured output with LangChain.
+    """
     messages = [
         {
             "role": "system",
             "content": (
-                "Given a chemical return the harmonized code with no additional text.\n"
+                "Given a chemical, return the harmonized system (HS) code.\n"
+                "The HS code should be a valid code used in international trade.\n"
                 "Example:\n"
-                "Chemical: 'Water'\n"
-                "22019000"
+                "Chemical: 'Water' -> HS Code: '22019000'"
             )
         },
         {
@@ -48,23 +76,39 @@ def predict_hscode(chemical_name: str) -> str:
         }
     ]
 
-    completion = get_chat_completion(client, messages, deployment=config.azure_openai_deployment)
-    return completion
+    # Use structured completion
+    result = get_structured_completion(
+        messages=messages,
+        response_model=HSCodeResponse,
+        deployment=config.azure_openai_deployment,
+        temperature=0,
+    )
+
+    return result.hs_code if result else ""
+
 
 def predict_material_classification(material: str) -> str:
+    """
+    Predict the material classification for a given material.
+    Now uses structured output with LangChain.
+    """
     messages = [
         {
             "role": "system",
             "content": (
-                "Given a material return the material classification with no additional text.\n"
-                "Output ('Plastic' | 'Paper' | 'Cardboard' | 'Aluminium' | 'Glass' | 'Steel' | 'Wood' | 'NONE')\n\n"
-                "Some Examples:\n"
-                "Material: 'PET'\n"
-                "Plastic\n\n"
-                "Material: 'Cardstock'\n"
-                "Paper\n\n"
-                "Material: 'Brass Clamp'\n"
-                "NONE"
+                "Given a material, classify it into one of the following categories:\n"
+                "- Plastic: Any plastic-based material (PET, HDPE, PVC, etc.)\n"
+                "- Paper: Paper-based materials\n"
+                "- Cardboard: Cardboard or corrugated materials\n"
+                "- Aluminium: Aluminium-based materials\n"
+                "- Glass: Glass-based materials\n"
+                "- Steel: Steel or iron-based materials\n"
+                "- Wood: Wood-based materials\n"
+                "- NONE: If the material doesn't fit any category\n\n"
+                "Examples:\n"
+                "Material: 'PET' -> Classification: 'Plastic'\n"
+                "Material: 'Cardstock' -> Classification: 'Paper'\n"
+                "Material: 'Brass Clamp' -> Classification: 'NONE'"
             )
         },
         {
@@ -73,5 +117,12 @@ def predict_material_classification(material: str) -> str:
         }
     ]
 
-    completion = get_chat_completion(client, messages, deployment=config.azure_openai_deployment)
-    return completion
+    # Use structured completion
+    result = get_structured_completion(
+        messages=messages,
+        response_model=MaterialClassificationResponse,
+        deployment=config.azure_openai_deployment,
+        temperature=0,
+    )
+
+    return result.classification if result else "NONE"
\ No newline at end of file
diff --git a/clients.py b/clients.py
index 29f2998..67ec4d2 100644
--- a/clients.py
+++ b/clients.py
@@ -1,5 +1,5 @@
 import asyncio
-from openai import AzureOpenAI, AsyncAzureOpenAI
+from langchain_openai import AzureChatOpenAI
 from azure.ai.inference.aio import ChatCompletionsClient as AsyncChatCompletionsClient
 from azure.core.credentials import AzureKeyCredential
 from config import config
@@ -23,6 +23,23 @@ def get_phi_client():
         )
     )
 
+# LangChain Azure OpenAI client - replaces the old OpenAI client
+def get_langchain_client(temperature: float = 0, deployment: str = None):
+    """Get a LangChain Azure OpenAI client with structured output support"""
+    return AzureChatOpenAI(
+        azure_endpoint=config.azure_openai_endpoint,
+        api_key=config.azure_openai_api_key,
+        api_version=config.azure_api_version,
+        azure_deployment=deployment or config.azure_openai_deployment,
+        temperature=temperature,
+    )
+
+# Default client instances
+langchain_client = get_langchain_client()
+langchain_client_us_east = get_langchain_client()  # Using same endpoint for now
+
+# Legacy compatibility - these will be removed after full migration
+from openai import AzureOpenAI, AsyncAzureOpenAI
 
 openai_client = AzureOpenAI(
     api_key=config.azure_openai_api_key,
@@ -42,4 +59,4 @@ openai_client_us_east = AzureOpenAI(
     api_key=config.azure_openai_api_key,
     api_version=config.azure_api_version,
     azure_endpoint=config.azure_openai_endpoint,
-)
+)
\ No newline at end of file
diff --git a/completions.py b/completions.py
index 5e543a5..4f14a9c 100644
--- a/completions.py
+++ b/completions.py
@@ -1,19 +1,18 @@
-from typing import List, TypedDict
+from typing import List, TypedDict, Type, TypeVar, Optional
 import time
 import asyncio
-from openai import (
-    AzureOpenAI,
-    AsyncAzureOpenAI,
-    OpenAI,
-    _exceptions as OpenAIExceptions
-)
-from azure.core.exceptions import HttpResponseError
+from pydantic import BaseModel
+from langchain_openai import AzureChatOpenAI
+from langchain_core.prompts import ChatPromptTemplate
+from langchain_core.messages import HumanMessage, SystemMessage
 import sentry_sdk
 from sentry_sdk.ai.monitoring import ai_track
-from clients import AsyncChatCompletionsClient
+from azure.core.exceptions import HttpResponseError
+from clients import AsyncChatCompletionsClient, get_langchain_client
 from config import config
 from utils import logger
 
+T = TypeVar('T', bound=BaseModel)
 
 class Message(TypedDict):
     role: str
@@ -27,6 +26,7 @@ async def _get_azure_completion(
     client: AsyncChatCompletionsClient,
     messages: List[Message]
 ) -> str | None:
+    """Legacy Azure completion function for Phi-3 model"""
     retry_count = 0
     while True:
         with sentry_sdk.start_transaction(op="azure-chat-completion"):
@@ -62,53 +62,125 @@ async def get_azure_completion(
     client: AsyncChatCompletionsClient,
     messages: List[Message],
 ) -> str | None:
+    """Legacy Azure completion function for Phi-3 model"""
     try:
         return await _get_azure_completion(client, messages)
     except Exception as error:
         logger.error(f"Error getting chat completion: {error}")
         return None
 
-@ai_track("Chat Completion")
-async def _get_async_completion(
-    client: AsyncAzureOpenAI,
+def convert_messages_to_langchain(messages: List[Message]):
+    """Convert OpenAI message format to LangChain message format"""
+    langchain_messages = []
+    for msg in messages:
+        if msg["role"] == "system":
+            langchain_messages.append(SystemMessage(content=msg["content"]))
+        elif msg["role"] == "user":
+            langchain_messages.append(HumanMessage(content=msg["content"]))
+    return langchain_messages
+
+@ai_track("LangChain Structured Completion")
+def get_structured_completion(
     messages: List[Message],
-    deployment: str = config.azure_openai_deployment,
+    response_model: Type[T],
+    deployment: str = None,
     temperature: float = 0,
-    strict: bool = False,
-    model: str = None,
-) -> str | None:
-    # For backward compatibility, use model if provided
-    model_param = model if model is not None else deployment
-
+    max_retries: int = 3,
+) -> T | None:
+    """
+    Get a structured completion using LangChain with guaranteed Pydantic model output.
+    
+    Args:
+        messages: List of messages in OpenAI format
+        response_model: Pydantic model class for the response
+        deployment: Azure deployment name (optional)
+        temperature: Temperature for the model
+        max_retries: Number of retries for transient errors
+    
+    Returns:
+        Instance of response_model or None if error
+    """
     retry_count = 0
-    while True:
-        with sentry_sdk.start_transaction(op="async-chat-completion"):
+    while retry_count < max_retries:
+        with sentry_sdk.start_transaction(op="langchain-structured-completion"):
             try:
-                response = await client.chat.completions.create(
-                    messages=messages,
-                    model=model_param,  # Azure OpenAI API expects 'model' parameter
-                    temperature=temperature,
-                    response_format={"type": "json_object"} if strict else None,
-                )
-                completion = response.choices[0].message.content
-
-                logger.info(f"Completion: {completion}")
-
-                return completion
-
-            except OpenAIExceptions.RateLimitError as error:
-                if retry_count > config.rate_limit_retry_count:
-                    raise RateLimitExceeded(
-                        "Exceeded number of retries, Rate Limit Exceeded."
-                    ) from error
+                # Get LangChain client
+                client = get_langchain_client(temperature=temperature, deployment=deployment)
+                
+                # Configure for structured output
+                structured_llm = client.with_structured_output(response_model)
+                
+                # Convert messages to LangChain format
+                langchain_messages = convert_messages_to_langchain(messages)
+                
+                # Get structured response
+                result = structured_llm.invoke(langchain_messages)
+                
+                logger.info(f"Structured completion successful: {type(result).__name__}")
+                return result
+                
+            except Exception as e:
+                if "429" in str(e) or "rate limit" in str(e).lower():
+                    if retry_count >= max_retries - 1:
+                        logger.error("Exceeded max retries for rate limit")
+                        return None
+                    
+                    sleep_time = min(2**retry_count, 120)
+                    retry_count += 1
+                    logger.warning(f"Rate limit hit, retrying after {sleep_time}s...")
+                    time.sleep(sleep_time)
+                else:
+                    logger.error(f"Error in structured completion: {e}")
+                    return None
 
-                sleep_time = min(2**retry_count, 120)
-                retry_count += 1
-                logger.warning(f"Exceeded rate limit, retrying after {sleep_time}s.")
-                time.sleep(sleep_time)
+@ai_track("LangChain Async Structured Completion")
+async def get_async_structured_completion(
+    messages: List[Message],
+    response_model: Type[T],
+    deployment: str = None,
+    temperature: float = 0,
+    max_retries: int = 3,
+) -> T | None:
+    """
+    Async version of get_structured_completion.
+    """
+    retry_count = 0
+    while retry_count < max_retries:
+        with sentry_sdk.start_transaction(op="langchain-async-structured-completion"):
+            try:
+                # Get LangChain client
+                client = get_langchain_client(temperature=temperature, deployment=deployment)
+                
+                # Configure for structured output
+                structured_llm = client.with_structured_output(response_model)
+                
+                # Convert messages to LangChain format
+                langchain_messages = convert_messages_to_langchain(messages)
+                
+                # Get structured response
+                result = await structured_llm.ainvoke(langchain_messages)
+                
+                logger.info(f"Async structured completion successful: {type(result).__name__}")
+                return result
+                
+            except Exception as e:
+                if "429" in str(e) or "rate limit" in str(e).lower():
+                    if retry_count >= max_retries - 1:
+                        logger.error("Exceeded max retries for rate limit")
+                        return None
+                    
+                    sleep_time = min(2**retry_count, 120)
+                    retry_count += 1
+                    logger.warning(f"Rate limit hit, retrying after {sleep_time}s...")
+                    await asyncio.sleep(sleep_time)
+                else:
+                    logger.error(f"Error in async structured completion: {e}")
+                    return None
 
-async def get_async_chat_completion(
-    client: AsyncAzureOpenAI,
+# Legacy functions for backward compatibility - these use the new structured output internally
+@ai_track("Chat Completion")
+def get_chat_completion(
+    client,  # This parameter is ignored now
     messages: List[Message],
     deployment: str = config.azure_openai_deployment,
     temperature: float = 0,
@@ -116,80 +188,41 @@ async def get_async_chat_completion(
     strict: bool = False,
     model: str = None,
 ) -> str | None:
-    # For backward compatibility, use model if provided
-    model_param = model if model is not None else deployment
-
+    """
+    Legacy function for backward compatibility.
+    Now uses LangChain for unstructured completions.
+    """
+    deployment = model if model is not None else deployment
+    
     try:
-        completion = await _get_async_completion(
-            client,
-            messages,
-            deployment=model_param,
-            temperature=temperature,
-            strict=strict,
-        )
+        # Get LangChain client
+        langchain_client = get_langchain_client(temperature=temperature, deployment=deployment)
+        
+        # Convert messages
+        langchain_messages = convert_messages_to_langchain(messages)
+        
+        # Get completion
+        response = langchain_client.invoke(langchain_messages)
+        completion = response.content
+        
+        logger.info(f"Completion: {completion}")
+        
+        # Handle validations if needed (though this is less relevant with LangChain)
+        for _ in range(n_validations):
+            validation_response = langchain_client.invoke(langchain_messages)
+            if completion != validation_response.content:
+                logger.warning("Validation did not match chat completion returning None")
+                return None
+        
+        return completion
+        
     except Exception as error:
         logger.error(f"Error getting chat completion: {error}")
         return None
 
-    for _ in range(n_validations):
-        try:
-            validation_completion = _get_completion(
-                client,
-                messages,
-                deployment=model_param,
-                temperature=temperature,
-                strict=strict,
-            )
-
-            if completion != validation_completion:
-                logger.warning("Validation did not match chat completion returning None")
-                return None
-
-        except Exception as error:
-            logger.warning(f"Error getting validation chat completion: {error}")
-            return completion
-    return completion
-
-@ai_track("Chat Completion")
-def _get_completion(
-    client: AzureOpenAI | OpenAI,
-    messages: List[Message],
-    deployment: str = config.azure_openai_deployment,
-    temperature: float = 0,
-    strict: bool = False,
-    model: str = None,
-) -> str | None:
-    # For backward compatibility, use model if provided
-    model_param = model if model is not None else deployment
-
-    retry_count = 0
-    while True:
-        with sentry_sdk.start_transaction(op="chat-completion"):
-            try:
-                completion = client.chat.completions.create(
-                    messages=messages,
-                    model=model_param,  # Azure OpenAI API expects 'model' parameter
-                    temperature=temperature,
-                    response_format={"type": "json_object"} if strict else None,
-                ).choices[0].message.content
-
-                logger.info(f"Completion: {completion}")
-
-                return completion
-
-            except OpenAIExceptions.RateLimitError as error:
-                if retry_count > config.rate_limit_retry_count:
-                    raise RateLimitExceeded(
-                        "Exceeded number of retries, Rate Limit Exceeded."
-                    ) from error
-
-                sleep_time = min(2**retry_count, 120)
-                retry_count += 1
-                logger.warning(f"Exceeded rate limit, retrying after {sleep_time}s.")
-                time.sleep(sleep_time)
-
-def get_chat_completion(
-    client: AzureOpenAI | OpenAI,
+@ai_track("Async Chat Completion")
+async def get_async_chat_completion(
+    client,  # This parameter is ignored now
     messages: List[Message],
     deployment: str = config.azure_openai_deployment,
     temperature: float = 0,
@@ -197,36 +230,34 @@ def get_chat_completion(
     strict: bool = False,
     model: str = None,
 ) -> str | None:
-    # For backward compatibility, use model if provided
-    model_param = model if model is not None else deployment
-
+    """
+    Legacy async function for backward compatibility.
+    Now uses LangChain for unstructured completions.
+    """
+    deployment = model if model is not None else deployment
+    
     try:
-        completion = _get_completion(
-            client,
-            messages,
-            deployment=model_param,
-            temperature=temperature,
-            strict=strict,
-        )
-    except Exception as error:
-        logger.error(f"Error getting chat completion: {error}")
-        return None
-
-    for _ in range(n_validations):
-        try:
-            validation_completion = _get_completion(
-                client,
-                messages,
-                deployment=model_param,
-                temperature=temperature,
-                strict=strict,
-            )
-
-            if completion != validation_completion:
+        # Get LangChain client
+        langchain_client = get_langchain_client(temperature=temperature, deployment=deployment)
+        
+        # Convert messages
+        langchain_messages = convert_messages_to_langchain(messages)
+        
+        # Get completion
+        response = await langchain_client.ainvoke(langchain_messages)
+        completion = response.content
+        
+        logger.info(f"Completion: {completion}")
+        
+        # Handle validations if needed
+        for _ in range(n_validations):
+            validation_response = await langchain_client.ainvoke(langchain_messages)
+            if completion != validation_response.content:
                 logger.warning("Validation did not match chat completion returning None")
                 return None
-
-        except Exception as error:
-            logger.warning(f"Error getting validation chat completion: {error}")
-            return completion
-    return completion
+        
+        return completion
+        
+    except Exception as error:
+        logger.error(f"Error getting async chat completion: {error}")
+        return None
\ No newline at end of file
diff --git a/config.py b/config.py
index fc91810..c8c594f 100644
--- a/config.py
+++ b/config.py
@@ -33,7 +33,7 @@ class Config:
         default_factory=lambda: get_required_env_var("AZURE_OPENAI_ENDPOINT")
     )
     azure_api_version: str = field(
-        default_factory=lambda: get_optional_env_var("AZURE_API_VERSION", "2024-02-01")
+        default_factory=lambda: get_optional_env_var("AZURE_API_VERSION", "2024-08-01-preview")
     )
     azure_openai_deployment: str = field(
         default_factory=lambda: get_optional_env_var(
diff --git a/debug_isic_issue.py b/debug_isic_issue.py
new file mode 100644
index 0000000..80f056e
--- /dev/null
+++ b/debug_isic_issue.py
@@ -0,0 +1,71 @@
+#!/usr/bin/env python3
+"""
+Debug script to understand the ISIC code issue
+"""
+import sys
+sys.path.append('/home/<USER>/app')
+
+from emissions_factor_matching.dataset import efs_with_geographies
+import pandas as pd
+
+def debug_isic_issue():
+    print("=== DEBUGGING ISIC CODE ISSUE ===\n")
+    
+    # Check the exact format of ISIC codes
+    print("1. Sample ISIC Classification values:")
+    sample_isic = efs_with_geographies['ISIC Classification'].dropna().head(10)
+    for i, code in enumerate(sample_isic):
+        print(f"   {i+1}. '{code}' (type: {type(code)})")
+    print()
+    
+    # Check if 4923 exists in different formats
+    print("2. Checking for 4923 in different formats:")
+    all_isic = efs_with_geographies['ISIC Classification'].dropna().unique()
+    
+    # Check exact match
+    exact_match = '4923' in all_isic
+    print(f"   Exact '4923' exists: {exact_match}")
+    
+    # Check with description
+    desc_matches = [code for code in all_isic if '4923' in str(code)]
+    print(f"   Codes containing '4923': {desc_matches}")
+    
+    # Check transport codes specifically
+    print("\n3. All transport-related codes (H section):")
+    transport_data = efs_with_geographies[
+        efs_with_geographies['ISIC Section'] == 'H - Transportation and storage'
+    ]
+    transport_codes = transport_data['ISIC Classification'].unique()
+    for code in sorted(transport_codes):
+        count = len(transport_data[transport_data['ISIC Classification'] == code])
+        print(f"   '{code}': {count} activities")
+    
+    # Check the exact code we're looking for
+    print(f"\n4. Checking exact code '4923':")
+    exact_4923 = efs_with_geographies[
+        efs_with_geographies['ISIC Classification'] == '4923'
+    ]
+    print(f"   Found {len(exact_4923)} activities with exact '4923'")
+    
+    # Check with description format
+    print(f"\n5. Checking code with description format:")
+    desc_4923 = efs_with_geographies[
+        efs_with_geographies['ISIC Classification'].str.contains('4923', na=False)
+    ]
+    print(f"   Found {len(desc_4923)} activities containing '4923'")
+    
+    if len(desc_4923) > 0:
+        print(f"   Example: '{desc_4923['ISIC Classification'].iloc[0]}'")
+    
+    # Show the available_isic_codes logic
+    print(f"\n6. Testing the available_isic_codes logic:")
+    available_isic_codes = set(efs_with_geographies['ISIC Classification'].dropna().unique())
+    print(f"   Total unique ISIC codes: {len(available_isic_codes)}")
+    print(f"   '4923' in available_isic_codes: {'4923' in available_isic_codes}")
+    
+    # Check if it's the full description format
+    freight_codes = [code for code in available_isic_codes if 'freight' in str(code).lower()]
+    print(f"   Codes with 'freight': {freight_codes}")
+
+if __name__ == "__main__":
+    debug_isic_issue()
diff --git a/dependencies/linux/requirements.txt b/dependencies/linux/requirements.txt
index fc0213b..ca6c9d3 100644
--- a/dependencies/linux/requirements.txt
+++ b/dependencies/linux/requirements.txt
@@ -150,7 +150,9 @@ olefile==0.47
 omegaconf==2.3.0
 onnx==1.16.1
 onnxruntime==1.18.0
-openai==1.16.2
+openai>=1.68.2
+langchain-openai>=0.3.18
+langchain-core>=0.3.62
 opencv-python==*********
 opencv-python-headless==*********
 openpyxl==3.1.3
diff --git a/dependencies/macos/requirements.txt b/dependencies/macos/requirements.txt
index 652d62a..622d10e 100644
--- a/dependencies/macos/requirements.txt
+++ b/dependencies/macos/requirements.txt
@@ -97,6 +97,8 @@ omegaconf==2.3.0
 onnx==1.17.0
 onnxruntime==1.20.1
 openai==1.59.7
+langchain-openai==0.3.18
+langchain-core==0.3.62
 opencv-python==4.10.0.84
 openpyxl==3.1.5
 opentelemetry-api==1.29.0
diff --git a/emissions_factor_matching/api.py b/emissions_factor_matching/api.py
index 9ac9c1f..e28d67d 100644
--- a/emissions_factor_matching/api.py
+++ b/emissions_factor_matching/api.py
@@ -14,6 +14,12 @@ from emissions_factor_matching.dataset import collection, collection_eol
 from emissions_factor_matching.predictions import (
     get_cas_number,
     predict_input_category,
+    predict_enhanced_input_category,
+    spot_modifiers,
+    map_isic_classification,
+    augment_query_text,
+    construct_dynamic_filters,
+    re_rank_candidates,
     predict_product_constituents,
     get_common_chemical_names,
     get_product_description,
@@ -21,6 +27,7 @@ from emissions_factor_matching.predictions import (
     get_closest_match,
     get_technological_representation,
 )
+from emissions_factor_matching.dataset import search_candidates_with_fallback
 from shared_predictions import get_top_match
 from emissions_factor_matching.geography import get_geography_activity_match
 from utils import logger
@@ -63,15 +70,20 @@ class ActivityResponse(Activity):
     similarity: float | None = None
 
 class ActivityRecommendationsRequest(BaseModel):
-    chemical_name: str
-    valid_units: list[str] | None = None
-    product_category: str | None = None
-    cas_number: str | None = None
+    user_query_primary: str | None = None  # Optional to support backward compatibility
+    user_query_secondary: str | None = None
+    lca_lifecycle_stage: str | None = None  # Optional for now - frontend doesn't send yet
     iso_code: str | None = None
+    valid_units: list[str] | None = None
+    product_category_context: str | None = None
     number_of_matches: int | None = None
     lcia_method: str | None = None
     carbon_only: bool | None = None
 
+    # Backward compatibility fields - will be deprecated
+    chemical_name: str | None = None
+    cas_number: str | None = None
+
 class ActivityRecommendationsResponse(BaseModel):
     matched_activity: Activity
     confidence: str
@@ -83,11 +95,13 @@ def get_activity_from_dataset(
     iso_code: str,
     similarity: float,
     reference_product_name: str,
+    preferred_source: str | None = None,
 ) -> ActivityResponse:
     activity = get_geography_activity_match(
         activity_name,
         iso_code,
-        reference_product_name
+        reference_product_name,
+        preferred_source
     )
 
     return ActivityResponse(
@@ -191,124 +205,109 @@ async def get_recommended_activities(
     api_version: str = Depends(get_api_version),
 ):
     logger.info(f"API Version: {api_version}")
-
-    if not activity_request.cas_number:
+    logger.info("Phase 1: Enhanced Input Category Prediction - Starting")
+
+    # Handle backward compatibility: convert old chemical_name to new format
+    if activity_request.chemical_name and not activity_request.user_query_primary:
+        activity_request.user_query_primary = activity_request.chemical_name
+        logger.info(f"Backward compatibility: converted chemical_name to user_query_primary")
+
+    # Ensure we have a primary query (either directly or from backward compatibility)
+    if not activity_request.user_query_primary:
+        raise HTTPException(status_code=400, detail="user_query_primary or chemical_name is required")
+
+    # Phase 1.1: Enhanced Input Category Prediction
+    enhanced_category = predict_enhanced_input_category(activity_request)
+    logger.info(f"Phase 1 Complete: Enhanced category = {enhanced_category}")
+
+    # Phase 1.2: Modifier Spotting
+    logger.info("Phase 2: Modifier Spotting - Starting")
+    modifiers = spot_modifiers(activity_request, enhanced_category)
+    logger.info(f"Phase 2 Complete: Extracted {len(modifiers)} modifiers = {modifiers}")
+
+    # Phase 1.3: ISIC Classification Mapping
+    logger.info("Phase 3: ISIC Classification Mapping - Starting")
+    isic_codes = map_isic_classification(enhanced_category, modifiers, activity_request.user_query_primary)
+    logger.info(f"Phase 3 Complete: Mapped to {len(isic_codes)} ISIC codes = {isic_codes}")
+
+    # Phase 1.4: Query Text Augmentation
+    logger.info("Phase 4: Query Text Augmentation - Starting")
+    augmented_query = augment_query_text(activity_request, enhanced_category, modifiers, isic_codes)
+    logger.info(f"Phase 4 Complete: Augmented query = '{augmented_query}'")
+
+    # Phase 1.5: Dynamic Filter Construction
+    logger.info("Phase 5: Dynamic Filter Construction - Starting")
+    dynamic_filters = construct_dynamic_filters(activity_request, enhanced_category, modifiers, isic_codes)
+    logger.info(f"Phase 5 Complete: Dynamic filters = {dynamic_filters}")
+
+    # Handle CAS number for chemicals (backward compatibility)
+    if not activity_request.cas_number and activity_request.chemical_name:
         activity_request.cas_number = get_cas_number(activity_request.chemical_name)
+    logger.info(f"CAS Number: {activity_request.cas_number}")
 
-    logger.info(activity_request.cas_number)
-
-    number_of_matches = activity_request.number_of_matches or 5
     iso_code = activity_request.iso_code or "GLO"
-    material = activity_request.chemical_name
-    category = predict_input_category(material)
-
-    if category == "PRODUCT":
-        if activity_request.product_category:
-            material += f" for {activity_request.product_category}"
-
-        constituent = predict_product_constituents(material)
-        query_text = f"{constituent} {material}"
-    else:
-        common_names = get_common_chemical_names(material)
-        logger.warning(common_names)
-
-        description = get_product_description(material)
-        logger.warning(description)
-
-        query_text = (
-            f"{material} ({common_names}): {description}"
-            if common_names != "NONE"
-            else f"{material}: {description}"
-        )
-
-    where = {"activity_type": {"$eq": "ordinary transforming activity"}}
-    product_section = get_product_activity_isic_section(material)
-    if product_section:
-        where = {
-            "$and": [
-                {"activity_type": {"$eq": "ordinary transforming activity"}},
-                {"isic_section": {"$eq": product_section}},
-            ],
-        }
 
-    if activity_request.valid_units:
-        if "$and" in where:
-            where["$and"].append({"unit": {"$in": activity_request.valid_units}})
-        else:
-            where = {
-                "$and": [
-                    {"activity_type": {"$eq": "ordinary transforming activity"}},
-                    {"unit": {"$in": activity_request.valid_units}},
-                ],
-            }
-    
-    if activity_request.lcia_method and not activity_request.carbon_only:
-        if "$and" in where:
-            where["$and"].append({activity_request.lcia_method: {"$eq": True}})
-        else:
-            where = {
-                "$and": [
-                    {"activity_type": {"$eq": "ordinary transforming activity"}},
-                    {activity_request.lcia_method: {"$eq": True}},
-                ],
-            }
-
-    documents = collection.query(
-        query_texts=[query_text],
-        n_results=25,
-        where=where
+    # Phase 1.6: ChromaDB Vector Search Enhancement
+    logger.info("Phase 6: ChromaDB Vector Search Enhancement - Starting")
+    candidates = search_candidates_with_fallback(
+        augmented_query=augmented_query,
+        filters=dynamic_filters,
+        n_results=25
     )
-
-    activities = []
-    activities_for_selection = []
-    for i in range(len(documents["ids"][0])):
-        activity = {
-            "uuid": documents["metadatas"][0][i]["uuid"],
-            "activity_name": documents["documents"][0][i],
-            "metadata": documents["metadatas"][0][i],
-            "similarity": 0,
-        }
-        activities.append(activity)
-        activities_for_selection.append({
-            k: v
-            for k, v in activity.items()
-            if k != "similarity"
-        })
-
-    closest_match = await get_closest_match(
-        query_text,
-        activities_for_selection,
+    logger.info(f"Phase 6 Complete: Retrieved {len(candidates)} candidates")
+
+    # Phase 1.7: LLM Re-ranking & Justification
+    logger.info("Phase 7: LLM Re-ranking & Justification - Starting")
+    if not candidates:
+        raise HTTPException(status_code=404, detail="No emission factor candidates found")
+
+    matched_ef = re_rank_candidates(
+        request_model=activity_request,
+        candidates=candidates,
+        augmented_query=augmented_query,
+        enhanced_category=enhanced_category,
+        modifiers=modifiers,
+        isic_codes=isic_codes
     )
-
-    matched_activity = next(
-        (
-            get_activity_from_dataset(
-                activity["activity_name"],
-                iso_code,
-                activity["similarity"],
-                activity["metadata"]["reference_product_name"]
-            )
-            for activity in activities
-            if closest_match.get("activity_uuid") == activity["uuid"]
-        ),
-        None,
+    logger.info(f"Phase 7 Complete: Selected '{matched_ef.activity_name}' with {matched_ef.confidence} confidence")
+
+    # Phase 1.8: Geography Matching & Record Retrieval
+    logger.info("Phase 8: Geography Matching & Record Retrieval - Starting")
+    matched_activity = get_activity_from_dataset(
+        activity_name=matched_ef.activity_name,
+        iso_code=iso_code,
+        similarity=matched_ef.confidence_score or 0.0,
+        reference_product_name=matched_ef.reference_product_name,
+        preferred_source=matched_ef.source  # Preserve the source selected by AI pipeline
     )
-
-    recommendations = [
-        get_activity_from_dataset(
-            activity["activity_name"],
-            iso_code,
-            activity["similarity"],
-            activity["metadata"]["reference_product_name"]
-        )
-        for activity in activities
-        if closest_match.get("activity_uuid") != activity["uuid"]
-    ]
+    logger.info(f"Phase 8 Complete: Geography matched to '{matched_activity.geography}' with source '{matched_activity.source}'")
+
+    # Phase 1.9: Response Assembly
+    logger.info("Phase 9: Response Assembly - Starting")
+
+    # Create recommendations from remaining candidates (excluding the selected one)
+    recommendations = []
+    for candidate in candidates:
+        if candidate.activity_uuid != matched_ef.activity_uuid:
+            try:
+                recommendation = get_activity_from_dataset(
+                    activity_name=candidate.activity_name,
+                    iso_code=iso_code,
+                    similarity=candidate.similarity_score or 0.0,
+                    reference_product_name=candidate.reference_product_name,
+                    preferred_source=candidate.source  # Preserve the source from each candidate
+                )
+                recommendations.append(recommendation)
+            except Exception as e:
+                logger.warning(f"Failed to create recommendation for {candidate.activity_uuid}: {str(e)}")
+                continue
+
+    logger.info(f"Phase 9 Complete: Assembled response with {len(recommendations)} recommendations")
 
     return ActivityRecommendationsResponse(
         matched_activity=matched_activity,
-        confidence=closest_match["confidence"],
-        explanation=closest_match["match_explanation"],
+        confidence=matched_ef.confidence,
+        explanation=matched_ef.explanation,
         recommendations=recommendations,
     )
 
@@ -350,7 +349,7 @@ def get_cache_stats():
         num_items = len(cache)
     except (TypeError, NotImplementedError):
         num_items = "Not available for this cache type"
-    
+
     return {
         "size (KB)": size_kb,
         "number of items": num_items,
diff --git a/emissions_factor_matching/dataset.py b/emissions_factor_matching/dataset.py
index e3271ec..d38ca18 100644
--- a/emissions_factor_matching/dataset.py
+++ b/emissions_factor_matching/dataset.py
@@ -2,11 +2,14 @@ import os
 import zipfile
 import pandas as pd
 import numpy as np
+import time
+from typing import List, Dict, Any, Optional
 from torch import tensor
 from huggingface_hub import hf_hub_download, login
 import chromadb
-from emissions_factor_matching.model import embedding_function, eol_embedding_function
+from emissions_factor_matching.model import embedding_function, eol_embedding_function, Candidate
 from config import config
+from utils import logger
 login(token=config.hf_token)
 
 chroma_db_zip = hf_hub_download(
@@ -48,3 +51,164 @@ efs_with_geographies_filepath = hf_hub_download(
 )
 
 efs_with_geographies = pd.read_pickle(efs_with_geographies_filepath)
+
+
+def search_candidates(augmented_query: str, filters: Dict[str, Any], n_results: int = 25) -> List[Candidate]:
+    """
+    Phase 1.6: ChromaDB Vector Search Enhancement
+
+    Performs enhanced vector search using the augmented query from Phase 1.4
+    and dynamic filters from Phase 1.5 to find the most relevant emission factor candidates.
+
+    Args:
+        augmented_query: Enhanced query text from Phase 1.4 (e.g., "Heavy-duty diesel freight transport truck >32 tonnes long-haul road transportation logistics")
+        filters: Dynamic filters from Phase 1.5 (e.g., {"$and": [{"activity_type": {"$eq": "ordinary transforming activity"}}, {"isic_4_code": {"$eq": "4923"}}]})
+        n_results: Number of candidates to return (default: 25)
+
+    Returns:
+        List[Candidate]: List of candidate emission factors with metadata and similarity scores
+    """
+    log_prefix = "phase_6_search"
+    logger.info(f"{log_prefix} Executing ChromaDB vector search")
+    logger.info(f"{log_prefix} Query: '{augmented_query}'")
+    logger.info(f"{log_prefix} Filters: {filters}")
+    logger.info(f"{log_prefix} Requesting {n_results} results")
+
+    start_time = time.time()
+
+    try:
+        # Execute ChromaDB query with enhanced parameters
+        results = collection.query(
+            query_texts=[augmented_query],
+            where=filters,
+            n_results=n_results,
+            include=['metadatas', 'documents', 'distances']
+        )
+
+        elapsed_time = (time.time() - start_time) * 1000
+
+        # Extract results
+        ids = results.get('ids', [[]])[0]
+        documents = results.get('documents', [[]])[0]
+        metadatas = results.get('metadatas', [[]])[0]
+        distances = results.get('distances', [[]])[0]
+
+        num_results = len(ids)
+        logger.info(f"{log_prefix} ChromaDB search completed in {elapsed_time:.2f}ms")
+        logger.info(f"{log_prefix} Retrieved {num_results} candidates")
+
+        if num_results == 0:
+            logger.warning(f"{log_prefix} No candidates found for query")
+            return []
+
+        # Convert ChromaDB results to Candidate objects
+        candidates = []
+        for i in range(num_results):
+            try:
+                metadata = metadatas[i] if i < len(metadatas) else {}
+                distance = distances[i] if i < len(distances) else 1.0
+
+                # Extract ISIC code from ChromaDB format "4923:Freight transport by road"
+                isic_classification = metadata.get('ISIC Classification', '')
+                isic_4_code = None
+                if isinstance(isic_classification, str) and ':' in isic_classification:
+                    isic_4_code = isic_classification.split(':')[0].strip()
+                elif isinstance(isic_classification, (int, str)) and str(isic_classification).strip():
+                    isic_4_code = str(isic_classification).strip()
+
+                # Ensure we have a valid ISIC code
+                if not isic_4_code or isic_4_code == '':
+                    isic_4_code = None
+
+                # Create Candidate object with all available metadata
+                candidate = Candidate(
+                    # Core identifiers
+                    activity_uuid=metadata.get('uuid', ids[i]),
+                    chroma_id=ids[i],
+
+                    # Activity information
+                    activity_name=documents[i] if i < len(documents) else "Unknown Activity",
+                    reference_product_name=metadata.get('reference_product_name', ''),
+                    product_information=metadata.get('product_information'),
+
+                    # Source and classification
+                    source=metadata.get('source', 'Unknown'),
+                    isic_4_code=isic_4_code,  # Use extracted numeric code
+                    isic_section=metadata.get('isic_section'),
+                    activity_type=metadata.get('activity_type', 'ordinary transforming activity'),
+
+                    # Geography and units
+                    geography=metadata.get('geography'),
+                    unit=metadata.get('unit'),
+
+                    # Search relevance
+                    distance=distance,
+                    similarity_score=max(0.0, 1.0 - distance),
+
+                    # Additional metadata
+                    metadata=metadata
+                )
+
+                candidates.append(candidate)
+
+                # Log top candidates for debugging
+                if i < 3:  # Log first 3 candidates
+                    logger.info(f"{log_prefix} Candidate {i+1}: '{candidate.activity_name}' (distance: {distance:.4f}, similarity: {candidate.similarity_score:.4f})")
+
+            except Exception as e:
+                logger.error(f"{log_prefix} Error creating candidate {i}: {str(e)}")
+                continue
+
+        # Sort candidates by distance (ascending = most similar first)
+        candidates.sort(key=lambda x: x.distance)
+
+        logger.info(f"{log_prefix} Successfully created {len(candidates)} candidate objects")
+        logger.info(f"{log_prefix} Best match: '{candidates[0].activity_name}' (distance: {candidates[0].distance:.4f})")
+
+        return candidates
+
+    except Exception as e:
+        elapsed_time = (time.time() - start_time) * 1000
+        logger.error(f"{log_prefix} ChromaDB search failed in {elapsed_time:.2f}ms: {str(e)}")
+
+        # Return empty list on error
+        return []
+
+
+def search_candidates_with_fallback(augmented_query: str, filters: Dict[str, Any], n_results: int = 25) -> List[Candidate]:
+    """
+    Enhanced search with fallback strategy for better reliability
+
+    Attempts the primary search with full filters, then falls back to simpler filters
+    if no results are found, ensuring we always return some candidates when possible.
+    """
+    log_prefix = "phase_6_fallback"
+
+    # Try primary search first
+    candidates = search_candidates(augmented_query, filters, n_results)
+
+    if candidates:
+        logger.info(f"{log_prefix} Primary search successful with {len(candidates)} candidates")
+        return candidates
+
+    # Fallback 1: Try with just activity_type filter
+    logger.warning(f"{log_prefix} Primary search returned no results, trying fallback with basic filters")
+
+    basic_filters = {"activity_type": {"$eq": "ordinary transforming activity"}}
+    candidates = search_candidates(augmented_query, basic_filters, n_results)
+
+    if candidates:
+        logger.info(f"{log_prefix} Fallback search successful with {len(candidates)} candidates")
+        return candidates
+
+    # Fallback 2: Try with no filters (last resort)
+    logger.warning(f"{log_prefix} Fallback search returned no results, trying without filters")
+
+    candidates = search_candidates(augmented_query, {}, n_results)
+
+    if candidates:
+        logger.info(f"{log_prefix} No-filter search successful with {len(candidates)} candidates")
+    else:
+        logger.error(f"{log_prefix} All search strategies failed - no candidates found")
+
+    return candidates
diff --git a/emissions_factor_matching/geography.py b/emissions_factor_matching/geography.py
index ff243bf..30b0561 100644
--- a/emissions_factor_matching/geography.py
+++ b/emissions_factor_matching/geography.py
@@ -126,7 +126,7 @@ def get_geography_matches_with_priority(iso_code):
     geography = root.find_geography(iso_code)
     return geography.get_priority_geographies()
 
-def get_geography_activity_match(activity_name: str, iso_code: str, reference_product: str | None=None):
+def get_geography_activity_match(activity_name: str, iso_code: str, reference_product: str | None=None, preferred_source: str | None=None):
     geography_matches = get_geography_matches_with_priority(iso_code)
     regex_pattern = f"^{iso_code}|{iso_code}-|-{iso_code}$"
 
@@ -145,6 +145,15 @@ def get_geography_activity_match(activity_name: str, iso_code: str, reference_pr
             ]
 
         if not matches.empty:
+            # If preferred_source is specified, try to find a match with that source first
+            if preferred_source:
+                preferred_matches = matches[matches['Source'] == preferred_source]
+                if not preferred_matches.empty:
+                    logger.info(f"Found preferred source '{preferred_source}' for activity '{activity_name}'")
+                    return preferred_matches.iloc[0]
+                else:
+                    logger.warning(f"Preferred source '{preferred_source}' not found for activity '{activity_name}', using first available")
+
             return matches.iloc[0]
 
     activity_matchs = efs_with_geographies[
@@ -158,4 +167,13 @@ def get_geography_activity_match(activity_name: str, iso_code: str, reference_pr
     for _, activity in activity_matchs.iterrows():
         logger.warning(activity["Geography"])
 
+    # Even in fallback, try to respect preferred source if specified
+    if preferred_source and not activity_matchs.empty:
+        preferred_fallback = activity_matchs[activity_matchs['Source'] == preferred_source]
+        if not preferred_fallback.empty:
+            logger.info(f"Fallback: Found preferred source '{preferred_source}' for activity '{activity_name}'")
+            return preferred_fallback.iloc[0]
+        else:
+            logger.warning(f"Fallback: Preferred source '{preferred_source}' not found for activity '{activity_name}', using first available")
+
     return activity_matchs.iloc[0]
diff --git a/emissions_factor_matching/model.py b/emissions_factor_matching/model.py
index 0016a45..9971b5b 100644
--- a/emissions_factor_matching/model.py
+++ b/emissions_factor_matching/model.py
@@ -1,4 +1,6 @@
 from chromadb.utils import embedding_functions
+from pydantic import BaseModel
+from typing import Dict, Any, Optional
 
 INSTRUCTION_PROMPT = "Retrieve the closest emissions activity name provided a product description based on product inputs and manufacturing methods, consider suitable proxies:"
 
@@ -13,3 +15,71 @@ eol_embedding_function = embedding_functions.InstructorEmbeddingFunction(
     model_name="hkunlp/instructor-large",
     instruction=EOL_INSTRUCTION_PROMPT,
 )
+
+
+class Candidate(BaseModel):
+    """
+    Data model for Phase 1.6: ChromaDB Vector Search candidates
+
+    Represents a candidate emission factor activity returned from ChromaDB search
+    with all necessary metadata for downstream processing.
+    """
+    # Core identifiers
+    activity_uuid: str
+    chroma_id: str
+
+    # Activity information
+    activity_name: str
+    reference_product_name: str
+    product_information: Optional[str] = None
+
+    # Source and classification
+    source: str
+    isic_4_code: Optional[str] = None
+    isic_section: Optional[str] = None
+    activity_type: str
+
+    # Geography and units
+    geography: Optional[str] = None
+    unit: Optional[str] = None
+
+    # Search relevance
+    distance: float  # ChromaDB distance score (lower = more similar)
+    similarity_score: Optional[float] = None  # Computed as 1 - distance
+
+    # Additional metadata
+    metadata: Dict[str, Any] = {}
+
+    def __post_init__(self):
+        """Compute similarity score from distance"""
+        if self.similarity_score is None and self.distance is not None:
+            self.similarity_score = max(0.0, 1.0 - self.distance)
+
+
+class MatchedEF(BaseModel):
+    """
+    Data model for final matched emission factor after all phases
+
+    Represents the final selected emission factor with confidence and explanation
+    from the AI-assisted matching pipeline.
+    """
+    # Core activity information (inherited from Candidate)
+    activity_uuid: str
+    activity_name: str
+    reference_product_name: str
+    product_information: Optional[str] = None
+    source: str
+    geography: Optional[str] = None
+    unit: Optional[str] = None
+
+    # AI-assisted matching results
+    confidence: str  # "HIGH", "MEDIUM", "LOW"
+    confidence_score: Optional[float] = None  # 0.0 to 1.0
+    explanation: str  # LLM explanation for the match
+
+    # Search and ranking information
+    original_distance: float  # Original ChromaDB distance
+    final_rank: int  # Final rank after LLM re-ranking
+
+    # Processing metadata
+    processing_metadata: Dict[str, Any] = {}
diff --git a/emissions_factor_matching/models.py b/emissions_factor_matching/models.py
new file mode 100644
index 0000000..243471a
--- /dev/null
+++ b/emissions_factor_matching/models.py
@@ -0,0 +1,47 @@
+"""Pydantic models for emissions factor matching structured outputs"""
+from typing import List, Optional, Literal
+from pydantic import BaseModel, Field
+
+
+class InputCategoryResponse(BaseModel):
+    """Response model for input category prediction"""
+    category: str = Field(description="The predicted input category code")
+
+
+class ModifiersResponse(BaseModel):
+    """Response model for modifier spotting"""
+    modifiers: List[str] = Field(
+        default_factory=list,
+        description="List of identified modifiers from the input"
+    )
+
+
+class ISICClassificationResponse(BaseModel):
+    """Response model for ISIC classification"""
+    result: List[str] = Field(
+        default_factory=list,
+        description="List of relevant ISIC codes"
+    )
+
+
+class ClosestMatchResponse(BaseModel):
+    """Response model for closest match finding"""
+    activity_uuid: str = Field(description="UUID of the matched activity")
+    confidence: Literal["low", "medium", "high"] = Field(description="Confidence level of the match")
+    match_explanation: str = Field(description="Explanation for why this match was selected")
+
+
+class TechnologicalRepresentationResponse(BaseModel):
+    """Response model for technological representation assessment"""
+    reason: str = Field(description="Explanation of the technological similarity/difference")
+    similar: bool = Field(description="Whether the technologies are similar")
+
+
+class RerankingResponse(BaseModel):
+    """Response model for LLM reranking of candidates"""
+    selected_candidate_uuid: str = Field(description="UUID of the selected candidate")
+    confidence: Literal["HIGH", "MEDIUM", "LOW"] = Field(description="Confidence level")
+    confidence_score: float = Field(ge=0.0, le=1.0, description="Numerical confidence score")
+    explanation: str = Field(description="Detailed explanation of the selection")
+    ranking_rationale: str = Field(description="Rationale for the ranking")
+    alternative_considerations: str = Field(description="Considerations about alternative candidates")
\ No newline at end of file
diff --git a/emissions_factor_matching/predictions.py b/emissions_factor_matching/predictions.py
index d2aa6ba..8b75df1 100644
--- a/emissions_factor_matching/predictions.py
+++ b/emissions_factor_matching/predictions.py
@@ -1,8 +1,23 @@
 from typing import Dict, Any, List
 import json
-from completion_utils import parse_json
+import time
 from emissions_factor_matching.dataset import efs_with_geographies
-from completions import get_chat_completion, get_async_chat_completion
+from emissions_factor_matching.prompt import get_enhanced_input_category_prompt, get_modifier_spotting_prompt, get_llm_reranking_prompt, get_isic_classification_prompt
+from emissions_factor_matching.model import Candidate, MatchedEF
+from emissions_factor_matching.models import (
+    InputCategoryResponse,
+    ModifiersResponse,
+    ISICClassificationResponse,
+    ClosestMatchResponse,
+    TechnologicalRepresentationResponse,
+    RerankingResponse
+)
+from completions import (
+    get_chat_completion, 
+    get_async_chat_completion,
+    get_structured_completion,
+    get_async_structured_completion
+)
 from clients import openai_client, openai_client_us_east, get_openai_async_client
 from config import config
 from utils import logger
@@ -86,11 +101,50 @@ def get_common_chemical_names(chemical_name: str):
     )
 
 def predict_input_category(input_str: str):
-    prompt = (
-        "Given an input, determine whether the input is a chemical or a more complex product.\n"
-        "Output:\n"
-        "CHEMICAL | PRODUCT"
-    )
+    """Legacy function for backward compatibility - calls enhanced version"""
+    # For backward compatibility, convert single string to request-like structure
+    from pydantic import BaseModel
+
+    class TempRequest(BaseModel):
+        user_query_primary: str
+        user_query_secondary: str | None = None
+        lca_lifecycle_stage: str | None = None
+        iso_code: str | None = None
+
+    request = TempRequest(user_query_primary=input_str)
+    result = predict_enhanced_input_category(request)
+    return result
+
+def predict_enhanced_input_category(request: Any):
+    """
+    Predict the input category for the enhanced emissions factor matching.
+    Now uses structured output with LangChain.
+    """
+    from pydantic import BaseModel
+
+    # Extract fields from request
+    if isinstance(request, BaseModel):
+        user_query_primary = request.user_query_primary
+        user_query_secondary = getattr(request, 'user_query_secondary', None)
+        lca_lifecycle_stage = getattr(request, 'lca_lifecycle_stage', None)
+        iso_code = getattr(request, 'iso_code', None)
+    elif isinstance(request, dict):
+        user_query_primary = request.get('user_query_primary')
+        user_query_secondary = request.get('user_query_secondary')
+        lca_lifecycle_stage = request.get('lca_lifecycle_stage')
+        iso_code = request.get('iso_code')
+    else:
+        # Handle string input for backward compatibility
+        user_query_primary = str(request)
+        user_query_secondary = None
+        lca_lifecycle_stage = None
+        iso_code = None
+
+    if not user_query_primary:
+        logger.error("No primary query provided")
+        return None
+
+    prompt = get_enhanced_input_category_prompt()
 
     messages = [
         {
@@ -99,23 +153,48 @@ def predict_input_category(input_str: str):
         },
         {
             "role": "user",
-            "content": (
-                f"{input_str}"
-            )
+            "content": f"User query: {user_query_primary}"
+                      + (f"\nAdditional context: {user_query_secondary}" if user_query_secondary else "")
+                      + (f"\nLCA lifecycle stage: {lca_lifecycle_stage}" if lca_lifecycle_stage else "")
+                      + (f"\nISO country code: {iso_code}" if iso_code else "")
         }
     ]
 
-    return get_chat_completion(
-        openai_client_us_east,
-        messages,
+    # Use structured completion
+    result = get_structured_completion(
+        messages=messages,
+        response_model=InputCategoryResponse,
         deployment=config.azure_openai_deployment,
-        n_validations=0,
+        temperature=0,
     )
 
-def predict_product_constituents(product: str):
-    prompt = (
-        "Given a product, determine the primary material of the primary components it is comprised of, keep this succinct."
-    )
+    if result:
+        return result.category
+    else:
+        logger.warning("Failed to get structured response for input category")
+        return None
+
+
+def predict_category(input_str: str):
+    """Alias for predict_input_category for backward compatibility"""
+    return predict_input_category(input_str)
+
+
+def spot_modifiers(input_description: str, lca_lifecycle_stage: str = None, iso_code: str = None) -> List[str]:
+    """
+    Extracts modifiers from the input description.
+    Now uses structured output with LangChain.
+    """
+    # Build the base query
+    query = f"Input: {input_description}"
+    
+    # Add optional parameters if provided
+    if lca_lifecycle_stage:
+        query += f"\nLCA Lifecycle Stage: {lca_lifecycle_stage}"
+    if iso_code:
+        query += f"\nISO Country Code: {iso_code}"
+
+    prompt = get_modifier_spotting_prompt()
 
     messages = [
         {
@@ -124,25 +203,36 @@ def predict_product_constituents(product: str):
         },
         {
             "role": "user",
-            "content": (
-                f"{product}"
-            )
+            "content": query
         }
     ]
 
-    return get_chat_completion(
-        openai_client_us_east,
-        messages,
+    # Use structured completion
+    result = get_structured_completion(
+        messages=messages,
+        response_model=ModifiersResponse,
         deployment=config.azure_openai_deployment,
-        n_validations=0,
+        temperature=0,
     )
 
-def get_product_description(product_name: str):
-    prompt = (
-        "Given a product name, give a succinct description of "
-        "what comprises it and is general application and uses. "
-        "NOTE: keep this fairly succinct."
-    )
+    if result:
+        return result.modifiers
+    else:
+        logger.warning("Failed to get structured response for modifiers")
+        return []
+
+
+def map_isic_classification(user_query: str, iso_code: str = None) -> List[str]:
+    """
+    Maps user query to ISIC codes.
+    Now uses structured output with LangChain.
+    """
+    # Build the query
+    query = f"User query: {user_query}"
+    if iso_code:
+        query += f"\nISO Country Code: {iso_code}"
+
+    prompt = get_isic_classification_prompt()
 
     messages = [
         {
@@ -151,387 +241,349 @@ def get_product_description(product_name: str):
         },
         {
             "role": "user",
-            "content": (
-                f"{product_name}"
-            )
+            "content": query
         }
     ]
 
-    return get_chat_completion(
-        openai_client_us_east,
-        messages,
+    # Use structured completion
+    result = get_structured_completion(
+        messages=messages,
+        response_model=ISICClassificationResponse,
         deployment=config.azure_openai_deployment,
-        n_validations=0,
+        temperature=0,
     )
 
-def get_product_activity_isic_section(product_name: str) -> str | None:
-    """Predict the ISIC Section for a given product."""
-    logger.info(f"Predicting Product ISIC Section for {product_name}")
-    isic_letter_to_class = {
-        "C": "C - Manufacturing",
-        "E": "E - Water supply; sewerage, waste management and remediation activities",
-        "A": "A - Agriculture, forestry and fishing",
-        "D": "D - Electricity, gas, steam and air conditioning supply",
-        "F": "F - Construction",
-        "B": "B - Mining and quarrying",
-        "G": "G - Wholesale and retail trade; repair of motor vehicles and motorcycles",
-        "H": "H - Transportation and storage",
-    }
+    if result:
+        return result.result
+    else:
+        logger.warning("Failed to get structured response for ISIC classification")
+        return []
+
+
+def prepare_results(
+    results_dict: Dict[str, List[Candidate]],
+    modifier_used: bool,
+    isic_codes: List[str],
+) -> List[MatchedEF]:
+    sorted_results = {}
+    for phase_name, candidates in results_dict.items():
+        sorted_candidates = sorted(candidates, key=lambda c: c.similarity_score, reverse=True)
+        sorted_results[phase_name] = sorted_candidates
+
+    # Flatten results from all phases
+    all_results = []
+    for phase_name, candidates in sorted_results.items():
+        for candidate in candidates:
+            all_results.append(
+                MatchedEF(
+                    activity_uuid=candidate.activity_uuid,
+                    similarity_score=candidate.similarity_score,
+                    ef_activity_name=candidate.ef_activity_name,
+                    ef_reference_unit=candidate.ef_reference_unit,
+                    phase=phase_name,
+                    modifier_used=modifier_used,
+                    isic_used=bool(isic_codes),
+                )
+            )
+
+    # Sort all results by similarity score
+    all_results = sorted(all_results, key=lambda r: r.similarity_score, reverse=True)
+
+    # Keep top 15 overall
+    top_results = all_results[:15]
+
+    # Remove duplicates based on activity_uuid while preserving order
+    seen_uuids = set()
+    unique_results = []
+    for result in top_results:
+        if result.activity_uuid not in seen_uuids:
+            seen_uuids.add(result.activity_uuid)
+            unique_results.append(result)
+
+    return unique_results
+
 
+def hybrid_predictions(
+    user_query_primary: str, 
+    user_query_secondary: str = None, 
+    lca_lifecycle_stage: str = None, 
+    iso_code: str = None
+) -> List[MatchedEF]:
+    """
+    Main function for the multi-phase approach.
+    Returns a list of MatchedEF objects with phase information.
+    """
+    # Initialize results dictionary
+    results_dict = {}
+    
+    # Phase 1: Base Search
+    from emissions_factor_matching.dataset import search_emissions_factors_vectordb
+    
+    # Always do base search first
+    base_results = search_emissions_factors_vectordb(user_query_primary, iso_code=iso_code, filter_metadata={})
+    results_dict["phase_1_base"] = base_results
+    
+    # Check if we need Phase 2
+    has_good_matches = any(candidate.similarity_score >= 0.8 for candidate in base_results)
+    
+    if has_good_matches:
+        # Good matches found, prepare and return results
+        return prepare_results(results_dict, modifier_used=False, isic_codes=[])
+    
+    # Phase 2: Enhanced Search with modifiers and ISIC codes
+    # Extract modifiers
+    modifiers = spot_modifiers(user_query_primary, lca_lifecycle_stage, iso_code)
+    
+    # Get ISIC codes
+    isic_codes = map_isic_classification(user_query_primary, iso_code)
+    
+    # Search with modifiers if found
+    if modifiers:
+        modifier_results = []
+        for modifier in modifiers:
+            results = search_emissions_factors_vectordb(modifier, iso_code=iso_code, filter_metadata={})
+            modifier_results.extend(results)
+        
+        # Remove duplicates based on activity_uuid
+        seen_uuids = set()
+        unique_modifier_results = []
+        for result in modifier_results:
+            if result.activity_uuid not in seen_uuids:
+                seen_uuids.add(result.activity_uuid)
+                unique_modifier_results.append(result)
+        
+        results_dict["phase_2_modifiers"] = unique_modifier_results
+    
+    # Search with ISIC codes if found
+    if isic_codes:
+        isic_results = []
+        for isic_code in isic_codes:
+            # Create filter for ISIC code
+            filter_metadata = {"isic_code": isic_code}
+            results = search_emissions_factors_vectordb(
+                user_query_primary, 
+                iso_code=iso_code, 
+                filter_metadata=filter_metadata
+            )
+            isic_results.extend(results)
+        
+        # Remove duplicates
+        seen_uuids = set()
+        unique_isic_results = []
+        for result in isic_results:
+            if result.activity_uuid not in seen_uuids:
+                seen_uuids.add(result.activity_uuid)
+                unique_isic_results.append(result)
+        
+        results_dict["phase_2_isic"] = unique_isic_results
+    
+    # Prepare final results
+    return prepare_results(results_dict, modifier_used=bool(modifiers), isic_codes=isic_codes)
+
+
+def search_emissions_factors(user_input: str) -> List[MatchedEF]:
+    """Legacy function for backward compatibility"""
+    return hybrid_predictions(user_query_primary=user_input)
+
+
+def match_emission_factor(
+    user_query: str,
+    activity_name: str, 
+    geography: str = None,
+    lca_stage: str = None
+) -> dict:
+    """Legacy function for backward compatibility"""
+    # This is a simplified version - actual implementation would need more logic
+    results = hybrid_predictions(
+        user_query_primary=user_query,
+        lca_lifecycle_stage=lca_stage,
+        iso_code=geography
+    )
+    
+    if results:
+        top_match = results[0]
+        return {
+            "activity_uuid": top_match.activity_uuid,
+            "confidence": "high" if top_match.similarity_score > 0.8 else "medium",
+            "match_explanation": f"Matched with similarity score {top_match.similarity_score}"
+        }
+    return None
+
+
+def get_closest_match(
+    prompt: str,
+    data: List[tuple]
+) -> dict | None:
+    """
+    Gets the closest match from a list of candidates.
+    Now uses structured output with LangChain.
+    """
     messages = [
-       {
+        {
             "role": "system",
-            "content": (
-                "Respond with the ISIC Section for a product provided below.\n"
-                "Respond only with the first letter which denotes a ISIC Section"
-                "Options:\n"
-                "A - Agriculture & Natural Resources (Agriculture, forestry, and fishing)\n"
-                "B - Extractive Industries (Mining and quarrying)\n"
-                "C - Manufacturing & Industrial Production (Manufacturing)\n"
-                "D - Energy & Utilities (Electricity, gas, steam, and air conditioning supply)\n"
-                "E - Water & Environmental Management (Water supply; sewerage, waste management, and remediation activities)\n"
-                "F - Construction & Infrastructure Development (Construction)\n"
-                "G - Trade & Vehicle Services (Wholesale and retail trade; repair of motor vehicles and motorcycles)\n"
-                "H - Transportation and Storage\n\n"
-                "Example:\n"
-                "Product \"Water\": E\n"
-                "Product \"Sodium Chloride\": B\n"
-                "Product \"Calcium Chloride\": C\n"
-            ),
+            "content": prompt
         },
         {
             "role": "user",
-            "content": (
-                f"Product \"{product_name}\": "
-            )
+            "content": json.dumps(data, ensure_ascii=False, indent=2)
         }
     ]
 
-    completion = get_chat_completion(openai_client, messages, deployment=config.azure_openai_deployment, n_validations=1)
-    if not completion:
+    # Use structured completion
+    result = get_structured_completion(
+        messages=messages,
+        response_model=ClosestMatchResponse,
+        deployment=config.azure_openai_deployment,
+        temperature=0,
+    )
+
+    if result:
+        return {
+            "activity_uuid": result.activity_uuid,
+            "confidence": result.confidence,
+            "match_explanation": result.match_explanation
+        }
+    else:
+        logger.warning("Failed to get structured response for closest match")
         return None
 
-    return isic_letter_to_class.get(completion.upper(), None)
 
-async def get_closest_match(product_name: str, activities: Dict) -> Dict | None:
-    """Return the UUID of the closest activity match."""
+async def get_technological_representation(process_name: str, activity_name: str):
+    """
+    Predict the Technological representativeness.
+    Now uses structured output with LangChain.
+    """
     prompt = (
-        "Given a list of emissions activities by the user determine which one most "
-        "closely is associated to the given chemical and/or product (always pick one). "
-        "The returned output should be a JSON Object with the following schema:```json\n"
-        "{\n"
-        "   \"activity_uuid\": {ACTIVITY_UUID}, NOTE: this should never be null, always pick one (the closest match),\n"
-        "   \"confidence\": {CONFIDENCE - (\"low\",\"medium\",\"high\") - NOTE: 'high' should be reserved for matches that are very close.},\n"
-        "   \"match_explanation\": {MATCH_EXPLANATION}"
-        "}\n"
-        "```"
-        "Example:\n"
-        "Chemical and/or product: {PRODUCT_NAME}\n"
-        "Emissions activities: {JSON_STRING}\n"
-        "Output:```json\n"
-        "{\n"
-        "   \"activity_uuid\": {ACTIVITY_UUID},\n"
-        "   \"confidence\": {CONFIDENCE - (\"low\",\"medium\",\"high\")},\n"
-        "   \"match_explanation\": {MATCH_EXPLANATION - explain why the match is the closest match for an emission factor match and/or proxy}"
-        "}\n"
-        "```"
+        "You will be given a user's manufacturing process or technology and a target ecoinvent activity name.\n"
+        "Your task is to determine if they represent similar technologies or processes.\n\n"
+        
+        "Consider these aspects:\n"
+        "- Manufacturing methods and processes\n"
+        "- Technology level (modern vs traditional)\n"
+        "- Scale of operation\n"
+        "- Core transformation process\n\n"
+        
+        "Respond with:\n"
+        "- reason: A brief explanation of why they are similar or different\n"
+        "- similar: true if they represent similar technologies, false otherwise\n\n"
+        
+        "Examples:\n"
+        "User: 'CNC machining of aluminum parts'\n"
+        "Activity: 'metal working, average for aluminium product manufacturing'\n"
+        "Response: {\"reason\": \"Both involve aluminum processing, though CNC is more specific than average metal working\", \"similar\": true}\n\n"
+        
+        "User: 'Hand weaving of cotton fabric'\n"  
+        "Activity: 'textile weaving, industrial scale'\n"
+        "Response: {\"reason\": \"Hand weaving is traditional/artisanal while industrial weaving uses modern machinery at scale\", \"similar\": false}\n"
     )
 
     messages = [
         {
             "role": "system",
-            "content": prompt,
+            "content": prompt
         },
         {
             "role": "user",
-            "content": (
-                f"Chemical and/or product: {product_name}\n"
-                f"Emissions activities: {json.dumps(activities)}\n"
-                "Output: "
-            )
+            "content": f"User process: '{process_name}'\nActivity name: '{activity_name}'"
         }
     ]
 
+    # Use async structured completion
     async with get_openai_async_client() as client:
-        completion = await get_async_chat_completion(
-            client,
-            messages,
+        result = await get_async_structured_completion(
+            messages=messages,
+            response_model=TechnologicalRepresentationResponse,
             deployment=config.azure_openai_deployment,
-            n_validations=0,
-            strict=True,
+            temperature=0,
         )
 
-    try:
-        completion_dict = parse_json(completion, model=dict)
-
-        assert "activity_uuid" in completion_dict
-        assert "confidence" in completion_dict
-        assert "match_explanation" in completion_dict
-
-        return completion_dict
-    except Exception:
+    if result:
+        return {
+            "reason": result.reason,
+            "similar": result.similar
+        }
+    else:
+        logger.warning("Failed to get structured response for technological representation")
         return None
 
-async def get_technological_representation(process_name: str, activity_name: str):
-    """
-    Predict the Technological representativeness which is the fitness of
-    the following technology categories:
-    - process design
-    - operating conditions
-    - material quality/type
-    - process scale
-
-    This technological representation is based on the BIFMA Seating PCR
-    """
-    async with get_openai_async_client() as client:
-        async def compare_category(category: str, process_1: str, process_2: str):
-            prompt = (
-                f"Given a process name describe the {category} (focus on the means of production)"
-            )
 
-            messages_1 = [
-                {
-                    "role": "system",
-                    "content": prompt,
-                },
-                {
-                    "role": "user",
-                    "content": (
-                        f"Process Name: {process_1}\n"
-                        "Output: "
-                    )
-                }
-            ]
-
-            messages_2 = [
-                {
-                    "role": "system",
-                    "content": prompt,
-                },
-                {
-                    "role": "user",
-                    "content": (
-                        f"Process Name: {process_2}\n"
-                        "Output: "
-                    )
-                }
-            ]
-
-            completion_1 = await get_async_chat_completion(
-                client,
-                messages_1,
-                deployment=config.azure_openai_deployment,
-                n_validations=0,
-            )
-            completion_2 = await get_async_chat_completion(
-                client,
-                messages_2,
-                deployment=config.azure_openai_deployment,
-                n_validations=0,
-            )
+def ask_anthropic(prompt: str):
+    # Placeholder function - actual implementation would use Anthropic API
+    return "Not implemented"
 
-            comparison_prompt = (
-                f"Given two {category} for an emissions factor determine whether they are similar, OUTPUT:```json\n"
-                "{\n"
-                "   \"reason\": {REASON} (str) Explain the reason why the " + f"{category} are similar or not,\n"
-                "   \"similar\": {SIMILAR} (bool) Are the " + f"{category} similar?,\n"
-                "}```\n"
-            )
 
-            comparison_messages = [
-                {
-                    "role": "system",
-                    "content": comparison_prompt,
-                },
-                {
-                    "role": "user",
-                    "content": (
-                        f"{category} 1: {completion_1}\n\n"
-                        f"{category} 2: {completion_2}\n\n"
-                        "OUTPUT: "
-                    )
-                }
-            ]
-
-            comparison_completion = await get_async_chat_completion(
-                client,
-                comparison_messages,
-                deployment=config.azure_openai_deployment,
-                n_validations=0,
-                strict=True
+def eol_activity_lookup(
+    user_query: str, 
+    iso_code: str = None,
+    limit: int = 10
+) -> List[MatchedEF]:
+    """
+    Searches for End-of-Life (EoL) activities based on user query.
+    Uses the EOL-specific vector database.
+    """
+    from emissions_factor_matching.dataset import search_emissions_factors_vectordb_eol
+    
+    # Search in EOL database
+    results = search_emissions_factors_vectordb_eol(
+        user_query, 
+        iso_code=iso_code,
+        filter_metadata={},
+        limit=limit
+    )
+    
+    # Convert to MatchedEF format
+    matched_results = []
+    for candidate in results:
+        matched_results.append(
+            MatchedEF(
+                activity_uuid=candidate.activity_uuid,
+                similarity_score=candidate.similarity_score,
+                ef_activity_name=candidate.ef_activity_name,
+                ef_reference_unit=candidate.ef_reference_unit,
+                phase="eol_search",
+                modifier_used=False,
+                isic_used=False,
             )
-
-            try:
-                completion_dict = parse_json(comparison_completion, model=dict)
-
-                assert "reason" in completion_dict
-                assert "similar" in completion_dict
-
-                return completion_dict
-            except Exception:
-                return None
-
-        process_design_comparison = await compare_category(
-            "Production Process Design",
-            process_name,
-            activity_name,
-        )
-        operating_condition_comparison = await compare_category(
-            "Production Operating Conditions",
-            process_name,
-            activity_name,
-        )
-        material_quality_comparison = await compare_category(
-            "Material Quality/Type",
-            process_name,
-            activity_name,
         )
-        process_scale_comparison = await compare_category(
-            "Production Process Scale",
-            process_name,
-            activity_name,
-        )
-
-    matched_categories = 0
-    if process_design_comparison["similar"]:
-        matched_categories += 1
-    if operating_condition_comparison["similar"]:
-        matched_categories += 1
-    if material_quality_comparison["similar"]:
-        matched_categories += 1
-    if process_scale_comparison["similar"]:
-        matched_categories += 1
-
-    return [
-        "None of the technology categories are similar",
-        "One of the technology categories is similar",
-        "Two of the technology categories are similar",
-        "Three of the technology categories are similar",
-        "All technology categories are similar",
-    ][matched_categories]
-
-
-def get_chemical_tree(chemical_name: str):
-    prompt = (
-        "Using the following tree output a valid tree for a given chemical\n"
-        "Class: Broadest category based on similar properties or uses.\n\n"
-        "Subclass: More specific grouping within a class.\n\n"
-        "Family: Chemicals with a common structural feature.\n\n"
-        "Subfamily: Further division within a family.\n\n"
-        "A valid output will be a comma seperated list with no other output which will only contain the following:\n"
-        "{Class},{Subclass},{Family},{Subfamily}"
-    )
+    
+    return matched_results
 
-    messages = [
-        {
-            "role": "system",
-            "content": prompt,
-        },
-        {
-            "role": "user",
-            "content": chemical_name,
-        },
-    ]
 
-    return get_chat_completion(
-        openai_client_us_east,
-        messages,
-        deployment=config.azure_openai_deployment,
-    )
-
-def get_product_activity_isic_division(product_name: str, isic_section: str) -> str | None:
+def re_rank_candidates(
+    user_query: str,
+    candidates_list: List[Dict[str, Any]],
+    lca_lifecycle_stage: str = None,
+    iso_code: str = None
+) -> RerankingResponse | None:
     """
-    Predict the ISIC Division (first digit) for a given product
-    based on its name and the ISIC section provided.
+    Use LLM to re-rank and select the best candidate from a list.
+    Now uses structured output with LangChain.
     """
-    logger.info(f"Predicting Product ISIC Section for {product_name}")
-
-    classifications = '\n'.join(efs_with_geographies[
-        efs_with_geographies["ISIC Section"] == isic_section
-    ]["ISIC Classification"].unique().tolist())
-
+    # Get the reranking prompt
+    prompt = get_llm_reranking_prompt(
+        user_query=user_query,
+        candidates=candidates_list,
+        lca_stage=lca_lifecycle_stage,
+        iso_code=iso_code
+    )
+    
     messages = [
         {
             "role": "system",
-            "content": (
-                "Respond with the ISIC Classification for a product given the ISIC Section provided below.\n"
-                "Possible ISIC Classifications:\n"
-                f"{classifications}\n"
-                "Respond only with the number which denotes a ISIC Classification.\n"
-                "Example:\n"
-                "Product: Water\n"
-                "ISIC Section: E\n"
-                "ISIC Classification: 3600\n\n"
-                "Product: Sodium Chloride"
-                "ISIC Section: B\n"
-                "ISIC Classification: 0893\n"
-            ),
+            "content": "You are an expert in environmental life cycle assessment and emission factor matching."
         },
         {
             "role": "user",
-            "content": (
-                f"Product: {product_name}\n"
-                f"ISIC Section: {isic_section}\n"
-                f"ISIC Classification: "
-            )
+            "content": prompt
         }
     ]
-
-    completion = get_chat_completion(openai_client, messages, deployment=config.azure_openai_deployment, n_validations=0)
-    completion = ''.join(char for char in completion if char.isdigit())
-
-    logger.info(f"Division Prediction: {completion[:1]}")
-
-    validation_completion = get_chat_completion(openai_client, messages, deployment=config.azure_openai_deployment, n_validations=0)
-    validation_completion = ''.join(char for char in validation_completion if char.isdigit())
-
-    logger.info(f"Validation Division Prediction: {validation_completion[:1]}")
-
-    return (
-        completion[0]
-        if completion[0] == validation_completion[0]
-        else None
-    )
-
-def get_chemical_features(chemical_name: str):
-    chat_completion = openai_client.chat.completions.create(
-        messages=[
-            {
-                "role": "system",
-                "content": (
-                    "Respond with a succinct description of the features of the chemical or ingredient provided by the user "
-                    "such as the class of chemical and common uses and applications in an industrial setting."
-                ),
-            },
-            {
-                "role": "user",
-                "content": f"Chemical: '{chemical_name}'"
-            },
-        ],
-        model=config.azure_openai_deployment  # Using 'model' as it's the parameter name expected by the OpenAI API
-    )
-
-    return chat_completion.choices[0].message.content
-
-def get_enhanced_activity_description(activity_name: str, activity_description: str):
-    chat_completion = openai_client.chat.completions.create(
-        messages=[
-            {
-                "role": "system",
-                "content": (
-                    "Given an activity name and description provided by the user "
-                    "provide a succinct description of the activity describing it's main application "
-                    "and primary chemical being used. "
-                    "Include chemical synonyms if any exist. If not a chemical, describe the "
-                    "features of the activity."
-                ),
-            },
-            {
-                "role": "user",
-                "content": f"{activity_name}: {activity_description}"
-            },
-        ],
-        model=config.azure_openai_deployment,  # Using 'model' as it's the parameter name expected by the OpenAI API
+    
+    # Use structured completion
+    result = get_structured_completion(
+        messages=messages,
+        response_model=RerankingResponse,
+        deployment=config.azure_openai_deployment,
+        temperature=0,
     )
-
-    return chat_completion.choices[0].message.content
+    
+    return result
\ No newline at end of file
diff --git a/emissions_factor_matching/prompt.py b/emissions_factor_matching/prompt.py
index 980aa41..776a481 100644
--- a/emissions_factor_matching/prompt.py
+++ b/emissions_factor_matching/prompt.py
@@ -16,3 +16,321 @@ def construct_chemical_prompt(
 
     logger.info(prompt)
     return prompt
+
+def get_enhanced_input_category_prompt() -> str:
+    """
+    Prompt template for Phase 1.1: Enhanced Input Category Prediction
+    Returns a detailed categorical classification instead of simple CHEMICAL/PRODUCT
+    """
+    return """You are an expert in environmental impact assessment and emission factor classification.
+Your task is to analyze user queries and classify them into specific, detailed categories that will help in finding the most appropriate emission factors.
+
+Given the user's input context, classify the query into ONE of the following detailed categories:
+
+**CHEMICAL CATEGORIES:**
+- CHEMICAL_ORGANIC_SOLVENT (organic solvents, alcohols, ketones, etc.)
+- CHEMICAL_INORGANIC_ACID (acids, bases, salts)
+- CHEMICAL_POLYMER_PLASTIC (polymers, plastics, resins)
+- CHEMICAL_METAL_COMPOUND (metal oxides, metal salts, alloys)
+- CHEMICAL_FUEL_ENERGY (fuels, energy carriers, combustibles)
+- CHEMICAL_PHARMACEUTICAL (drugs, active ingredients, medical compounds)
+- CHEMICAL_AGRICULTURAL (fertilizers, pesticides, herbicides)
+- CHEMICAL_OTHER (other chemical substances)
+
+**PRODUCT CATEGORIES:**
+- PRODUCT_ELECTRONICS_DEVICE (smartphones, computers, appliances)
+- PRODUCT_AUTOMOTIVE_VEHICLE (cars, trucks, motorcycles)
+- PRODUCT_TEXTILE_CLOTHING (fabrics, garments, footwear)
+- PRODUCT_CONSTRUCTION_MATERIAL (cement, steel, wood, insulation)
+- PRODUCT_PACKAGING_CONTAINER (bottles, boxes, wrapping materials)
+- PRODUCT_FOOD_BEVERAGE (food items, drinks, agricultural products)
+- PRODUCT_FURNITURE_FIXTURE (furniture, fixtures, home goods)
+- PRODUCT_MEDICAL_EQUIPMENT (medical devices, instruments, supplies)
+- PRODUCT_OTHER (other manufactured products)
+
+**SERVICE CATEGORIES:**
+- SERVICE_TRANSPORT_ROAD_FREIGHT (truck transportation, delivery services)
+- SERVICE_TRANSPORT_ROAD_PASSENGER (bus, taxi, ride-sharing)
+- SERVICE_TRANSPORT_AIR (aviation, air freight, passenger flights)
+- SERVICE_TRANSPORT_MARITIME (shipping, marine transport)
+- SERVICE_TRANSPORT_RAIL (train transport, rail freight)
+- SERVICE_ENERGY_ELECTRICITY (electricity generation, grid services)
+- SERVICE_ENERGY_HEATING (heating services, thermal energy)
+- SERVICE_WASTE_TREATMENT (waste processing, recycling, disposal)
+- SERVICE_CONSTRUCTION (building services, infrastructure)
+- SERVICE_OTHER (other service activities)
+
+**ANALYSIS INSTRUCTIONS:**
+1. Consider the primary query and any secondary context provided
+2. Look for key indicators like materials, processes, end-use applications
+3. Consider the lifecycle stage if provided (production, use, disposal)
+4. Consider geographical context if relevant to the classification
+5. Choose the MOST SPECIFIC category that fits the query
+
+**OUTPUT FORMAT:**
+Return a JSON object with the category code. Use this exact format:
+{"category": "SERVICE_TRANSPORT_ROAD_FREIGHT"}
+
+If you cannot determine a specific category, use "PRODUCT_OTHER" as the default.
+Do not include any additional text or explanation outside the JSON object."""
+
+def get_modifier_spotting_prompt() -> str:
+    """
+    Prompt template for Phase 1.2: Modifier Spotting
+    Extracts key modifiers and attributes from user queries
+    """
+    return """You are an expert in environmental impact assessment and emission factor analysis.
+Your task is to extract key modifiers and attributes from user queries that are relevant for finding the most appropriate emission factors.
+
+Analyze the provided query context and extract specific modifiers that would affect emission factor selection. Focus on:
+
+**TRANSPORT MODIFIERS:**
+- Fuel type: diesel, gasoline, electric, hybrid, natural gas, biodiesel
+- Vehicle size/weight: <3.5t, 3.5-7.5t, 7.5-16t, 16-32t, >32t, light duty, heavy duty
+- Driving conditions: urban, highway, mixed, city, rural, long-haul
+- Vehicle type: truck, van, car, bus, motorcycle, freight, passenger
+- Load factor: empty, partial load, full load, return trip
+
+**CHEMICAL/MATERIAL MODIFIERS:**
+- Purity/grade: high purity, industrial grade, pharmaceutical grade, technical grade
+- Physical state: liquid, solid, gas, powder, crystalline
+- Concentration: dilute, concentrated, pure, mixed
+- Source/origin: synthetic, natural, bio-based, recycled
+- Processing: refined, crude, processed, raw
+
+**PRODUCT MODIFIERS:**
+- Material composition: steel, aluminum, plastic, wood, composite
+- Quality/grade: high strength, lightweight, premium, standard
+- Manufacturing process: cast, forged, machined, molded, extruded
+- Recycled content: recycled, virgin, post-consumer, post-industrial
+- Size/scale: small, medium, large, industrial, commercial, residential
+
+**ENERGY MODIFIERS:**
+- Source type: renewable, fossil, nuclear, solar, wind, hydro
+- Grid mix: national grid, regional grid, specific utility
+- Efficiency: high efficiency, standard, low efficiency
+- Technology: combined cycle, simple cycle, cogeneration
+
+**GEOGRAPHIC/TEMPORAL MODIFIERS:**
+- Regional specifics: European, Asian, North American
+- Climate conditions: cold climate, hot climate, temperate
+- Seasonal: summer, winter, peak, off-peak
+
+**ANALYSIS INSTRUCTIONS:**
+1. Extract modifiers from both primary and secondary queries
+2. Consider the input category context to focus on relevant modifier types
+3. Look for quantitative specifications (weights, percentages, sizes)
+4. Identify qualitative attributes (efficiency, purity, grade)
+5. Extract implicit modifiers from context clues
+6. Prioritize modifiers that would significantly impact emission factors
+
+**OUTPUT FORMAT:**
+Return a JSON array of modifier strings, each representing a distinct attribute or specification.
+Example: ["diesel", ">32t", "long-haul", "full load"]
+If no relevant modifiers are found, return an empty array: []
+
+**IMPORTANT:**
+- Only extract modifiers that are explicitly mentioned or clearly implied
+- Use standardized terminology when possible
+- Keep modifiers concise but descriptive
+- Do not invent modifiers that aren't supported by the input"""
+
+def get_query_augmentation_prompt() -> str:
+    """
+    Prompt template for Phase 1.4: Query Text Augmentation
+    Transforms enhanced category + modifiers + ISIC codes into optimized search queries
+    """
+    return """You are an expert in environmental impact assessment and emission factor databases.
+Your task is to transform structured query information into optimized search text that will effectively match against emission factor activity descriptions in a vector database.
+
+Given the enhanced category classification, extracted modifiers, and ISIC codes from previous analysis phases, create a descriptive search query that:
+
+**OPTIMIZATION GOALS:**
+1. **Semantic Richness**: Use terminology that matches how emission factors are described in databases
+2. **Technical Precision**: Include specific technical terms, processes, and industry language
+3. **Context Expansion**: Add relevant synonyms, related processes, and industry context
+4. **Vector Search Optimization**: Structure text to maximize similarity with emission factor descriptions
+
+**AUGMENTATION STRATEGIES:**
+
+**For CHEMICAL Categories:**
+- Include chemical class, industrial applications, production methods
+- Add common synonyms, CAS-related terminology, purity grades
+- Mention typical manufacturing processes, feedstocks, end-uses
+- Include relevant industry sectors (petrochemical, pharmaceutical, etc.)
+
+**For PRODUCT Categories:**
+- Describe materials, manufacturing processes, typical applications
+- Include component materials, assembly methods, quality grades
+- Add lifecycle context (production, use phase, disposal)
+- Mention relevant industry standards, certifications
+
+**For SERVICE Categories:**
+- Detail operational characteristics, equipment types, energy sources
+- Include scale indicators, efficiency levels, technology types
+- Add geographic/regulatory context, service delivery methods
+- Mention infrastructure requirements, operational parameters
+
+**ISIC CODE INTEGRATION:**
+- Incorporate industry-specific terminology from the ISIC classification
+- Add sector-specific processes, equipment, and operational context
+- Include regulatory and technical standards relevant to the ISIC sector
+
+**MODIFIER ENHANCEMENT:**
+- Expand abbreviated modifiers (">32t" → "heavy duty freight vehicles over 32 tonnes")
+- Add technical context for specifications (diesel → "diesel fuel combustion in compression ignition engines")
+- Include related operational parameters and efficiency considerations
+
+**OUTPUT REQUIREMENTS:**
+- Generate 2-4 sentences of descriptive text
+- Use natural language that flows well for embedding models
+- Include 8-15 key technical terms and concepts
+- Balance specificity with searchability
+- Avoid overly generic terms, focus on distinctive characteristics
+
+**EXAMPLES:**
+
+Input: Category="SERVICE_TRANSPORT_ROAD_FREIGHT", Modifiers=["diesel", ">32t", "long-haul"], ISIC=["4923"]
+Output: "Heavy duty diesel freight transportation services using vehicles exceeding 32 tonnes gross weight for long-distance cargo delivery. Road freight transport operations involving compression ignition diesel engines in commercial trucking fleets for inter-regional goods movement and logistics services."
+
+Input: Category="CHEMICAL_ORGANIC_SOLVENT", Modifiers=["high purity", "industrial grade"], ISIC=["2011"]
+Output: "High purity organic solvent production for industrial applications in chemical manufacturing processes. Industrial grade solvent synthesis involving distillation, purification, and quality control in petrochemical facilities for use in coatings, adhesives, and chemical processing operations."
+
+**INSTRUCTIONS:**
+1. Analyze the provided category, modifiers, and ISIC codes
+2. Generate descriptive text optimized for vector similarity search
+3. Include relevant technical terminology and industry context
+4. Ensure the output is natural language suitable for embedding models
+5. Focus on terms that would appear in emission factor activity descriptions"""
+
+
+def get_isic_classification_prompt() -> str:
+    """
+    Prompt template for Phase 1.3: LLM-Based ISIC Classification
+    Maps enhanced categories and modifiers to actual ISIC codes from the database
+    """
+    return """You are an expert in industrial classification systems and environmental impact assessment.
+Your task is to analyze user queries and map them to appropriate ISIC (International Standard Industrial Classification) codes based on the enhanced category, modifiers, and available codes in our emission factor database.
+
+## AVAILABLE ISIC CODES:
+You will be provided with a list of ISIC codes that exist in our database. You MUST only return codes from this list.
+
+## ANALYSIS FRAMEWORK:
+
+**TRANSPORT SERVICES (H - Transportation and storage):**
+- 4911: Passenger rail transport, interurban
+- 4912: Freight rail transport
+- 4921: Urban and suburban passenger land transport
+- 4922: Other passenger land transport
+- 4923: Freight transport by road
+- 5012: Sea and coastal freight water transport
+- 5022: Inland freight water transport
+- 5110: Passenger air transport
+- 5120: Freight air transport
+
+**ENERGY SERVICES (D - Electricity, gas, steam and air conditioning supply):**
+- 3510: Electric power generation, transmission and distribution
+- 3520: Manufacture of gas; distribution of gaseous fuels through mains
+- 3530: Steam and air conditioning supply
+
+**CHEMICAL PRODUCTION (C - Manufacturing):**
+- 2011: Manufacture of basic chemicals
+- 2012: Manufacture of fertilizers and nitrogen compounds
+- 2013: Manufacture of plastics and synthetic rubber in primary forms
+- 1920: Manufacture of refined petroleum products
+
+**CONSTRUCTION MATERIALS:**
+- 2394: Manufacture of cement
+- 2395: Manufacture of articles of concrete, cement and plaster
+- 2420: Manufacture of basic precious and other non-ferrous metals
+
+**WASTE SERVICES (E - Water supply; sewerage, waste management):**
+- 3821: Treatment and disposal of non-hazardous waste
+- 3822: Treatment and disposal of hazardous waste
+- 3830: Materials recovery
+
+## MAPPING INSTRUCTIONS:
+
+1. **Primary Mapping**: Match the enhanced category to the most appropriate ISIC section and division
+2. **Modifier Refinement**: Use modifiers to select the most specific ISIC code within that division
+3. **Validation**: Ensure all returned codes exist in the provided available codes list
+4. **Fallback**: If no perfect match exists, select the closest related code from available options
+
+## OUTPUT FORMAT:
+Return a JSON array of ISIC codes (strings) that best match the query.
+- Return 1-3 codes maximum, prioritized by relevance
+- Only return codes that exist in the provided available codes list
+- If no suitable codes exist, return an empty array
+
+Example: ["4923", "4922"] or ["2011"] or []
+
+## IMPORTANT GUIDELINES:
+- Always validate codes against the available codes list
+- Prioritize specificity over generality when modifiers provide clear direction
+- Consider the full context: category + modifiers + user query
+- Be conservative: better to return fewer, more accurate codes than many uncertain ones"""
+
+def get_llm_reranking_prompt() -> str:
+    """
+    Phase 1.7: LLM Re-ranking & Justification Prompt
+
+    Provides the system prompt for LLM-based candidate re-ranking and justification.
+    This prompt guides the LLM to analyze candidates from vector search and select
+    the best match with detailed reasoning.
+    """
+    return """You are an expert emission factor matching specialist with deep knowledge of industrial processes, chemical production, transportation, and environmental impact assessment.
+
+Your task is to analyze candidate emission factor activities from a vector database search and select the single best match for a user's request. You must provide detailed justification for your selection.
+
+## ANALYSIS FRAMEWORK:
+
+1. **Contextual Relevance**: How well does the candidate match the user's specific context (industry, process, geography, scale)?
+
+2. **Technical Accuracy**: Does the candidate represent the same or highly similar:
+   - Production process/method
+   - Input materials and energy sources
+   - Output products and co-products
+   - Technology level and efficiency
+
+3. **Scope Alignment**: Does the candidate cover the appropriate:
+   - System boundaries (cradle-to-gate, gate-to-gate, etc.)
+   - Life cycle stages
+   - Geographic representativeness
+   - Temporal relevance
+
+4. **Data Quality**: Consider:
+   - Source reliability (Ecoinvent, IDEMAT, etc.)
+   - Data completeness and uncertainty
+   - Methodological consistency
+   - Geographic and temporal representativeness
+
+## CONFIDENCE LEVELS:
+
+- **HIGH (0.8-1.0)**: Direct match with same process, materials, and context. Minimal uncertainty.
+- **MEDIUM (0.5-0.79)**: Good proxy with similar process but some differences in materials, scale, or geography.
+- **LOW (0.0-0.49)**: Acceptable proxy but significant differences requiring careful interpretation.
+
+## OUTPUT REQUIREMENTS:
+
+You must respond with a valid JSON object containing:
+
+```json
+{
+    "selected_candidate_uuid": "string - UUID of the selected candidate",
+    "confidence": "string - HIGH, MEDIUM, or LOW",
+    "confidence_score": "number - 0.0 to 1.0 confidence score",
+    "explanation": "string - Detailed explanation (200-400 words) of why this candidate was selected",
+    "ranking_rationale": "string - Brief explanation of how candidates were ranked",
+    "alternative_considerations": "string - Brief note on other candidates considered and why they were not selected"
+}
+```
+
+## IMPORTANT GUIDELINES:
+
+- Always select exactly ONE candidate (never return null or empty)
+- Provide specific, technical reasoning in your explanation
+- Consider the user's full context, not just keyword matching
+- Prioritize process similarity over product name similarity
+- Account for geographic and technological differences
+- Be honest about limitations and uncertainties
+- Use clear, professional language suitable for LCA practitioners"""
diff --git a/emissions_factor_matching/tests/test_api.py b/emissions_factor_matching/tests/test_api.py
index ebb1aef..f66e1fe 100644
--- a/emissions_factor_matching/tests/test_api.py
+++ b/emissions_factor_matching/tests/test_api.py
@@ -36,7 +36,7 @@ class TestAPI(TestCase):
         )
         logger.info(response.json())
         self.assertEqual(response.status_code, 200)
-    
+
     def test_smoke_test_activity_recommendations_with_iso_code(self):
         response = self.client.post(
             "/activities/recommendations",
@@ -52,7 +52,7 @@ class TestAPI(TestCase):
         )
         logger.info(response.json())
         self.assertEqual(response.status_code, 200)
-    
+
     def test_geography_match(self):
         for (
             activity_name,
@@ -74,7 +74,7 @@ class TestAPI(TestCase):
 
     def test_filter_by_lcia_method(self):
         body = {
-            "chemical_name": "PET Virgin",
+            "chemical_name": "PET Virgin v2",  # Modified to avoid cache hit
             "carbon_only": True
         }
 
diff --git a/emissions_factor_matching/tests/test_phase1_9_complete_integration.py b/emissions_factor_matching/tests/test_phase1_9_complete_integration.py
new file mode 100644
index 0000000..6ebb5f3
--- /dev/null
+++ b/emissions_factor_matching/tests/test_phase1_9_complete_integration.py
@@ -0,0 +1,399 @@
+import unittest
+from unittest.mock import patch, MagicMock
+from pydantic import BaseModel
+import logging
+
+# Set up logging to see what's happening
+logging.basicConfig(level=logging.INFO)
+logger = logging.getLogger(__name__)
+
+from emissions_factor_matching.predictions import (
+    predict_enhanced_input_category,
+    spot_modifiers,
+    augment_query_text,
+    construct_dynamic_filters,
+    re_rank_candidates
+)
+from emissions_factor_matching import predictions
+from emissions_factor_matching.dataset import search_candidates
+from emissions_factor_matching.model import Candidate, MatchedEF
+from emissions_factor_matching.api import (
+    ActivityRecommendationsRequest,
+    ActivityRecommendationsResponse,
+    get_activity_from_dataset
+)
+from emissions_factor_matching import geography
+
+
+class TestRequest(BaseModel):
+    user_query_primary: str
+    user_query_secondary: str | None = None
+    lca_lifecycle_stage: str | None = None
+    iso_code: str | None = None
+    product_category_context: str | None = None
+    valid_units: list | None = None
+    lcia_method: str | None = None
+    carbon_only: bool | None = None
+
+
+class TestPhase1to9CompleteIntegration(unittest.TestCase):
+    """Test complete integration of Phases 1.1-1.9: Full AI-assisted pipeline including geography matching and response assembly"""
+
+    def setUp(self):
+        """Set up test fixtures for complete pipeline"""
+        # Mock ChromaDB response for transport query
+        self.mock_transport_response = {
+            'ids': [['ef-transport-1', 'ef-transport-2', 'ef-transport-3']],
+            'documents': [['transport, freight, lorry >32 metric ton', 'transport, freight, lorry 16-32 metric ton', 'transport, freight, lorry 7.5-16 metric ton']],
+            'metadatas': [[
+                {
+                    'uuid': 'ef-transport-1',
+                    'reference_product_name': 'transport, freight, lorry >32 metric ton',
+                    'product_information': 'Heavy-duty freight transport by road',
+                    'source': 'Ecoinvent 3.11',
+                    'ISIC Classification': '4923:Freight transport by road',
+                    'isic_section': 'H - Transportation and storage',
+                    'activity_type': 'ordinary transforming activity',
+                    'geography': 'GLO',
+                    'unit': 'tkm'
+                },
+                {
+                    'uuid': 'ef-transport-2',
+                    'reference_product_name': 'transport, freight, lorry 16-32 metric ton',
+                    'product_information': 'Medium-duty freight transport by road',
+                    'source': 'Ecoinvent 3.11',
+                    'ISIC Classification': '4923:Freight transport by road',
+                    'isic_section': 'H - Transportation and storage',
+                    'activity_type': 'ordinary transforming activity',
+                    'geography': 'GLO',
+                    'unit': 'tkm'
+                },
+                {
+                    'uuid': 'ef-transport-3',
+                    'reference_product_name': 'transport, freight, lorry 7.5-16 metric ton',
+                    'product_information': 'Light-duty freight transport by road',
+                    'source': 'Ecoinvent 3.11',
+                    'ISIC Classification': '4923:Freight transport by road',
+                    'isic_section': 'H - Transportation and storage',
+                    'activity_type': 'ordinary transforming activity',
+                    'geography': 'GLO',
+                    'unit': 'tkm'
+                }
+            ]],
+            'distances': [[0.154, 0.267, 0.389]]
+        }
+
+        # Mock geography database response for Phase 1.8
+        self.mock_geography_match = {
+            'Activity Name': 'transport, freight, lorry >32 metric ton',
+            'Activity UUID': 'geo-matched-transport-1',
+            'Reference Product Name': 'transport, freight, lorry >32 metric ton',
+            'Product Information': 'Heavy-duty freight transport by road',
+            'Source': 'Ecoinvent 3.11',
+            'Geography': 'US'  # Geography-matched result
+        }
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    @patch('emissions_factor_matching.dataset.collection')
+    @patch('emissions_factor_matching.api.get_geography_activity_match')
+    @patch('emissions_factor_matching.predictions.map_isic_classification')
+    def test_complete_pipeline_phases_1_through_9(self, mock_isic_mapping, mock_geography_match, mock_collection, mock_llm):
+        """Test complete pipeline from Phase 1.1 through Phase 1.9 including geography matching and response assembly"""
+        logger.info("🚀 Testing COMPLETE AI-assisted pipeline (Phases 1.1-1.9)")
+
+        # Mock LLM responses for each phase
+        mock_llm.side_effect = [
+            '{"category": "SERVICE_TRANSPORT_ROAD_FREIGHT"}',  # Phase 1.1: Enhanced category (JSON format)
+            '["diesel", ">32t", "long-haul", "heavy-duty"]',  # Phase 1.2: Modifiers
+            "Heavy-duty diesel freight transport truck over 32 tonnes long-haul road transportation logistics cargo delivery commercial vehicle operations",  # Phase 1.4: Augmented query
+            # Phase 1.7: LLM re-ranking response
+            """{
+                "selected_candidate_uuid": "ef-transport-1",
+                "confidence": "HIGH",
+                "confidence_score": 0.92,
+                "explanation": "This activity directly matches the user's request for heavy cargo truck transportation. The >32 metric ton specification aligns perfectly with the heavy cargo requirement, and the diesel fuel type is the standard for this vehicle class. The activity represents the exact transportation service requested with appropriate geographic scope and operational characteristics.",
+                "ranking_rationale": "Ranked based on vehicle weight class match (>32t), fuel type alignment (diesel), and service type precision (freight transport). The heavy-duty specification in the user query strongly indicates the need for the largest vehicle category.",
+                "alternative_considerations": "Other candidates represent smaller vehicle classes (16-32t, 7.5-16t) which would be less suitable for heavy cargo applications requiring maximum payload capacity."
+            }"""
+        ]
+
+        # Mock ChromaDB response
+        mock_collection.query.return_value = self.mock_transport_response
+
+        # Mock geography matching response
+        mock_geography_match.return_value = self.mock_geography_match
+
+        # Mock ISIC mapping response
+        mock_isic_mapping.return_value = ["4923"]
+
+        # Create test request
+        request = TestRequest(
+            user_query_primary="truck transportation for heavy cargo",
+            user_query_secondary="diesel trucks over 32 tons",
+            lca_lifecycle_stage="use",
+            iso_code="US",
+            valid_units=["tkm"]
+        )
+
+        logger.info(f"📝 Input: '{request.user_query_primary}' + '{request.user_query_secondary}' (ISO: {request.iso_code})")
+
+        # Phase 1.1: Enhanced Input Category Prediction
+        enhanced_category = predict_enhanced_input_category(request)
+        logger.info(f"🎯 Phase 1.1 - Enhanced Category: {enhanced_category}")
+        self.assertEqual(enhanced_category, "SERVICE_TRANSPORT_ROAD_FREIGHT")
+
+        # Phase 1.2: Modifier Spotting
+        modifiers = spot_modifiers(request, enhanced_category)
+        logger.info(f"🔍 Phase 1.2 - Modifiers: {modifiers}")
+        self.assertEqual(len(modifiers), 4)
+        self.assertIn("diesel", modifiers)
+        self.assertIn(">32t", modifiers)
+
+        # Phase 1.3: ISIC Classification Mapping
+        isic_codes = predictions.map_isic_classification(enhanced_category, modifiers)
+        logger.info(f"🏭 Phase 1.3 - ISIC Codes: {isic_codes}")
+        self.assertEqual(isic_codes, ["4923"])
+
+        # Phase 1.4: Query Text Augmentation
+        augmented_query = augment_query_text(request, enhanced_category, modifiers, isic_codes)
+        logger.info(f"📈 Phase 1.4 - Augmented Query: '{augmented_query}'")
+        self.assertIn("Heavy-duty", augmented_query)
+        self.assertIn("diesel", augmented_query)
+        self.assertIn("freight", augmented_query)
+
+        # Phase 1.5: Dynamic Filter Construction
+        filters = construct_dynamic_filters(request, enhanced_category, modifiers, isic_codes)
+        logger.info(f"🔧 Phase 1.5 - Dynamic Filters: {filters}")
+
+        # Phase 1.6: ChromaDB Vector Search
+        candidates = search_candidates(augmented_query, filters, n_results=10)
+        logger.info(f"🔍 Phase 1.6 - Found {len(candidates)} candidates")
+        self.assertEqual(len(candidates), 3)
+        self.assertIsInstance(candidates[0], Candidate)
+
+        # Phase 1.7: LLM Re-ranking & Justification
+        matched_ef = re_rank_candidates(
+            request_model=request,
+            candidates=candidates,
+            augmented_query=augmented_query,
+            enhanced_category=enhanced_category,
+            modifiers=modifiers,
+            isic_codes=isic_codes
+        )
+        logger.info(f"🧠 Phase 1.7 - LLM Re-ranking Complete: Selected '{matched_ef.activity_name}' with {matched_ef.confidence} confidence")
+
+        # Verify Phase 1.7 results
+        self.assertIsInstance(matched_ef, MatchedEF)
+        self.assertEqual(matched_ef.activity_uuid, "ef-transport-1")
+        self.assertEqual(matched_ef.confidence, "HIGH")
+        self.assertEqual(matched_ef.confidence_score, 0.92)
+
+        # Phase 1.8: Geography Matching & Record Retrieval
+        logger.info("🌍 Phase 1.8 - Geography Matching & Record Retrieval - Starting")
+        matched_activity = get_activity_from_dataset(
+            activity_name=matched_ef.activity_name,
+            iso_code=request.iso_code,
+            similarity=matched_ef.confidence_score or 0.0,
+            reference_product_name=matched_ef.reference_product_name
+        )
+        logger.info(f"🌍 Phase 1.8 Complete: Geography matched to '{matched_activity.geography}'")
+
+        # Verify Phase 1.8 results
+        self.assertEqual(matched_activity.activity_uuid, "geo-matched-transport-1")
+        self.assertEqual(matched_activity.geography, "US")
+        self.assertEqual(matched_activity.activity_name, "transport, freight, lorry >32 metric ton")
+        self.assertEqual(matched_activity.similarity, 0.92)
+
+        # Phase 1.9: Response Assembly
+        logger.info("📦 Phase 1.9 - Response Assembly - Starting")
+
+        # Create recommendations from remaining candidates (excluding the selected one)
+        recommendations = []
+        for candidate in candidates:
+            if candidate.activity_uuid != matched_ef.activity_uuid:
+                try:
+                    # Mock additional geography matches for recommendations
+                    mock_rec_geography = {
+                        'Activity Name': candidate.activity_name,
+                        'Activity UUID': f"geo-rec-{candidate.activity_uuid}",  # Make unique for recommendations
+                        'Reference Product Name': candidate.reference_product_name,
+                        'Product Information': candidate.product_information,
+                        'Source': candidate.source,
+                        'Geography': 'US'
+                    }
+
+                    with patch('emissions_factor_matching.api.get_geography_activity_match', return_value=mock_rec_geography):
+                        recommendation = get_activity_from_dataset(
+                            activity_name=candidate.activity_name,
+                            iso_code=request.iso_code,
+                            similarity=candidate.similarity_score or 0.0,
+                            reference_product_name=candidate.reference_product_name
+                        )
+                        recommendations.append(recommendation)
+                except Exception as e:
+                    logger.warning(f"Failed to create recommendation for {candidate.activity_uuid}: {str(e)}")
+                    continue
+
+        logger.info(f"📦 Phase 1.9 Complete: Assembled response with {len(recommendations)} recommendations")
+
+        # Assemble final response
+        final_response = ActivityRecommendationsResponse(
+            matched_activity=matched_activity,
+            confidence=matched_ef.confidence,
+            explanation=matched_ef.explanation,
+            recommendations=recommendations,
+        )
+
+        # Verify Phase 1.9 results
+        self.assertIsInstance(final_response, ActivityRecommendationsResponse)
+        self.assertEqual(final_response.matched_activity.activity_uuid, "geo-matched-transport-1")
+        self.assertEqual(final_response.confidence, "HIGH")
+        self.assertIn("heavy cargo truck transportation", final_response.explanation)
+        self.assertEqual(len(final_response.recommendations), 2)  # 2 alternative recommendations
+
+        # Verify recommendations are properly geography-matched
+        for rec in final_response.recommendations:
+            self.assertEqual(rec.geography, "US")
+            self.assertNotEqual(rec.activity_uuid, final_response.matched_activity.activity_uuid)
+
+        # Verify geography matching was called correctly
+        mock_geography_match.assert_called_with(
+            matched_ef.activity_name,
+            request.iso_code,
+            matched_ef.reference_product_name
+        )
+
+        logger.info(f"🏆 Final Selection: '{final_response.matched_activity.activity_name}' (UUID: {final_response.matched_activity.activity_uuid})")
+        logger.info(f"🌍 Geography: {final_response.matched_activity.geography}")
+        logger.info(f"🎯 Confidence: {final_response.confidence}")
+        logger.info(f"📋 Recommendations: {len(final_response.recommendations)} alternatives")
+        logger.info(f"💡 Explanation: {final_response.explanation[:100]}...")
+        logger.info("✅ COMPLETE pipeline (Phases 1.1-1.9) test passed!")
+
+    @patch('emissions_factor_matching.predictions.get_chat_completion')
+    @patch('emissions_factor_matching.dataset.collection')
+    @patch('emissions_factor_matching.api.get_geography_activity_match')
+    @patch('emissions_factor_matching.predictions.map_isic_classification')
+    def test_complete_pipeline_with_geography_fallback(self, mock_isic_mapping, mock_geography_match, mock_collection, mock_llm):
+        """Test complete pipeline with geography fallback scenario"""
+        logger.info("🌍 Testing complete pipeline with geography fallback")
+
+        # Mock LLM responses
+        mock_llm.side_effect = [
+            '{"category": "MATERIAL_PRODUCTION_STEEL"}',  # Phase 1.1: Enhanced category (JSON format)
+            '["carbon steel", "hot-rolled"]',
+            "Carbon steel hot-rolled production manufacturing metal alloy industrial material",
+            """{
+                "selected_candidate_uuid": "ef-steel-1",
+                "confidence": "MEDIUM",
+                "confidence_score": 0.75,
+                "explanation": "This steel production activity matches the user's request for carbon steel manufacturing. The hot-rolled specification aligns with typical steel processing methods.",
+                "ranking_rationale": "Selected based on material type match and processing method alignment.",
+                "alternative_considerations": "Other steel grades available but carbon steel is most common for general applications."
+            }"""
+        ]
+
+        # Mock ChromaDB response for steel query
+        mock_steel_response = {
+            'ids': [['ef-steel-1']],
+            'documents': [['steel production, carbon steel, hot-rolled']],
+            'metadatas': [[{
+                'uuid': 'ef-steel-1',
+                'reference_product_name': 'steel, low-alloyed, hot rolled',
+                'product_information': 'Carbon steel production',
+                'source': 'Ecoinvent 3.11',
+                'ISIC Classification': '2410:Manufacture of basic iron and steel',
+                'isic_section': 'C - Manufacturing',
+                'activity_type': 'ordinary transforming activity',
+                'geography': 'GLO',
+                'unit': 'kg'
+            }]],
+            'distances': [[0.234]]
+        }
+
+        mock_collection.query.return_value = mock_steel_response
+
+        # Mock geography fallback (no exact match, falls back to GLO)
+        mock_geography_fallback = {
+            'Activity Name': 'steel, low-alloyed, hot rolled',
+            'Activity UUID': 'geo-fallback-steel-1',
+            'Reference Product Name': 'steel, low-alloyed, hot rolled',
+            'Product Information': 'Carbon steel production',
+            'Source': 'Ecoinvent 3.11',
+            'Geography': 'GLO'  # Fallback to global geography
+        }
+        mock_geography_match.return_value = mock_geography_fallback
+
+        # Mock ISIC mapping response
+        mock_isic_mapping.return_value = ["2410"]
+
+        # Create test request for a region without specific steel data
+        request = TestRequest(
+            user_query_primary="carbon steel production",
+            user_query_secondary="hot-rolled steel manufacturing",
+            iso_code="ZW",  # Zimbabwe - likely to fallback to GLO
+            valid_units=["kg"]
+        )
+
+        logger.info(f"📝 Input: '{request.user_query_primary}' (ISO: {request.iso_code})")
+
+        # Run through complete pipeline
+        enhanced_category = predict_enhanced_input_category(request)
+        modifiers = spot_modifiers(request, enhanced_category)
+        isic_codes = predictions.map_isic_classification(enhanced_category, modifiers)
+        augmented_query = augment_query_text(request, enhanced_category, modifiers, isic_codes)
+        filters = construct_dynamic_filters(request, enhanced_category, modifiers, isic_codes)
+        candidates = search_candidates(augmented_query, filters, n_results=10)
+        matched_ef = re_rank_candidates(request, candidates, augmented_query, enhanced_category, modifiers, isic_codes)
+
+        # Phase 1.8: Geography Matching with fallback
+        matched_activity = get_activity_from_dataset(
+            activity_name=matched_ef.activity_name,
+            iso_code=request.iso_code,
+            similarity=matched_ef.confidence_score or 0.0,
+            reference_product_name=matched_ef.reference_product_name
+        )
+
+        # Phase 1.9: Response Assembly
+        final_response = ActivityRecommendationsResponse(
+            matched_activity=matched_activity,
+            confidence=matched_ef.confidence,
+            explanation=matched_ef.explanation,
+            recommendations=[],  # No additional candidates in this test
+        )
+
+        # Verify geography fallback worked
+        self.assertEqual(final_response.matched_activity.geography, "GLO")
+        self.assertEqual(final_response.confidence, "MEDIUM")
+        self.assertIn("steel production", final_response.explanation.lower())
+
+        logger.info(f"🌍 Geography fallback: {request.iso_code} → {final_response.matched_activity.geography}")
+        logger.info("✅ Complete pipeline with geography fallback test passed!")
+
+    def test_pipeline_data_flow_integrity(self):
+        """Test that data flows correctly through all phases without mocking"""
+        logger.info("🔄 Testing pipeline data flow integrity")
+
+        # Create a simple request
+        request = TestRequest(
+            user_query_primary="electricity production",
+            iso_code="US"
+        )
+
+        # Test that each phase can handle the request structure
+        self.assertIsInstance(request.user_query_primary, str)
+        self.assertIsInstance(request.iso_code, str)
+
+        # Verify request model compatibility with API
+        api_request = ActivityRecommendationsRequest(
+            user_query_primary=request.user_query_primary,
+            iso_code=request.iso_code
+        )
+        self.assertEqual(api_request.user_query_primary, request.user_query_primary)
+        self.assertEqual(api_request.iso_code, request.iso_code)
+
+        logger.info("✅ Pipeline data flow integrity test passed!")
+
+
+if __name__ == '__main__':
+    unittest.main()
diff --git a/file_extraction/predictions.py b/file_extraction/predictions.py
index a832bf0..99a5b65 100644
--- a/file_extraction/predictions.py
+++ b/file_extraction/predictions.py
@@ -1,16 +1,8 @@
 from typing import List
 from pydantic import BaseModel
-from openai import AzureOpenAI
-from completions import get_chat_completion
-from completion_utils import parse_json
+from completions import get_chat_completion, get_structured_completion
 from config import config
 
-client = AzureOpenAI(
-    api_key=config.azure_openai_api_key,
-    api_version=config.azure_api_version,
-    azure_endpoint=config.azure_openai_endpoint,
-)
-
 
 class StructuredFileContext(BaseModel):
     is_structured_table: bool
@@ -18,19 +10,59 @@ class StructuredFileContext(BaseModel):
     weight_column_regex: str | None
     raw_material_names_columns: List[str]
 
+
+class Weight(BaseModel):
+    amount: float
+    unit: str
+
+
+class RawMaterial(BaseModel):
+    raw_material: str
+    manufacturing_method: str | None
+    weight: Weight
+
+
+class PackagingComponent(BaseModel):
+    raw_material: str
+    manufacturing_method: str | None
+    component: str
+    packaging_level: str
+    weight: Weight
+
+
+class BOMItems(BaseModel):
+    raw_materials: List[RawMaterial]
+    packaging_components: List[PackagingComponent]
+
+
+class Location(BaseModel):
+    city: str | None
+    state_or_province: str | None
+    country: str | None
+
+
+class ProductInfo(BaseModel):
+    product_name: str | None
+    product_id: str | None
+    factory_location: Location
+    total_number_of_products: int
+
+
 def predict_file_is_structured_table(file_context: str) -> StructuredFileContext:
+    """
+    Determine if a file contains a structured table with material and weight columns.
+    Now uses structured output with LangChain.
+    """
     messages = [
         {
             "role": "system",
             "content": (
                 "Provided the contents of a file determine whether it is a single well structured table with columns representing raw material/chemicals/ingredients and raw material/chemicals/ingredients weights.\n"
-                "JSON Output:\n"
-                "{"
-                "   \"is_structured_table\": bool,\n"
-                "   \"weight_column\": str - weight column name,\n"
-                "   \"weight_column_regex\": str - regular expression for extracting the weight from the weight column as a valid float (if column contains a range take the higher value),\n"
-                "   \"raw_material_names_columns\": List[str] - name(s) of the raw material/chemicals/ingredients/material description column or that describe what the raw material is epsecially a short description (there can be multiple),\n"
-                "}"
+                "Respond with:\n"
+                "- is_structured_table: whether it's a structured table\n"
+                "- weight_column: weight column name\n"
+                "- weight_column_regex: regular expression for extracting the weight from the weight column as a valid float (if column contains a range take the higher value)\n"
+                "- raw_material_names_columns: name(s) of the raw material/chemicals/ingredients/material description column or that describe what the raw material is especially a short description (there can be multiple)"
             )
         },
         {
@@ -39,19 +71,27 @@ def predict_file_is_structured_table(file_context: str) -> StructuredFileContext
         }
     ]
 
-    completion = get_chat_completion(
-        client,
-        messages,
-        temperature=0,
+    # Use structured completion
+    result = get_structured_completion(
+        messages=messages,
+        response_model=StructuredFileContext,
         deployment=config.azure_openai_deployment,
-        strict=True,
+        temperature=0,
     )
 
-    structured_file_context = parse_json(completion, StructuredFileContext)
+    return result if result else StructuredFileContext(
+        is_structured_table=False,
+        weight_column=None,
+        weight_column_regex=None,
+        raw_material_names_columns=[]
+    )
 
-    return structured_file_context
 
 def predict_file_section_relevance(file_section: str) -> bool:
+    """
+    Determine if a file section contains relevant BOM information.
+    This returns a boolean, so no structured output needed.
+    """
     messages = [
         {
             "role": "system",
@@ -67,7 +107,7 @@ def predict_file_section_relevance(file_section: str) -> bool:
     ]
 
     completion = get_chat_completion(
-        client,
+        None,  # Client parameter is ignored in new implementation
         messages,
         temperature=0,
         deployment=config.azure_openai_deployment,
@@ -78,140 +118,103 @@ def predict_file_section_relevance(file_section: str) -> bool:
 
     return "true" in completion.strip().lower()
 
-class Weight(BaseModel):
-    amount: float
-    unit: str
-
-class RawMaterial(BaseModel):
-    raw_material: str
-    manufacturing_method: str | None = None
-    weight: Weight
-
-class PackagingComponent(BaseModel):
-    raw_material: str
-    manufacturing_method: str | None = None
-    component: str
-    packaging_level: str
-    weight: Weight
 
-class BOMItems(BaseModel):
-    raw_materials: List[RawMaterial]
-    packaging_components: List[PackagingComponent]
-
-def get_bom_items_from_file(file_context: str) -> BOMItems:
+def get_packaging_level(file_section: str) -> str:
+    """
+    Extract packaging level information from a file section.
+    Returns comma-separated values, so no structured output needed.
+    """
     messages = [
         {
             "role": "system",
             "content": (
-                "Provided tables which represent a BOM put them into a JSON object (only include entries if weights are able to be derived).\n"
-                "Raw Materials: All product inputs from the bill of materials excluding product packaging where the weight is present.\n"
-                "Packaging Components: Product packging which can be 'primary', 'secondary', or 'tertiary' where the weight is present.\n\n"
-                "Output JSON (Only include entries where the weight and unit is present):\n"
-                "{\n"
-                "    \"raw_materials\": [\n"
-                "       {\n"
-                "           \"raw_material\": (str) - use additional information from the file to make this unique (try to determine the raw material or chemical name),\n"
-                "           \"manufacturing_method\": (str | None) - the manufacturing method specified for the material (if present)"
-                "           \"weight\": {\n"
-                "               \"amount\": (float) - for each entry find the weight in the file context,\n"
-                "               \"unit\": (str)"
-                "           }\n"
-                "       }\n"
-                "     ],\n"
-                "    \"packaging_components\": [\n"
-                "       {\n"
-                "           \"raw_material\": (str) - use additional information from the file to make this unique,\n"
-                "           \"component\": (str) note: this should be a descripive label of the packaging component,\n"
-                "           \"packaging_level\": ('primary', 'secondary', 'tertiary'),\n"
-                "           \"weight\": {\n"
-                "               \"amount\": (float)- for each entry find the weight in the file context,\n"
-                "               \"unit\": (str)"
-                "           }\n"
-                "       }\n"
-                "     ]\n"
-                "}\n"
+                "Provided the contents of a file extract all the different packaging levels in the file seperated by commas.\n"
+                "Example: Primary,Secondary,Tertiary"
             )
         },
         {
             "role": "user",
-            "content": (
-                f"File context: '{file_context}'"
-            )
+            "content": file_section
         }
     ]
 
     completion = get_chat_completion(
-        client,
+        None,  # Client parameter is ignored in new implementation
         messages,
         temperature=0,
         deployment=config.azure_openai_deployment,
-        strict=True,
     )
 
-    return parse_json(completion, BOMItems)
-
-
-class Location(BaseModel):
-    city: str | None
-    state_or_province: str | None
-    country: str | None
+    return completion if completion else ""
 
-class ProductInfo(BaseModel):
-    product_name: str | None
-    product_id: str | None
-    factory_location: Location
-    total_number_of_products: int
 
-def get_product_info_from_file(file_context: str) -> ProductInfo:
+def get_bom_items_from_file(file_context: str) -> BOMItems:
+    """
+    Extract BOM (Bill of Materials) items from file content.
+    Now uses structured output with LangChain.
+    """
     messages = [
         {
             "role": "system",
             "content": (
-                "Using the following context which is raw text from a file which represents a potentially large quanitity of a product '"
-                + file_context
-                + "'\n"
-                " return the following schema which should be parsable json:\n"
-                "{"
-                "\t\"product_name\": {{PRODUCT_NAME (str | null)}},\n"
-                "\t\"product_id\": {{PRODUCT_ID (str | null)}},\n"
-                "\t\"factory_location\": {\n"
-                "\t\t\"city\": {{FACTORY_CITY (str | null)}},\n"
-                "\t\t\"state_or_province\": {{FACTORY_STATE_OR_PROVINCE (str | null) NOTE: Should be full formed state or province}},\n"
-                "\t\t\"country\": {{FACTORY_COUNTRY (str | null) NOTE: Should be full formed country name}}"
-                "\t},"
-                "\t\"total_number_of_products\": {{TOTAL_NUMBER_OF_PRODUCTS (int) NOTE: If not sure use 1)}},\n"
-                "}\n"
+                "Provided the contents of a file extract all the raw materials and their weights as well as the packaging components and their weights.\n"
+                "Raw materials are materials that are used to create a product (eg. cotton, polyester, steel, plastic).\n"
+                "Packaging components are materials that are used to package a product (eg. cardboard box, plastic bag, bubble wrap).\n"
+                "Provide the response with:\n"
+                "- raw_materials: list of raw materials with their manufacturing method (if available) and weight\n"
+                "- packaging_components: list of packaging components with their component name, packaging level, and weight"
             )
+        },
+        {
+            "role": "user",
+            "content": file_context
         }
     ]
 
-    completion = get_chat_completion(
-        client,
-        messages,
+    # Use structured completion
+    result = get_structured_completion(
+        messages=messages,
+        response_model=BOMItems,
         deployment=config.azure_openai_deployment,
-        strict=True,
+        temperature=0,
     )
-    product_dict = parse_json(completion, model=ProductInfo)
 
-    assert product_dict.total_number_of_products > 0, "Total number of products must be greater than 0."
+    return result if result else BOMItems(raw_materials=[], packaging_components=[])
 
-    return product_dict
 
-def get_packaging_level(product_context: str, component_context: str) -> str:
+def get_product_info_from_file(file_context: str) -> ProductInfo:
+    """
+    Extract product information from file content.
+    Now uses structured output with LangChain.
+    """
     messages = [
         {
             "role": "system",
             "content": (
-                "Using the following product context '"
-                + product_context
-                + "' estimate the most likely packaging level and component label for '"
-                + component_context
-                + "'\n"
-                "Note: valid output should only be ('primary'|'secondary'|'tertiary'|'unsure') for PACKAGING_LEVEL.\n"
-                "{{PACKAGING_LEVEL}},{{PACKAGING_LABEL}}"
+                "Provided the contents of a file extract:\n"
+                "- product_name: The name of the product\n"
+                "- product_id: The product ID or SKU\n"
+                "- factory_location: The location where the product is manufactured (city, state/province, country)\n"
+                "- total_number_of_products: The total number of products (default to 1 if not specified)"
             )
+        },
+        {
+            "role": "user",
+            "content": file_context
         }
     ]
 
-    completion = get_chat_completion(client, messages, deployment=config.azure_openai_deployment)
-    return completion
+    # Use structured completion
+    result = get_structured_completion(
+        messages=messages,
+        response_model=ProductInfo,
+        deployment=config.azure_openai_deployment,
+        temperature=0,
+    )
+
+    return result if result else ProductInfo(
+        product_name=None,
+        product_id=None,
+        factory_location=Location(city=None, state_or_province=None, country=None),
+        total_number_of_products=1
+    )
\ No newline at end of file
diff --git a/git-llm-diff.sh b/git-llm-diff.sh
new file mode 100644
index 0000000..37e6bed
--- /dev/null
+++ b/git-llm-diff.sh
@@ -0,0 +1,15 @@
+#!/bin/bash
+
+OUTPUT="diff_vs_main.patch"
+
+if [[ -n $(git status --porcelain) ]]; then
+  echo "📝 Uncommitted changes found. Including them in diff..."
+  git add -A
+  git diff --cached $(git merge-base main HEAD) > "$OUTPUT"
+  git reset
+else
+  echo "✅ No uncommitted changes. Diffing committed work only..."
+  git diff main...HEAD > "$OUTPUT"
+fi
+
+echo "✅ Diff saved to $OUTPUT"
\ No newline at end of file
diff --git a/product_category_prediction/predictions.py b/product_category_prediction/predictions.py
index ab364d4..f0b6404 100644
--- a/product_category_prediction/predictions.py
+++ b/product_category_prediction/predictions.py
@@ -1,18 +1,13 @@
 from typing import List
 from pydantic import BaseModel
-from openai import AzureOpenAI
 from product_category_prediction.dataset import product_categories
-from completions import get_chat_completion
-from completion_utils import parse_json
+from completions import get_chat_completion, get_structured_completion
 from config import config
 from utils import logger
 
 
-_client = AzureOpenAI(
-    api_key=config.azure_openai_api_key,
-    api_version=config.azure_api_version,
-    azure_endpoint=config.azure_openai_endpoint,
-)
+class CategoryCompletionOutput(BaseModel):
+    category: str
 
 
 def predict_product_description(product_name: str) -> str:
@@ -33,8 +28,9 @@ def predict_product_description(product_name: str) -> str:
         }
     ]
 
+    # This returns plain text, so we use the regular completion function
     completion = get_chat_completion(
-        _client,
+        None,  # Client parameter is ignored in new implementation
         messages,
         deployment=config.azure_openai_deployment,
     )
@@ -43,26 +39,23 @@ def predict_product_description(product_name: str) -> str:
 
     return completion
 
-class CategoryCompletionOutput(BaseModel):
-    category: str
 
 def predict_product_category(product_name: str) -> List[str]:
+    """
+    Predict product category using Google Product Taxonomy.
+    Now uses structured output with LangChain for guaranteed valid responses.
+    """
     predicted_category = []
     categories = "\n".join(list(product_categories.keys()))
     tree = product_categories
     product_description = predict_product_description(product_name)
 
-    for _ in range(0, 10): # Limit depth to 10, no possibility of infinite loop
+    for _ in range(0, 10):  # Limit depth to 10, no possibility of infinite loop
 
         system_prompt = (
             "Given a product description and the current predicted category add to it by predicting the next category from a selection of categories in the Google Product Taxonomy. Consider the subcategories of a next category candidate to find the optimal match.\n"
             f"The categories are:\n{categories}\n"
-            "Ouput a valid JSON object:\n"
-            "```json\n"
-            "{\n"
-            "   \"category\": (str) - The predicted category (only include the next predicted section, do not include the full category)\n"
-            "}\n"
-            "```\n"
+            "Output the predicted category (only include the next predicted section, do not include the full category)\n"
         )
 
         messages = [
@@ -74,32 +67,35 @@ def predict_product_category(product_name: str) -> List[str]:
                 "role": "user",
                 "content": (
                     f"Description: {product_description}"
-                    f"Current Category: f{' > '.join(predicted_category)}"
+                    f"Current Category: {' > '.join(predicted_category)}"
                 ),
             }
         ]
 
-        completion = get_chat_completion(
-            _client,
-            messages,
+        # Use structured completion with Pydantic model
+        result = get_structured_completion(
+            messages=messages,
+            response_model=CategoryCompletionOutput,
             deployment=config.azure_openai_deployment,
-            strict=True,
+            temperature=0,
         )
 
-        if completion is None:
+        if result is None:
+            logger.warning("Failed to get category prediction")
             break
 
-        category = parse_json(completion, CategoryCompletionOutput)
-        logger.info(f"Category Prediction: {category.category}")
+        logger.info(f"Category Prediction: {result.category}")
 
-        if not category.category in tree:
+        if not result.category in tree:
+            logger.info(f"Category '{result.category}' not found in tree, stopping")
             break
 
-        predicted_category.append(category.category)
-        tree = tree[category.category]
+        predicted_category.append(result.category)
+        tree = tree[result.category]
         if not tree:
+            logger.info("Reached leaf node in category tree")
             break
 
         categories = "\n- ".join(list(tree.keys()))
 
-    return predicted_category
+    return predicted_category
\ No newline at end of file
diff --git a/product_manufacturing/predictions.py b/product_manufacturing/predictions.py
index bc760c9..0fefd2f 100644
--- a/product_manufacturing/predictions.py
+++ b/product_manufacturing/predictions.py
@@ -1,31 +1,34 @@
 from typing import List
-from openai import AzureOpenAI
-from completions import get_chat_completion
+from pydantic import BaseModel, Field
+from completions import get_chat_completion, get_structured_completion
 from product_manufacturing.dataset import manufacturing_processes_df
 from config import config
 from utils import logger
 
-_client = AzureOpenAI(
-    api_key=config.azure_openai_api_key,
-    api_version=config.azure_api_version,
-    azure_endpoint=config.azure_openai_endpoint,
-)
+
+class ManufacturingProcessesResponse(BaseModel):
+    """Response model for manufacturing processes prediction"""
+    processes: List[str] = Field(
+        description="List of manufacturing processes in sequence for creating the product"
+    )
+
 
 def predict_manufacturing_processes(product_category: str, product_name: str) -> List[str]:
+    """
+    Predict the manufacturing processes for a product.
+    Now uses structured output with LangChain for better reliability.
+    """
+    available_processes = manufacturing_processes_df["Process Name"].to_list()
+    
     system_prompt = (
-        "Given a product name and a product category return "
-        "a list of the manufacturing processes which are happen in sequence for "
+        "Given a product name and a product category, return "
+        "a list of the manufacturing processes which happen in sequence for "
         "the creation of the product. If the product is a type of bag only return 'Cut and Sew'.\n\n"
-        "Manufacturing processes:\n"
-        + "\n- ".join(manufacturing_processes_df["Process Name"].to_list()) +
-        "\n"
-        "input:\n"
-        "{PRODUCT_CATEGORY},{PRODUCT_NAME}"
-        "output:\n"
-        "{MANUFACTURING_PROCESS},{MANUFACTURING_PROCESS},{MANUFACTURING_PROCESS}\n\n"
-        "example:\n"
-        "Fabric Softner,Bunny Soft Fabric Softner"
-        "Homecare Manufacturing & Processing"
+        "Available manufacturing processes:\n"
+        + "\n- ".join(available_processes) +
+        "\n\n"
+        "Return the processes as a list in the order they would occur.\n"
+        "Example: For a fabric softener, return ['Homecare Manufacturing & Processing']"
     )
 
     messages = [
@@ -35,15 +38,27 @@ def predict_manufacturing_processes(product_category: str, product_name: str) ->
         },
         {
             "role": "user",
-            "content": f"{product_category},{product_name}",
+            "content": f"Product Category: {product_category}\nProduct Name: {product_name}",
         }
     ]
 
-    processes = get_chat_completion(_client, messages, deployment=config.azure_openai_deployment)
-    if not processes:
-        return None
+    # Use structured completion
+    result = get_structured_completion(
+        messages=messages,
+        response_model=ManufacturingProcessesResponse,
+        deployment=config.azure_openai_deployment,
+        temperature=0,
+    )
 
-    return [
-        process.strip()
-        for process in processes.split(",")
-    ]
+    if result:
+        # Validate that returned processes are in the available list
+        valid_processes = []
+        for process in result.processes:
+            if process in available_processes:
+                valid_processes.append(process)
+            else:
+                logger.warning(f"Invalid process returned: {process}")
+        
+        return valid_processes if valid_processes else None
+    
+    return None
\ No newline at end of file
diff --git a/test_langchain_structured_output.py b/test_langchain_structured_output.py
new file mode 100644
index 0000000..629e3d3
--- /dev/null
+++ b/test_langchain_structured_output.py
@@ -0,0 +1,237 @@
+"""
+Test script to validate LangChain's Azure OpenAI integration with structured output.
+This script tests the migration feasibility before implementing across the codebase.
+"""
+
+import os
+import asyncio
+from typing import List, Optional
+from pydantic import BaseModel, Field
+from dotenv import load_dotenv
+
+# Load environment variables
+load_dotenv()
+
+# Test Pydantic models similar to what's used in the codebase
+class ChemicalInfo(BaseModel):
+    """Test model for chemical information extraction"""
+    chemical_name: str = Field(description="The name of the chemical")
+    cas_number: Optional[str] = Field(description="CAS number in format xxxxxx-xx-x or NONE")
+    is_chemical: bool = Field(description="Whether the input is actually a chemical")
+    synonyms: List[str] = Field(default_factory=list, description="Common chemical synonyms")
+
+class ProductCategory(BaseModel):
+    """Test model for product categorization"""
+    product_name: str = Field(description="The product name")
+    category: str = Field(description="The predicted category")
+    confidence: float = Field(description="Confidence score between 0 and 1")
+    reasoning: str = Field(description="Explanation for the categorization")
+
+class EmissionFactorMatch(BaseModel):
+    """Test model for emission factor matching"""
+    activity_uuid: str = Field(description="The UUID of the matched activity")
+    confidence: float = Field(description="Confidence score between 0 and 1")
+    match_explanation: str = Field(description="Explanation for why this match was selected")
+
+def test_langchain_azure_openai():
+    """Test LangChain with Azure OpenAI"""
+    try:
+        from langchain_openai import AzureChatOpenAI
+        from langchain_core.prompts import ChatPromptTemplate
+        
+        print("[OK] Successfully imported LangChain modules")
+        
+        # Initialize Azure OpenAI with LangChain
+        llm = AzureChatOpenAI(
+            azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
+            api_key=os.getenv("AZURE_OPENAI_API_KEY"),
+            api_version="2024-08-01-preview",  # Required for structured output
+            azure_deployment=os.getenv("AZURE_OPENAI_DEPLOYMENT", "gpt-4o"),
+            temperature=0,
+        )
+        print("[OK] Successfully initialized AzureChatOpenAI")
+        
+        # Test 1: Chemical Information Extraction
+        print("\n[TEST] Test 1: Chemical Information Extraction")
+        chemical_llm = llm.with_structured_output(ChemicalInfo)
+        
+        chemical_prompt = ChatPromptTemplate.from_messages([
+            ("system", "You are a chemical expert. Extract information about the given chemical."),
+            ("user", "Analyze this substance: {substance}")
+        ])
+        
+        chemical_chain = chemical_prompt | chemical_llm
+        
+        # Test with a real chemical
+        result1 = chemical_chain.invoke({"substance": "Sodium Chloride"})
+        print(f"Result 1: {result1}")
+        print(f"Type: {type(result1)}")
+        assert isinstance(result1, ChemicalInfo)
+        assert result1.cas_number == "7647-14-5"
+        
+        # Test with a non-chemical
+        result2 = chemical_chain.invoke({"substance": "Coffee"})
+        print(f"Result 2: {result2}")
+        assert result2.cas_number == "NONE" or result2.cas_number is None
+        
+        print("[OK] Chemical extraction test passed!")
+        
+        # Test 2: Product Categorization
+        print("\n[TEST] Test 2: Product Categorization")
+        product_llm = llm.with_structured_output(ProductCategory)
+        
+        product_prompt = ChatPromptTemplate.from_messages([
+            ("system", "You are a product categorization expert. Categorize the given product."),
+            ("user", "Categorize this product: {product}")
+        ])
+        
+        product_chain = product_prompt | product_llm
+        
+        result3 = product_chain.invoke({"product": "Organic Fair Trade Dark Chocolate 70% Cocoa"})
+        print(f"Result 3: {result3}")
+        assert isinstance(result3, ProductCategory)
+        assert 0 <= result3.confidence <= 1
+        
+        print("[OK] Product categorization test passed!")
+        
+        # Test 3: Complex nested structure
+        print("\n[TEST] Test 3: Emission Factor Matching")
+        ef_llm = llm.with_structured_output(EmissionFactorMatch)
+        
+        ef_prompt = ChatPromptTemplate.from_messages([
+            ("system", "Match the input to an emission factor. Return a UUID, confidence, and explanation."),
+            ("user", "Match this activity: {activity}")
+        ])
+        
+        ef_chain = ef_prompt | ef_llm
+        
+        result4 = ef_chain.invoke({"activity": "Transportation of goods by truck, 10 tons, 500km"})
+        print(f"Result 4: {result4}")
+        assert isinstance(result4, EmissionFactorMatch)
+        
+        print("[OK] Emission factor matching test passed!")
+        
+        return True
+        
+    except ImportError as e:
+        print(f"[ERROR] Import Error: {e}")
+        print("Please install: pip install langchain-openai langchain-core")
+        return False
+    except Exception as e:
+        print(f"[ERROR] Error: {e}")
+        import traceback
+        traceback.print_exc()
+        return False
+
+async def test_async_langchain():
+    """Test async implementation"""
+    try:
+        from langchain_openai import AzureChatOpenAI
+        from langchain_core.prompts import ChatPromptTemplate
+        
+        print("\n[TEST] Testing Async Implementation")
+        
+        # Initialize Azure OpenAI with LangChain
+        llm = AzureChatOpenAI(
+            azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
+            api_key=os.getenv("AZURE_OPENAI_API_KEY"),
+            api_version="2024-08-01-preview",  # Required for structured output
+            azure_deployment=os.getenv("AZURE_OPENAI_DEPLOYMENT", "gpt-4o"),
+            temperature=0,
+        )
+        
+        chemical_llm = llm.with_structured_output(ChemicalInfo)
+        
+        chemical_prompt = ChatPromptTemplate.from_messages([
+            ("system", "You are a chemical expert. Extract information about the given chemical."),
+            ("user", "Analyze this substance: {substance}")
+        ])
+        
+        chemical_chain = chemical_prompt | chemical_llm
+        
+        # Async invocation
+        result = await chemical_chain.ainvoke({"substance": "Water (H2O)"})
+        print(f"Async Result: {result}")
+        assert isinstance(result, ChemicalInfo)
+        
+        print("[OK] Async test passed!")
+        return True
+        
+    except Exception as e:
+        print(f"[ERROR] Async Error: {e}")
+        import traceback
+        traceback.print_exc()
+        return False
+
+def compare_with_current_implementation():
+    """Compare with current OpenAI implementation"""
+    print("\n=== Comparing with current implementation ===")
+    
+    try:
+        from openai import AzureOpenAI
+        import json
+        
+        # Current implementation
+        client = AzureOpenAI(
+            api_key=os.getenv("AZURE_OPENAI_API_KEY"),
+            api_version="2024-08-01-preview",  # Required for structured output
+            azure_endpoint=os.getenv("AZURE_OPENAI_ENDPOINT"),
+        )
+        
+        messages = [
+            {
+                "role": "system",
+                "content": "Extract chemical information. Return JSON with fields: chemical_name, cas_number, is_chemical, synonyms"
+            },
+            {
+                "role": "user", 
+                "content": "Analyze: Sodium Chloride"
+            }
+        ]
+        
+        response = client.chat.completions.create(
+            model=os.getenv("AZURE_OPENAI_DEPLOYMENT", "gpt-4o"),
+            messages=messages,
+            temperature=0,
+            response_format={"type": "json_object"}
+        )
+        
+        current_result = response.choices[0].message.content
+        print(f"Current implementation result: {current_result}")
+        
+        # Parse and validate
+        parsed = json.loads(current_result)
+        print(f"Parsed successfully: {parsed}")
+        
+        print("[OK] Current implementation works but requires manual parsing")
+        
+    except Exception as e:
+        print(f"Current implementation error: {e}")
+
+if __name__ == "__main__":
+    print("=== Testing LangChain Azure OpenAI with Structured Output ===\n")
+    
+    # Check environment variables
+    required_vars = ["AZURE_OPENAI_API_KEY", "AZURE_OPENAI_ENDPOINT"]
+    missing_vars = [var for var in required_vars if not os.getenv(var)]
+    
+    if missing_vars:
+        print(f"[ERROR] Missing environment variables: {missing_vars}")
+        print("Please ensure .env file contains all required Azure OpenAI credentials")
+        exit(1)
+    
+    print("[OK] Environment variables loaded successfully")
+    
+    # Run tests
+    sync_success = test_langchain_azure_openai()
+    
+    if sync_success:
+        # Run async test
+        asyncio.run(test_async_langchain())
+        
+        # Compare with current implementation
+        compare_with_current_implementation()
+        
+        print("\n[OK] All tests completed! LangChain migration is feasible.")
+    else:
+        print("\n[ERROR] Tests failed. Please check the errors above.")
\ No newline at end of file
