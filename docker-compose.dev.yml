services:
  redis:
    image: redis:6
    container_name: ml-model-redis
    ports:
      - "6379:6379"
    volumes:
      - ./scripts/config/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - ml-models-network

  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ml-models-app
    ports:
      - "5001:5001"
    env_file:
      - .env
    environment:
      - REDIS_URL=redis://ml-model-redis:6379
      # Disable debugging to prevent waiting for debugger
      - DEBUG=false
    volumes:
      # Mount the local codebase to the container's working directory
      - ./:/home/<USER>/app
      # Exclude the virtual environment directory if you have one locally
      - /home/<USER>/app/venv
      # Exclude any other directories that shouldn't be mounted
      - /home/<USER>/app/__pycache__
      # Add a bind mount for NLTK data
      - ./nltk_data:/home/<USER>/nltk_data
      # Add a bind mount for Hugging Face cache
      - ./hf_cache:/home/<USER>/.cache/huggingface
      # Add a bind mount for ChromaDB persistent storage
      - ./chroma_data:/tmp/chroma_documents
      - ./chroma_data_eol:/tmp/chroma_documents_eol
      # Add a bind mount for disk cache
      - ./disk_cache:/home/<USER>/.cache/diskcache
      # Add a bind mount for PyTorch cache
      - ./torch_cache:/home/<USER>/.cache/torch
    user: "${UID:-1000}:${GID:-1000}"
    depends_on:
      - redis
    dns:
      - 8.8.8.8
      - 8.8.4.4
    networks:
      - ml-models-network

networks:
  ml-models-network:
    driver: bridge
