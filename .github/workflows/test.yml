name: Test

on: pull_request

env:
  AZURE_OPENAI_API_KEY: ${{ secrets.AZURE_OPENAI_API_KEY }}
  AZURE_OPENAI_ENDPOINT: ${{ vars.AZURE_OPENAI_ENDPOINT }}
  AZURE_OPENAI_DEPLOYMENT: ${{ vars.AZURE_OPENAI_DEPLOYMENT }}
  AZURE_API_VERSION: "2025-01-01-preview"
  HF_TOKEN: ${{ secrets.HF_TOKEN }}
  MAPBOX_ACCESS_TOKEN: ${{ secrets.MAPBOX_ACCESS_TOKEN }}
  AZURE_PHI_3_KEY: ${{ secrets.AZURE_PHI_3_KEY }}
  AZURE_PHI_3_ENDPOINT: ${{ vars.AZURE_PHI_3_ENDPOINT }}
  REDIS_URL: "redis://localhost:6379"
  DEBUG: "false"

jobs:
  test:
    runs-on: ubuntu-22.04

    steps:
      - uses: actions/checkout@v4

      - name: Install system dependencies
        run: ./dependencies/linux/setup.sh

      - name: Setup Redis Docker Container
        run: ./scripts/setup_local_redis.sh

      - name: Set up Python
        uses: actions/setup-python@v5.1.1
        with:
          python-version: "3.10"
          cache: "pip"

      - name: Install python dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r ./dependencies/linux/requirements.txt

      - name: Run utils tests
        run: python -m unittest discover ./utils/tests

      #- name: Run category prediction tests
      #  run: python -m unittest discover ./product_category_prediction/tests

      - name: Run Emissions Factor Matching API Tests
        run: python -m unittest ./emissions_factor_matching/tests/test_api.py

      - name: Run Chemical Prediction Tests
        run: python -m unittest discover ./chemical_prediction/tests

      - name: Run File Extraction Tests
        run: python -m unittest discover ./file_extraction/tests
