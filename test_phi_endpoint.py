import asyncio
import os
from azure.ai.inference.aio import ChatCompletionsClient
from azure.core.credentials import AzureKeyCredential
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

async def test_phi_endpoint(endpoint, key, test_name):
    """Test a Phi-3 endpoint with a simple request."""
    print(f"\n--- Testing {test_name} ---")
    print(f"Endpoint: {endpoint}")
    print(f"Key: {key[:5]}...{key[-5:] if len(key) > 10 else key}")
    
    try:
        # Create the client
        client = ChatCompletionsClient(
            endpoint=endpoint,
            credential=AzureKeyCredential(key)
        )
        
        # Set up a simple request
        messages = [
            {
                "role": "system",
                "content": "You are a helpful assistant."
            },
            {
                "role": "user",
                "content": "Hello, can you respond with a simple 'Hello, I am working!' message?"
            }
        ]
        
        # Set a timeout for the request
        print("Sending request...")
        response = await asyncio.wait_for(
            client.complete(
                body={
                    "messages": messages,
                    "max_tokens": 100,
                    "temperature": 0,
                }
            ),
            timeout=10.0  # 10 second timeout
        )
        
        # Print the response
        completion = response["choices"][0]["message"]["content"]
        print(f"Response: {completion}")
        print("Test successful!")
        return True
        
    except asyncio.TimeoutError:
        print("Request timed out after 10 seconds")
        return False
    except Exception as e:
        print(f"Error: {str(e)}")
        return False
    finally:
        # Close the client
        if 'client' in locals():
            await client.close()

async def main():
    # Get the old and new credentials from environment variables or hardcode them
    # Old credentials
    old_endpoint = input("Enter old PHI-3 endpoint: ")
    old_key = input("Enter old PHI-3 key: ")
    
    # New credentials
    new_endpoint = input("Enter new PHI-3 endpoint: ")
    new_key = input("Enter new PHI-3 key: ")
    
    # Test the old endpoint
    old_result = await test_phi_endpoint(old_endpoint, old_key, "OLD ENDPOINT")
    
    # Test the new endpoint
    new_result = await test_phi_endpoint(new_endpoint, new_key, "NEW ENDPOINT")
    
    # Summary
    print("\n--- Summary ---")
    print(f"Old endpoint test: {'PASSED' if old_result else 'FAILED'}")
    print(f"New endpoint test: {'PASSED' if new_result else 'FAILED'}")

if __name__ == "__main__":
    asyncio.run(main())
