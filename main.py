import uuid
import uvicorn
from fastapi import FastAP<PERSON>, Request, HTTPException, Response
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from huggingface_hub import login
from config import config
from utils import logger
import debugpy
from starlette.middleware.base import BaseHTTPMiddleware
import time

login(token=config.hf_token)

import nltk
nltk.download('punkt')
nltk.download('punkt_tab')
nltk.download('averaged_perceptron_tagger')
nltk.download('averaged_perceptron_tagger_eng')

import sentry_sdk
from sentry_sdk.integrations.fastapi import FastApiIntegration
from sentry_sdk.integrations.starlette import StarletteIntegration
from sentry_sdk.integrations.openai import OpenAIIntegration
from package_material_classifier_simplified.api import model_api as package_material_classifier_api
from chemical_prediction.api import model_api as chemical_prediction_api
from emissions_factor_matching.api import model_api as emissions_factor_matching_api
from product_category_prediction.api import model_api as product_category_prediction_api
from product_manufacturing.api import model_api as product_manufacturing_api
from file_extraction.api import model_api as file_extraction_api

# Only perform SQLite version check if ChromaDB is needed
if config.chroma_needed:
    import sqlite3
    if sqlite3.sqlite_version_info < (3, 47, 2):
        __import__('pysqlite3')
        import sys
        sys.modules['sqlite3'] = sys.modules.pop('pysqlite3')

if config.sentry_dsn:
    sentry_sdk.init(
        dsn=config.sentry_dsn,
        send_default_pii=True,
        traces_sample_rate=1.0,
        profiles_sample_rate=1.0,
        integrations=[
            FastApiIntegration(),
            StarletteIntegration(),
            OpenAIIntegration()
        ]
    )

app = FastAPI()

class LoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        response = await call_next(request)
        process_time = (time.time() - start_time) * 1000

        # Get response body
        response_body = b""
        async for chunk in response.body_iterator:
            response_body += chunk

        # Create a new response with the same content
        new_response = Response(
            content=response_body,
            status_code=response.status_code,
            headers=dict(response.headers),
            media_type=response.media_type
        )

        # Log the response info including body
        try:
            body_str = response_body.decode()
        except UnicodeDecodeError:
            body_str = "<binary data>"

        logger.info(
            f"Path: {request.url.path} | "
            f"Method: {request.method} | "
            f"Status: {response.status_code} | "
            f"Duration: {process_time:.2f}ms | "
            f"Response: {body_str}"
        )

        return new_response

app.add_middleware(LoggingMiddleware)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.mount(
    '/api/package-material-classifier-simplified',
    package_material_classifier_api,
)

app.mount(
    '/api/chemical-prediction',
    chemical_prediction_api,
)

app.mount(
    '/api/emissions-factor-matching',
    emissions_factor_matching_api,
)

app.mount(
    '/api/product-category-prediction',
    product_category_prediction_api,
)

app.mount(
    '/api/product-manufacturing',
    product_manufacturing_api,
)

app.mount(
    '/api/file-extraction',
    file_extraction_api,
)

@app.exception_handler(HTTPException)
async def global_http_exception_handler(request: Request, exc: HTTPException):
    # Use a request ID to correlate logs and errors
    request_id = str(uuid.uuid4())
    request_info = await _get_request_extra_info(request, request_id)

    # Log details internally
    logger.exception(
        f"Error occurred for URL: {request.url}\n"
        f"Exception: {exc.detail}\n"
        f"Request: {request_info}\n"
    )

    # Return a concise, safe message to the client:
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "detail": str(exc.detail),
            "error": f"Internal server error: {exc}",
            "request_id": request_id
        }
    )


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    # Use a request ID to correlate logs and errors
    request_id = str(uuid.uuid4())
    request_info = await _get_request_extra_info(request, request_id)

    # Log details internally
    logger.exception(
        f"Error occurred for URL: {request.url}\n"
        f"Exception: {exc}\n"
        f"Request: {request_info}\n"
    )

    # Return a concise, safe message to the client:
    return JSONResponse(
        status_code=500,
        content={
            "detail": str(exc),
            "error": f"Internal server error: {exc}",
            "request_id": request_id
        }
    )

async def _get_request_extra_info(request: Request, request_id: str) -> dict:
    """
    Safely extracts and formats request information for logging purposes.

    Args:
        request (Request): The FastAPI request object
        request_id (str): Unique identifier for the request

    Returns:
        dict: Formatted request information including method, URL, headers, body, etc.
    """
    return {
        "request_id": request_id,
        "url": str(request.url),
        "method": request.method,
        "headers": dict(request.headers),
        "query_params": dict(request.query_params),
        "client_host": request.client.host if request.client else None,
        "path_params": dict(request.path_params),
    }

if __name__ == "__main__":
    if config.debug:
        # Enable remote debugging
        debugpy.listen(("0.0.0.0", 5678))
        logger.info("Remote debugging enabled on port 5678")
        # Wait for debugger to attach
        debugpy.wait_for_client()
        logger.info("Debugger attached")

    uvicorn.run("main:app", host="0.0.0.0", port=config.port, workers=config.workers)
