from pydantic import BaseModel


class Location(BaseModel):
    city: str | None = None
    country: str | None = None


class Node(BaseModel):
    name: str
    location: Location | None = None
    node_type: str
    scrap_rate: float | None = None
    scrap_fate: str | None = None
    amount: float | None = None
    unit: str | None = None
    component_name: str | None = None
    description: str | None = None
    quantity: float | None = None
    node_id: int | None = None
    row_num: int | None = None


class Edge(BaseModel):
    from_node_id: int
    to_node_id: int


class Component(BaseModel):
    component_name: str
    component_id: str
    nodes: list[Node]
    edges: list[Edge]
