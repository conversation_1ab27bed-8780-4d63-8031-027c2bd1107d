from typing import Dict, List, Tuple, Any, Callable, Optional, Set
from enum import Enum
import re
import requests
import pandas as pd
from utils.dataframe import get_dataframe
from file_extraction.models import Node, Edge, Location, Component
from config import config
from utils import logger
import json
from utils.cache import create_cache, cached
import asyncio
import sys
from dataclasses import dataclass
from decimal import Decimal

# Each set in the list represents alternative/alias columns where any one is required
COMPONENT_REQUIRED_COLUMNS = [
    {"Level"},
    {"Part Name"},
    {"Description"},
    {"Material Type", "Material Family"},
    {"Unit Weight", "Weight"},
    {"Quantity"},
    {"Scrap Rate"},
    {"Scrap Fate"},
]

PACKAGING_REQUIRED_COLUMNS = [
    {"Material"},
    {"Weight (grams)"},
    {"QTY"},
    {"Part No."},
    {"Packing Component"}
]

# Required columns for parts files
PARTS_REQUIRED_COLUMNS = [
    {"item"},
    {"Qty", "matl_qty"},
    {"Vendor Name", "VendorName"},
    {"Rank"}
]

def validate_columns(df: pd.DataFrame, required_column_groups: List[Set[str]], warnings: List[str]) -> None:
    """
    Validate that the DataFrame has the required columns.
    Each set in required_column_groups represents alternative columns where any one must be present.

    Args:
        df: DataFrame to validate
        required_column_groups: List of sets, where each set contains alternative column names
        warnings: List to collect warning messages

    Raises:
        ValueError: If any required column group is missing
    """
    df_columns = set(df.columns)
    missing_groups = []

    for alternatives in required_column_groups:
        if not any(col in df_columns for col in alternatives):
            # Take first column name as the canonical name for the error message
            missing_groups.append(next(iter(alternatives)))

    if missing_groups:
        error_msg = f"Missing columns: {', '.join(missing_groups)}"
        error_and_collect(error_msg, warnings)
        raise ValueError(error_msg)

@dataclass
class ProcessingResult:
    """Result of processing components including any warnings."""
    components: List[Component]
    warnings: List[str]

def warn_and_collect(warning_msg: str, warnings: List[str]):
    """
    Log a warning message and append it to the warnings list.
    This centralizes the pattern of logging and collecting warnings.

    Args:
        warning_msg: The warning message to log and collect
        warnings: The list to append the warning message to
    """
    logger.warning(warning_msg)
    warnings.append(warning_msg)

def error_and_collect(error_msg: str, warnings: List[str]):
    """
    Log an error message and append it to the warnings list.
    This centralizes the pattern of logging and collecting errors.

    Args:
        error_msg: The error message to log and collect
        warnings: The list to append the error message to
    """
    logger.exception(error_msg)
    warnings.append(error_msg)

def get_row_num(row: pd.Series) -> int:
    """+1 for 0-based index, and +1 for the header row."""
    return row.name + 2

# Create a cache instance for location lookups
location_cache = create_cache(prefix="location")

COMPONENT_PATTERNS = [
    r'^1000\d{6}[A-Z]{2}\d{2}$',
    r'^38\d{7,8}[A-Z]{2}\d{2}(\.INT)?$',
    r'^966\d{2}[A-Z]{1}\d{3}[A-Z]{2}\d{2}$',
    r'^[3,5,8]\d{9}$',
    r'^20\d{8}$',
    r'^96602Q458.$',
    r'^8509010007$',
    r'^966\d{2}[A-Z]{2}\d{2}[A-Z]{2}\d{2}$',
    r'^CB\d{6}$',
    r'^95\d{8}$',
    r'^GT-NPD-HT-\d{4}-001-[A-Z]\d{2}$',
    r'^OSP-38\d{8}[A-Z]{2}\d{2}$',
    r'^96\d{3}[A-Z]{1,2}\d{2,3}\.?$',
    r'^966\d{2}[A-Z]\d[A-Z]\d[A-Z]{2}\d{2}$',
    r'^F[A-Z]{4,5}\d{2}-[A-Z]$',
    r'^HSFTAG\d{4}$',
    r'^U\d{4}$',
    r'^FSBSQ\d{3}-[A-Z]$',
    r'^24\d{8}[A-Z]{2,3}\d{2}$',
]

def _is_component(value: str) -> bool:
    if not isinstance(value, str) or pd.isna(value):
        return False

    return any(re.match(pattern, str(value)) for pattern in COMPONENT_PATTERNS)

def _format_component_id(part_number: str, supplier: str | None = None) -> str:
    """
    Format a component ID by combining part no. with supplier name.
    """
    supplier_str = str(supplier).strip() if supplier is not None else ""

    if len(supplier_str):
        return f"{part_number} ({supplier_str})"

    return part_number

class NodeTypes(Enum):
    MATERIAL = "material"
    PACKAGING = "packaging"
    PRODUCTION = "production"
    TRANSPORTATION = "transportation"
    BUNDLE = "bundle"

@dataclass
class NodeConstraints:
    """Constraints for a node type defining input and output requirements."""
    min_inputs: int = 0
    max_inputs: Optional[int] = None
    min_outputs: int = 0
    max_outputs: Optional[int] = None

# Define constraints for each node type
NODE_TYPE_CONSTRAINTS = {
    NodeTypes.PRODUCTION.value: NodeConstraints(min_inputs=1),
    NodeTypes.TRANSPORTATION.value: NodeConstraints(min_inputs=1, max_inputs=1, min_outputs=1, max_outputs=1),
    NodeTypes.MATERIAL.value: NodeConstraints(max_inputs=0, min_outputs=1),
    NodeTypes.PACKAGING.value: NodeConstraints(max_inputs=0, min_outputs=1),
    NodeTypes.BUNDLE.value: NodeConstraints(),  # No specific constraints
}

async def get_location_with_default(row_: pd.Series) -> Location:
    location = await get_location(row_)
    if not location.city or not location.country:
        return Location(city="Timbuktu", country="Mali")
    return location

@cached(location_cache, result_factory=Location)
async def get_location_mapbox(source: str) -> Location:
    """
    Get location details from Mapbox API for a given source string.
    Returns a Location object with city and country information.
    Uses caching to avoid redundant API calls for the same location.
    """
    if not source:
        return Location()

    url = f"https://api.mapbox.com/geocoding/v5/mapbox.places/{source}.json"
    params = {"access_token": config.mapbox_access_token, "limit": 1}
    resp = requests.get(url, params=params)
    data = resp.json()
    if not data.get("features"):
        return Location(city=None, country=None)

    features = data.get("features", [])
    city = country = None
    if (
        "country" in features[0].get("place_type", [])
        and len(features[0].get("place_type", [])) == 1
    ):
        country = features[0].get("text", "")
    else:
        context = features[0].get("context", [])
        for c in context:
            if "place" in c.get("id", "") or "locality" in c.get("id", ""):
                city = c.get("text", "")
            elif "country" in c.get("id", ""):
                country = c.get("text", "")

        if not city:
            city = data["features"][0].get("text", "")
        if not country:
            parts = data["features"][0].get("place_name", "").split(", ")
            country = parts[-1] if len(parts) > 1 else ""

    return Location(city=city, country=country)

async def get_location(row_: pd.Series, location_column_names: List[str] = ["Plant", "Source Location"]) -> Location:
    """
    Get location from a row. Returns a default location if no location is found.
    """
    source = None
    for column_name in location_column_names:
        if column_name in row_ and not pd.isna(row_.get(column_name)):
            source = row_.get(column_name)
            break
    if not source or pd.isna(source):
        return Location(city=None, country=None)

    return await get_location_mapbox(source)

def _get_recycled_content_percentage(row: pd.Series, pre_consumer_column_name: str, post_consumer_column_name: str) -> float:
    pre_recycled_content_percentage = 0
    post_recycled_content_percentage = 0

    if pd.notna(row.get(pre_consumer_column_name)):
        try:
            pre_recycled_content_percentage = float(
                row[pre_consumer_column_name].split(" - ")[-1][:-1]
            )
        except Exception:
            pre_recycled_content_percentage = 0

    if pd.notna(row.get(post_consumer_column_name)):
        try:
            post_recycled_content_percentage = float(
                row[post_consumer_column_name].split(" - ")[-1][:-1]
            )
        except Exception:
            post_recycled_content_percentage = 0

    # Ensure the total recycled content percentage is not greater than 100
    total_recycled_content_percentage = (
        pre_recycled_content_percentage + post_recycled_content_percentage
    )
    if total_recycled_content_percentage > 100:
        logger.warning(f"Total recycled content percentage is greater than 100 for component {row['Part Name']} at row {get_row_num(row)}")
        return 100

    return total_recycled_content_percentage

def get_next_node_id():
    """Generate sequential node IDs"""
    get_next_node_id.counter += 1
    return get_next_node_id.counter

# Initialize the counter
get_next_node_id.counter = 0

def extract_weight(weight_str: str) -> float:
    # Handle case when weight is already a float or int
    if isinstance(weight_str, (float, int)):
        return float(weight_str)

    # weight can be in format "100g" or "100 - 200g"
    # Extract the numeric part, preserving exact decimal places
    weight_part = weight_str.split(" - ")[-1].rstrip("g").strip()

    # Use Decimal for exact decimal arithmetic
    try:
        # Convert to Decimal first to preserve exact decimal places
        return float(Decimal(weight_part))
    except:
        # Fallback to regular float if Decimal conversion fails
        return float(weight_part)

def has_non_empty_value(value):
    """Return True if the given value is not NA and not an empty string."""
    return pd.notna(value) and value != ""

async def _get_material_nodes(
    row_: pd.Series,
    node_name: str,
    is_component_row: bool,
    predecessor_material_quantity: float,
    warnings: List[str]
) -> List[Node]:
    weight_col_names = ["Unit Weight", "Weight"]
    unit_col_name = "Unit of Measure"
    unit = row_[unit_col_name] if pd.notna(row_[unit_col_name]) else "g"

    weight = 0
    for weight_col_name in weight_col_names:
        if has_non_empty_value(row_[weight_col_name]):
            weight = extract_weight(row_[weight_col_name])
            break

    # If weight is 0 or negative, skip the node
    if weight <= 0:
        warning_msg = f"Weight ({weight}) is 0 or negative for component {row_['Part Name']} material {node_name} at row {get_row_num(row_)}"
        warn_and_collect(warning_msg, warnings)
        return [], []
    # Ignore quantity for top level component rows, as they would get the quantity
    # from the parts list file
    if is_component_row:
        quantity = 1
    elif has_non_empty_value(row_["Quantity"]):
        quantity = float(row_["Quantity"])
    else:
        quantity = predecessor_material_quantity

    if quantity == 0:
       quantity = 1

    # Get recycled content percentage
    recycled_content_percentage = _get_recycled_content_percentage(
        row=row_,
        pre_consumer_column_name="% Pre-Consumer Recycled Content",
        post_consumer_column_name="% Post-Consumer Recycled Content"
    )

    location = await get_location(row_, location_column_names=["Source Location"])

    # Common node parameters
    common_node_params = {
        "unit": unit,
        "component_name": row_["Part Name"] if pd.notna(row_["Part Name"]) else None,
        "description": row_["Description"] if pd.notna(row_["Description"]) else None,
        "quantity": quantity,
        "location": location,
        "row_num": get_row_num(row_)  # Add row_num to common parameters
    }

    nodes = []
    edges = []

    # Create material nodes
    if recycled_content_percentage:
        nodes.append(Node(
            name=node_name + " (recycled)",
            node_type=NodeTypes.MATERIAL.value,
            amount=weight * recycled_content_percentage / 100,
            node_id=get_next_node_id(),
            **common_node_params
        ))

        if recycled_content_percentage < 100:
            nodes.append(Node(
                name=node_name + " (non-recycled)",
                node_type=NodeTypes.MATERIAL.value,
                amount=weight * (1 - recycled_content_percentage / 100),
                node_id=get_next_node_id(),
                **common_node_params
            ))
    else:
        nodes.append(Node(
            name=node_name,
            node_type=NodeTypes.MATERIAL.value,
            amount=weight,
            node_id=get_next_node_id(),
            **common_node_params
        ))

    # Add bundle node only if there are multiple material nodes
    if len(nodes) > 1:
        bundle_node = Node(
            name=node_name + " bundle",
            node_type=NodeTypes.BUNDLE.value,
            node_id=get_next_node_id(),
            location=location,
            row_num=get_row_num(row_)  # Add row_num to bundle node
        )
        nodes.append(bundle_node)

        # Connect all material nodes to bundle
        for material_node in nodes[:-1]:  # Exclude the bundle node itself
            edges.append(Edge(
                from_node_id=material_node.node_id,
                to_node_id=bundle_node.node_id
            ))

    return nodes, edges

def get_parts_from_file(file_path: str, content_type: str) -> List[Dict[str, Any]]:
    df = get_dataframe(file_path, content_type)
    warnings = []

    # Validate required columns for parts file
    try:
        validate_columns(df, PARTS_REQUIRED_COLUMNS, warnings)
    except ValueError as e:
        error_msg = f"Invalid Product Parts List file format - {str(e)}"
        logger.error(error_msg)
        raise ValueError(error_msg)

    # log all "item" values
    logger.info(f"All items in DataFrame:\n{df['item'].to_string()}")

    # Get skip parts from config and split into list
    skip_parts = config.skip_parts.split(",") if config.skip_parts else []
    logger.info(f"Skipping parts: {skip_parts}")

    # Determine which column names to use
    vendor_col = "Vendor Name" if "Vendor Name" in df.columns else "VendorName"
    qty_col = "Qty" if "Qty" in df.columns else "matl_qty"

    logger.info(f"Using vendor column: {vendor_col}, quantity column: {qty_col}")

    items = (
        df[["item", qty_col, vendor_col, "Rank"]]
        .dropna(subset=["item"])  # Only drop NA for required columns
        .query("Rank == 1 or Rank == '' or Rank.isna()")  # Accept rank 1, empty string, or null
        .drop_duplicates(subset=["item", vendor_col])
        .to_dict(orient="records")
    )

    components = [
        {
            "component_id": _format_component_id(item['item'], item.get(vendor_col, '')),
            "quantity": 1 if float(item[qty_col]) < 1 else item[qty_col]  # Handle both int and float zeros
        }
        for item in items
        if item['item'] not in skip_parts  # Skip parts in the config list
    ]

    logger.info(f"All components after filtering:\n{json.dumps(components, indent=2)}")
    return components

def _is_material_node(row: pd.Series) -> bool:
    return (
        (_is_not_empty(row, "Material Type") or
        _is_not_empty(row, "Material Family")) and
        (_is_not_empty(row, "Unit Weight") or
        _is_not_empty(row, "Weight"))
    )

def _is_manufacturing_node(row: pd.Series) -> bool:
    return (
        _is_not_empty(row, "MANUFACTURING PROCESS") or
        _is_not_empty(row, "MANUFACTURING PROCESS 2") or
        _is_not_empty(row, "MANUFACTURING PROCESS 3")
    )
def _is_not_empty(row: pd.Series, column_name: str) -> bool:
    return has_non_empty_value(row[column_name]) and row[column_name] != "-"

async def _extract_material_nodes_and_edges(row: pd.Series,
                                           is_component_row: bool,
                                           predecessor_material_quantity: float,
                                           warnings: List[str]) \
                                            -> Tuple[List[Node], List[Edge]]:
    """
    Extract material and transportation nodes and edges from a row.
    Returns a tuple of (nodes, edges) where nodes are material and transportation nodes
    and edges connect them in sequence.
    """
    nodes = []
    edges = []

    material_family = row["Material Family"] if has_non_empty_value(row["Material Family"]) else ""
    material_type = row["Material Type"] if has_non_empty_value(row["Material Type"]) else ""
    if material_type:
        node_name = f"{material_type} ({material_family})" if material_family else material_type
    else:
        node_name = material_family

    # Create material nodes
    material_nodes, material_edges = await _get_material_nodes(
            row,
            node_name,
            is_component_row,
            predecessor_material_quantity,
            warnings
    )
    nodes.extend(material_nodes)
    edges.extend(material_edges)

    # Get default transportation location
    transportation_location = await get_location_with_default(row)

    # Create transportation node
    transport_node_id = get_next_node_id()
    transport_node = Node(
        name=node_name + " transportation",
        node_type=NodeTypes.TRANSPORTATION.value,
        location=transportation_location,
        node_id=transport_node_id,
        row_num=get_row_num(row)  # Add row_num to transport node
    )
    nodes.append(transport_node)

    # connect last material node to transportation node
    edges.append(Edge(
        from_node_id=material_nodes[-1].node_id,
        to_node_id=transport_node_id
    ))

    return nodes, edges

def _find_clean_column_name(df: pd.DataFrame, target_name: str) -> str:
    """Find a column in the DataFrame that matches the target name after cleaning whitespace/newlines."""
    # Helper function to clean column names
    def clean_col_name(name):
        return "".join(str(name).split()) if pd.notna(name) else None

    cleaned_target = clean_col_name(target_name)
    if not cleaned_target:
        raise ValueError(f"Target column name '{target_name}' is invalid after cleaning.")

    for col in df.columns:
        cleaned_col = clean_col_name(col)
        if cleaned_col == cleaned_target:
            return col  # Return the original column name

    # If no match found after checking all columns
    raise ValueError(f"Could not find a column matching '{target_name}' in the DataFrame after cleaning.")

def _validate_component(component: Component):
    """Validate a component's node-edge structure and manufacturing node inputs.

    Args:
        component: The Component object to validate

    Raises:
        ValueError: If any validation fails
    """
    node_ids = {node.node_id for node in component.nodes}
    edge_node_ids = set()

    # Helper function to get row info string
    def get_row_info(node_id=None):
        if node_id is not None:
            # Find the specific node with this ID
            for node in component.nodes:
                if node.node_id == node_id and node.row_num is not None:
                    return f" at row {node.row_num}"
            return ""

        # Get all row numbers for the component
        rows = sorted([node.row_num for node in component.nodes if node.row_num is not None])
        if rows:
            if len(rows) == 1:
                return f" at row {rows[0]}"
            return f" defined between rows {rows[0]} and {rows[-1]}"
        return ""

    # Check edge validity - all referenced nodes must exist
    for edge in component.edges:
        if edge.from_node_id not in node_ids:
            raise ValueError(
                f"Edge 'from_node_id' {edge.from_node_id} not found in nodes for "
                f"component '{component.component_name}'{get_row_info()} (ID: {component.component_id})"
            )
        if edge.to_node_id not in node_ids:
            raise ValueError(
                f"Edge 'to_node_id' {edge.to_node_id} not found in nodes for "
                f"component '{component.component_name}'{get_row_info()} (ID: {component.component_id})"
            )
        edge_node_ids.add(edge.from_node_id)
        edge_node_ids.add(edge.to_node_id)

    # Check node connectivity (only if more than one node)
    if len(component.nodes) > 1:
        for node in component.nodes:
            if node.node_id not in edge_node_ids:
                row_info = f" at row {node.row_num}" if node.row_num is not None else ""
                raise ValueError(
                    f"Node {node.name}{row_info} (ID: {node.node_id}) not found in any edge for "
                    f"component '{component.component_name}'{get_row_info()} (ID: {component.component_id}) "
                    f"which has multiple nodes."
                )

    # Initialize dictionaries for tracking node inputs/outputs
    node_inputs = {node.node_id: 0 for node in component.nodes}
    node_outputs = {node.node_id: 0 for node in component.nodes}

    # Count inputs and outputs for each node
    for edge in component.edges:
        node_outputs[edge.from_node_id] += 1
        node_inputs[edge.to_node_id] += 1

    # Find nodes without successors (no outgoing edges)
    nodes_without_successors = [
        node for node in component.nodes
        if node_outputs[node.node_id] == 0
    ]

    # Validate there is exactly one node without successors and it's a bundle.
    # UI expects this while tying together components to a product
    if len(nodes_without_successors) == 0:
        raise ValueError(
            f"Component '{component.component_name}'{get_row_info()} (ID: {component.component_id}) "
            f"has no terminal node (node without successors)"
        )
    elif len(nodes_without_successors) > 1:
        node_details = ", ".join(
            f"{node.name} (ID: {node.node_id}){get_row_info(node.node_id)}"
            for node in nodes_without_successors
        )
        raise ValueError(
            f"Component '{component.component_name}'{get_row_info()} (ID: {component.component_id}) "
            f"has multiple terminal nodes: {node_details}"
        )

    terminal_node = nodes_without_successors[0]
    if terminal_node.node_type != NodeTypes.BUNDLE.value:
        raise ValueError(
            f"Terminal node {terminal_node.name} (ID: {terminal_node.node_id}){get_row_info(terminal_node.node_id)} "
            f"must be of type 'bundle' but is of type '{terminal_node.node_type}' in component "
            f"'{component.component_name}'{get_row_info()} (ID: {component.component_id})"
        )

    # Validate nodes against constraints
    for node in component.nodes:
        node_type = node.node_type
        if node_type not in NODE_TYPE_CONSTRAINTS:
            continue  # Skip unknown node types

        constraints = NODE_TYPE_CONSTRAINTS[node_type]
        row_info = f" at row {node.row_num}" if node.row_num is not None else ""

        # Check inputs
        if node_inputs[node.node_id] < constraints.min_inputs:
            raise ValueError(
                f"Node '{node.name}'{row_info} of type '{node_type}' has {node_inputs[node.node_id]} "
                f"inputs, minimum required: {constraints.min_inputs} in component "
                f"'{component.component_name}'{get_row_info()} (ID: {component.component_id})"
            )
        if constraints.max_inputs is not None and node_inputs[node.node_id] > constraints.max_inputs:
            raise ValueError(
                f"Node '{node.name}'{row_info} of type '{node_type}' has {node_inputs[node.node_id]} "
                f"inputs, maximum allowed: {constraints.max_inputs} in component "
                f"'{component.component_name}'{get_row_info()} (ID: {component.component_id})"
            )

        # Check outputs
        if node_outputs[node.node_id] < constraints.min_outputs:
            raise ValueError(
                f"Node '{node.name}'{row_info} of type '{node_type}' has {node_outputs[node.node_id]} "
                f"outputs, minimum required: {constraints.min_outputs} in component "
                f"'{component.component_name}'{get_row_info()} (ID: {component.component_id})"
            )
        if constraints.max_outputs is not None and node_outputs[node.node_id] > constraints.max_outputs:
            raise ValueError(
                f"Node '{node.name}'{row_info} of type '{node_type}' has {node_outputs[node.node_id]} "
                f"outputs, maximum allowed: {constraints.max_outputs} in component "
                f"'{component.component_name}'{get_row_info()} (ID: {component.component_id})"
            )

def _create_component_from_nodes_and_edges(
    component: pd.Series,
    nodes: List[Node],
    edges: List[Edge]
) -> Component:
    """
    Create a Component object from a row and its associated nodes and edges.
    """
    component_name = component["Description"]

    component_id = _format_component_id(component["Part Name"], component.get("Supplier", ""))
    _set_component_metadata(nodes, component_name, component_id)

    new_component = Component(
        component_name=component_name,
        component_id=component_id,
        nodes=nodes,
        edges=edges
    )

    # Validate the new component
    _validate_component(new_component)

    return new_component

async def _get_packaging_components(df) -> ProcessingResult:
    """
    Extract packaging components from a DataFrame.
    Returns a ProcessingResult containing the components and any warnings encountered.
    """
    components = []
    warnings = []

    # Find the actual column names using the helper function
    try:
        actual_pre_consumer_col = _find_clean_column_name(df, "Pre-consumer Recycled Content (%)")
        actual_post_consumer_col = _find_clean_column_name(df, "Post-consumer Recycled Content (%)")
    except ValueError as e:
        error_msg = f"Error finding required packaging columns: {e}"
        error_and_collect(error_msg, warnings)
        return ProcessingResult(components=[], warnings=warnings)

    for _, row in df.iterrows():
        try:
            recycled_content_percentage = _get_recycled_content_percentage(
                row=row,
                pre_consumer_column_name=actual_pre_consumer_col,
                post_consumer_column_name=actual_post_consumer_col,
            )
            source_location = await get_location(row, location_column_names=["Source Location"])

            if source_location is None or not source_location.country:
                warning_msg = f"Location is None for packaging component at row {get_row_num(row)}: {row['Material']}"
                warn_and_collect(warning_msg, warnings)

            if recycled_content_percentage:
                recycled_node_id = get_next_node_id()
                non_recycled_node_id = get_next_node_id()
                combined_node_id = get_next_node_id()

                try:
                    weight = float(row["Weight (grams)"])
                    if weight <= 0:
                        warning_msg = f"Skipping packaging component at row {get_row_num(row)}: {row['Material']} - Weight ({weight}) is zero or negative"
                        warn_and_collect(warning_msg, warnings)
                        continue
                except (ValueError, TypeError) as e:
                    warning_msg = f"Invalid weight format at row {get_row_num(row)}: {row['Material']} - {str(e)}"
                    warn_and_collect(warning_msg, warnings)
                    continue

                nodes = [
                    Node(
                        name=f"{row['Material']} (recycled)",
                        node_type=NodeTypes.PACKAGING.value,
                        amount=weight * recycled_content_percentage / 100,
                        unit="g",
                        quantity=row["QTY"],
                        node_id=recycled_node_id,
                        location=source_location,
                        row_num=get_row_num(row)  # Add row_num to recycled node
                    ),
                    Node(
                        name=f"{row['Material']} (non-recycled)",
                        node_type=NodeTypes.PACKAGING.value,
                        amount=weight * (1 - recycled_content_percentage / 100),
                        unit="g",
                        quantity=row["QTY"],
                        node_id=non_recycled_node_id,
                        location=source_location,
                        row_num=get_row_num(row)  # Add row_num to non-recycled node
                    ),
                    Node(
                        name=f"{row['Material']} combined",
                        node_type=NodeTypes.BUNDLE.value,
                        node_id=combined_node_id,
                        location=source_location,
                        row_num=get_row_num(row)  # Add row_num to combined node
                    ),
                ]

                edges = [
                    Edge(from_node_id=recycled_node_id, to_node_id=combined_node_id),
                    Edge(from_node_id=non_recycled_node_id, to_node_id=combined_node_id),
                ]
            else:
                material_node_id = get_next_node_id()
                try:
                    weight = float(row["Weight (grams)"])
                    if weight <= 0:
                        warning_msg = f"Skipping packaging component at row {get_row_num(row)}: {row['Material']} - Weight ({weight}) is zero or negative"
                        warn_and_collect(warning_msg, warnings)
                        continue
                except (ValueError, TypeError) as e:
                    warning_msg = f"Invalid weight format at row {get_row_num(row)}: {row['Material']} - {str(e)}"
                    warn_and_collect(warning_msg, warnings)
                    continue

                nodes = [
                    Node(
                        name=row["Material"],
                        node_type=NodeTypes.PACKAGING.value,
                        amount=weight,
                        unit="g",
                        quantity=row["QTY"],
                        node_id=material_node_id,
                        location=source_location,
                        row_num=get_row_num(row)  # Add row_num to material node
                    ),
                ]
                edges = []

            # Check if component has packaging nodes
            packaging_nodes = [node for node in nodes if node.node_type == NodeTypes.PACKAGING.value]
            if not packaging_nodes:
                warning_msg = f"Skipping packaging component at row {get_row_num(row)}: {row['Material']} - No packaging nodes found"
                warn_and_collect(warning_msg, warnings)
                continue

            try:
                component_id = _format_component_id(row["Part No."], row.get("Supplier", ""))
                component_name = row["Packing Component"]
                _set_component_metadata(nodes, component_name, component_id)

                new_component = Component(
                    component_id=component_id,
                    component_name=component_name,
                    nodes=nodes,
                    edges=edges
                )

                # Validate the new component
                _validate_component(new_component)
                components.append(new_component)

            except Exception as e:
                error_msg = f"Error creating component at row {get_row_num(row)}: {row['Material']} - {str(e)}"
                error_and_collect(error_msg, warnings)
                continue

        except Exception as e:
            error_msg = f"Error processing row {get_row_num(row)}: {str(e)}"
            error_and_collect(error_msg, warnings)
            continue

    return ProcessingResult(components=components, warnings=warnings)

class RowType(Enum):
    SKIP_ROW = "skip_row"
    END_OF_COMPONENT_START_NEW = "end_of_component_start_new"
    END_OF_COMPONENT_SKIP_ROW = "end_of_component_skip_row"
    START_MATERIAL_NODE = "start_material_node"
    START_COMPONENT_NODE = "start_component_node"
    END_MATERIAL_NODE = "end_material_node"

async def _create_component_bundle(row: pd.Series) -> Tuple[Node, List[Node], List[Edge]]:
    """
    Create a component with its bundle node, nodes and edges.
    Returns a tuple of (bundle_node, nodes, edges)
    """
    location = await get_location(row, location_column_names=["Plant"])
    bundle_node_id = get_next_node_id()
    bundle_node = Node(
        name=row["Description"] + " bundle",
        node_type=NodeTypes.BUNDLE.value,
        location=location,
        node_id=bundle_node_id,
        row_num=get_row_num(row)  # Add row_num to bundle node
    )
    return bundle_node, [bundle_node], []

def _reset_node_id():
    """Reset the node ID counter to -1"""
    get_next_node_id.counter = 0

class NodeLevel:
    """A node in the stack that contains a Node object and its level"""
    def __init__(self, node: Node, level: int):
        self.node = node
        self.level = level


current_component_row = None



async def get_components_from_file(csv_path: str) -> ProcessingResult:
    """
    Extract components from a bill of materials CSV file.
    Uses a stack-based approach to track nodes and their relationships.
    Returns both components and any warnings encountered during processing.
    """
    df = pd.read_csv(csv_path)
    warnings = []

    if "Level" not in df.columns:
        # This might be a packaging file
        try:
            validate_columns(df, PACKAGING_REQUIRED_COLUMNS, warnings)
            result = await _get_packaging_components(df)
            return ProcessingResult(components=result.components, warnings=warnings + result.warnings)
        except ValueError as e:
            error_msg = f"Invalid packaging file format - {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

    # This should be a BOM file
    try:
        validate_columns(df, COMPONENT_REQUIRED_COLUMNS, warnings)
    except ValueError as e:
        error_msg = f"Invalid BOM file format - {str(e)}"
        logger.error(error_msg)
        raise ValueError(error_msg)

    # Add a column to check if the row is a component based on regex using the part name
    df["is_component"] = df["Part Name"].apply(_is_component)
    _reset_node_id()
    components = []
    visited_component_ids = set()

    new_components_in_loop = True
    # Extract components from the file, until no new components are found
    while new_components_in_loop:
        new_components_in_loop = False
        # Stack to track nodes and their levels
        node_stack = []
        node_stack_predecessor_qty = []  # Stack to track (level, quantity) for predecessor quantity
        current_component_row = None
        current_nodes = []
        current_edges = []

        for row_idx, row in df.iterrows():
            level = row["Level"]
            is_comp = row["is_component"]
            part_name = row["Part Name"]

            # Pop from predecessor stack until top has level < current level
            while node_stack_predecessor_qty and node_stack_predecessor_qty[-1][0] >= level:
                node_stack_predecessor_qty.pop()
            # Get predecessor quantity from the top of the stack, default to 0
            if node_stack_predecessor_qty:
                predecessor_material_quantity = node_stack_predecessor_qty[-1][1]
            else:
                predecessor_material_quantity = 0

            # If we hit a level that's same or higher than component level, add this component to the list
            if current_component_row is not None and level <= current_component_row["Level"]:
                if (current_nodes and current_edges
                    and _is_valid_component(part_name, current_nodes, warnings)):
                    components.append(_create_component_from_nodes_and_edges(
                        current_component_row,
                        current_nodes,
                        current_edges
                    ))
                visited_component_ids.add(current_component_row["Part Name"])
                new_components_in_loop = True
                current_component_row = None
                current_nodes = []
                current_edges = []
                node_stack = []
                node_stack_predecessor_qty = []

            # If we encounter a component and it's not visited yet and current_component is None
            # then we are starting a new component
            if is_comp and part_name not in visited_component_ids and current_component_row is None:
                # Start new component, then check if it has manufacturing and material information
                current_component_row = row
                current_component_id = _format_component_id(row["Part Name"], row.get("Supplier", ""))
                bundle_node, current_nodes, current_edges = await _create_component_bundle(row)
                node_stack = [NodeLevel(bundle_node, level)]
            # If we're not in a component, skip
            elif current_component_row is None:
                continue
            else:
                # If level is equal to current top of the stack or higher, pop the stack until
                # the level is lower than the current top of the stack
                while node_stack and node_stack[-1].level >= level:
                    node_stack.pop()

            # if the top of the stack is a material node, continue until we reach
            # the same level as it and its popped out of the stack
            # this is because material nodes should not have an input
            if node_stack and node_stack[-1].node.node_type == NodeTypes.MATERIAL.value:
                continue

            # Process manufacturing nodes if present
            if _is_manufacturing_node(row):
                try:
                    manuf_nodes, manuf_edges = await _extract_manufacturing_nodes_and_edges(row, current_component_id, warnings)

                    # Connect last manufacturing node to bundle node
                    if manuf_nodes and node_stack:
                        last_node = node_stack[-1].node
                        manuf_edges.append(Edge(
                            from_node_id=manuf_nodes[-1].node_id,
                            to_node_id=last_node.node_id
                        ))

                    current_nodes.extend(manuf_nodes)
                    current_edges.extend(manuf_edges)
                    node_stack.append(NodeLevel(manuf_nodes[0], level))

                except Exception as error:
                    error_msg = f"Error extracting manufacturing node at row {get_row_num(row)}: {row['Part Name']} {error}"
                    error_and_collect(error_msg, warnings)

            # Process material nodes if present
            if _is_material_node(row):
                # Material node should be added only if there is a manufacturing node on the stack
                if node_stack and node_stack[-1].node.node_type != NodeTypes.PRODUCTION.value:
                    warning_msg = f"Skipping Material node at row {get_row_num(row)}: {row['Part Name']} as there is no manufacturing node on the stack"
                    warn_and_collect(warning_msg, warnings)
                    continue
                else:
                    try:
                        is_component_row = current_component_row is row
                        material_nodes, material_edges = await _extract_material_nodes_and_edges(
                            row, is_component_row, predecessor_material_quantity, warnings
                        )

                        # Connect material nodes to the appropriate parent node
                        if material_nodes and node_stack:
                            parent_node = node_stack[-1].node
                            material_edges.append(Edge(
                                from_node_id=material_nodes[-1].node_id,
                                to_node_id=parent_node.node_id
                            ))

                        current_nodes.extend(material_nodes)
                        current_edges.extend(material_edges)
                        node_stack.append(NodeLevel(material_nodes[0], level))

                    except Exception as error:
                        error_msg = f"Error extracting material node at row {get_row_num(row)}: {row['Part Name']} {error}"
                        error_and_collect(error_msg, warnings)

            # After processing the row, push (level, quantity) to the predecessor stack
            current_quantity = float(row["Quantity"]) if has_non_empty_value(row["Quantity"]) else 0
            node_stack_predecessor_qty.append((level, current_quantity))

        # Handle the last component if any
        if current_component_row is not None and current_nodes and current_edges:
            components.append(_create_component_from_nodes_and_edges(
                current_component_row,
                current_nodes,
                current_edges
            ))

    valid_components = [component for component in components if len(component.edges)]
    return ProcessingResult(components=valid_components, warnings=warnings)

def _set_component_metadata(nodes: List[Node], component_name: str, component_id: str):
    for node in nodes:
        node.description = component_name
        node.component_name = component_id

async def _extract_manufacturing_nodes_and_edges(row: pd.Series, current_component_id: str, warnings: List[str]) -> Tuple[List[Node], List[Edge]]:
    """
    Extract manufacturing nodes and edges from a row.
    Returns a tuple of (nodes, edges) where nodes are manufacturing process nodes
    and edges connect them in sequence.
    """
    nodes = []
    edges = []

    scrap_rate = None
    scrap_fate = None

    row_num = get_row_num(row)
    part_name = row['Part Name']

    if _is_not_empty(row, "Scrap Rate"):
        try:
            if row["Scrap Rate"][-1] == "%":
                scrap_rate = row["Scrap Rate"].split(" - ")[-1][:-1]
            else:
                scrap_rate = row["Scrap Rate"].split(" - ")[-1]
            filtered_rate = "".join(c for c in scrap_rate if c in set("0123456789."))
            if filtered_rate:  # Only try to convert if we have numbers
                scrap_rate = float(filtered_rate)
            else:
                scrap_rate = None
                warning_msg = f"Invalid scrap rate format at row {row_num} with part name {part_name} for component {current_component_id}"
                warn_and_collect(warning_msg, warnings)
        except Exception as e:
            scrap_rate = None
            warning_msg = f"Error parsing scrap rate at row {row_num} with part name {part_name} for component {current_component_id} - {str(e)}"
            warn_and_collect(warning_msg, warnings)

    if _is_not_empty(row, "Scrap Fate"):
        scrap_fate = row["Scrap Fate"]

    # Create manufacturing process nodes with IDs
    process_node_ids = []
    seen_processes = set()
    is_first_node = True

    location = await get_location(row, location_column_names=["Plant"])
    if(location is None or not location.country):
        warning_msg = f"Location is None for manufacturing process at row {row_num} with part name {part_name} for component {current_component_id}"
        warn_and_collect(warning_msg, warnings)

    for process_col in ["MANUFACTURING PROCESS", "MANUFACTURING PROCESS 2", "MANUFACTURING PROCESS 3"]:
        if _is_not_empty(row, process_col) and row[process_col] not in seen_processes:
            seen_processes.add(row[process_col])
            process_id = get_next_node_id()
            process_node_ids.append(process_id)
            nodes.append(Node(
                name=_get_manufacturing_name_with_context(str(row[process_col])),
                scrap_rate=scrap_rate if is_first_node else 0.0,
                scrap_fate=scrap_fate if is_first_node else None,
                node_type=NodeTypes.PRODUCTION.value,
                location=location,
                node_id=process_id,
                row_num=row_num  # Add row_num to manufacturing node
            ))
            is_first_node = False

    # Create edges between nodes using node IDs
    for i, node in enumerate(nodes):
        if i == 0:
            continue
        edges.append(Edge(
            from_node_id=nodes[i - 1].node_id,
            to_node_id=node.node_id
        ))

    return nodes, edges

def _get_manufacturing_name_with_context(name: str) -> str:
    """
    Get the manufacturing name with furniture context if it's an assembly
    """
    if name.lower() in ("assembled", "assembly"):
        return f"{name} (Furniture)"
    return name

def _is_valid_component(component_name: str, nodes: List[Node], warnings: List[str]) -> bool:
    """
    Validate if a component has the required nodes and structure.
    Currently checks:
        - Has at least one material node
    """
    # Check if there is at least one material node
    material_nodes = [node for node in nodes if node.node_type == NodeTypes.MATERIAL.value]
    if not material_nodes:
        # Get row numbers from nodes if available
        rows = sorted([node.row_num for node in nodes if node.row_num is not None])
        row_info = f" (defined at row{'s' if len(rows) > 1 else ''} {rows})" if rows else ""

        warning_msg = f"Component {component_name}{row_info} is invalid: No material nodes found"
        warn_and_collect(warning_msg, warnings)
        return False

    return True
