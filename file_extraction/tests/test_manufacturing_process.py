import unittest
import pandas as pd
from file_extraction.beta import _extract_manufacturing_nodes_and_edges, Location, NodeTypes

class TestManufacturingProcess(unittest.IsolatedAsyncioTestCase):
    async def test_scrap_rate_assignment(self):
        """Test that scrap rate is applied correctly to manufacturing processes."""
        # Create a test row with multiple manufacturing processes and a scrap rate
        row = pd.Series({
            "MANUFACTURING PROCESS": "Process 1",
            "MANUFACTURING PROCESS 2": "Process 2",
            "MANUFACTURING PROCESS 3": "Process 3",
            "Scrap Rate": "5.5%",
            "Scrap Fate": "Recycled",
            "Plant": "Test Plant",
            "Part Name": "Test Part"
        }, name=42)  # Set index to 42

        # Extract nodes and edges
        warnings = []
        nodes, edges = await _extract_manufacturing_nodes_and_edges(row, "TEST-COMP-001", warnings)

        # Verify we got three nodes
        self.assertEqual(len(nodes), 3)

        # First node should have the original scrap rate
        self.assertEqual(nodes[0].name, "Process 1")
        self.assertEqual(nodes[0].scrap_rate, 5.5)
        self.assertEqual(nodes[0].scrap_fate, "Recycled")

        # Subsequent nodes should have 0.0 scrap rate and None scrap fate
        self.assertEqual(nodes[1].name, "Process 2")
        self.assertEqual(nodes[1].scrap_rate, 0.0)
        self.assertIsNone(nodes[1].scrap_fate)

        self.assertEqual(nodes[2].name, "Process 3")
        self.assertEqual(nodes[2].scrap_rate, 0.0)
        self.assertIsNone(nodes[2].scrap_fate)

        # Verify all nodes are production type
        for node in nodes:
            self.assertEqual(node.node_type, NodeTypes.PRODUCTION.value)

    async def test_single_process_scrap_rate(self):
        """Test scrap rate assignment with a single manufacturing process."""
        row = pd.Series({
            "MANUFACTURING PROCESS": "Single Process",
            "MANUFACTURING PROCESS 2": None,
            "MANUFACTURING PROCESS 3": None,
            "Scrap Rate": "10%",
            "Scrap Fate": "Reused",
            "Plant": "Test Plant",
            "Part Name": "Single Part"
        }, name=1)  # Set index to 1

        warnings = []
        nodes, _ = await _extract_manufacturing_nodes_and_edges(row, "TEST-COMP-002", warnings)

        self.assertEqual(len(nodes), 1)
        self.assertEqual(nodes[0].scrap_rate, 10.0)
        self.assertEqual(nodes[0].scrap_fate, "Reused")

    async def test_no_scrap_rate(self):
        """Test behavior when no scrap rate is provided."""
        row = pd.Series({
            "MANUFACTURING PROCESS": "Process 1",
            "MANUFACTURING PROCESS 2": "Process 2",
            "MANUFACTURING PROCESS 3": None,
            "Scrap Rate": None,
            "Scrap Fate": None,
            "Plant": "Test Plant",
            "Part Name": "No Scrap Part"
        }, name=2)  # Set index to 2

        warnings = []
        nodes, _ = await _extract_manufacturing_nodes_and_edges(row, "TEST-COMP-003", warnings)

        self.assertEqual(len(nodes), 2)
        self.assertIsNone(nodes[0].scrap_rate)
        self.assertEqual(nodes[1].scrap_rate, 0.0)

    async def test_duplicate_process_names(self):
        """Test that duplicate process names are handled correctly."""
        row = pd.Series({
            "MANUFACTURING PROCESS": "Process A",
            "MANUFACTURING PROCESS 2": "Process A",  # Same as first process
            "MANUFACTURING PROCESS 3": "Process B",
            "Scrap Rate": "7%",
            "Scrap Fate": "Recycled",
            "Plant": "Test Plant",
            "Part Name": "Duplicate Part"
        }, name=3)  # Set index to 3

        warnings = []
        nodes, _ = await _extract_manufacturing_nodes_and_edges(row, "TEST-COMP-004", warnings)

        # Should only have two nodes since Process A was duplicate
        self.assertEqual(len(nodes), 2)
        self.assertEqual(nodes[0].name, "Process A")
        self.assertEqual(nodes[0].scrap_rate, 7.0)
        self.assertEqual(nodes[1].name, "Process B")
        self.assertEqual(nodes[1].scrap_rate, 0.0)

    async def test_error_handling_with_invalid_data(self):
        """Test error handling when invalid data is provided."""
        row = pd.Series({
            "MANUFACTURING PROCESS": "Process 1",
            "MANUFACTURING PROCESS 2": None,
            "MANUFACTURING PROCESS 3": None,
            "Scrap Rate": "invalid%",  # Invalid scrap rate
            "Scrap Fate": None,  # Include Scrap Fate column
            "Plant": "Test Plant",
            "Part Name": "Error Part"
        }, name=4)  # Set index to 4

        warnings = []
        # This should log a warning but not raise an exception
        nodes, _ = await _extract_manufacturing_nodes_and_edges(row, "TEST-COMP-005", warnings)
        self.assertEqual(len(nodes), 1)  # Should still create the node
        self.assertIsNone(nodes[0].scrap_rate)  # Scrap rate should be None due to invalid format
        # Verify that a warning was collected
        self.assertTrue(any("Invalid scrap rate format at row 6 with part name Error Part for component TEST-COMP-005" in warning for warning in warnings))