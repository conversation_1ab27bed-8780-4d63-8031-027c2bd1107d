import os
import unittest
from fastapi.testclient import TestClient
from file_extraction.api import model_api

client = TestClient(model_api)

class TestComponentExtraction(unittest.TestCase):
    current_dir = os.path.dirname(os.path.abspath(__file__))
    file_path = os.path.join(current_dir, "validation_files", "test_component_file.csv")
    packaging_file_path = os.path.join(current_dir, "validation_files", "test_packaging_file.csv")

    expected_components = [
        {
            "component_name": "Table Top",
            "component_id": "1000123456AB12 (Shanghai Metal Corp)",
            "nodes": [
                {
                    "name": "Metal (Aluminum)",
                    "node_type": "material",
                    "amount": 2761.03,
                    "unit": "g",
                    "component_name": "1000123456AB12",
                    "description": "Table Top",
                    "quantity": 1,
                    "location": {
                        "city": None,
                        "country": None
                    },
                    "node_id": 1
                },
                {
                    "name": "Metal (Aluminum) transportation",
                    "node_type": "transportation",
                    "location": {
                        "city": "Timbuktu",
                        "country": "Mali"
                    },
                    "node_id": 2
                },
                {
                    "name": "Metal Working",
                    "scrap_rate": 0.0,
                    "scrap_fate": None,
                    "node_type": "production",
                    "location": {
                        "city": "Shanghai",
                        "country": "China"
                    },
                    "node_id": 3
                },
                {
                    "name": "Painting",
                    "scrap_rate": 0.0,
                    "scrap_fate": None,
                    "node_type": "production",
                    "location": {
                        "city": "Shanghai",
                        "country": "China"
                    },
                    "node_id": 4
                },
                {
                    "name": "Table Top bundle",
                    "node_type": "bundle",
                    "location": {
                        "city": "Shanghai",
                        "country": "China"
                    },
                    "node_id": 5
                }
            ],
            "edges": [
                {
                    "from_node_id": 1,
                    "to_node_id": 2
                },
                {
                    "from_node_id": 2,
                    "to_node_id": 3
                },
                {
                    "from_node_id": 3,
                    "to_node_id": 4
                },
                {
                    "from_node_id": 4,
                    "to_node_id": 5
                }
            ]
        },
        {
            "component_name": "Bottom Board",
            "component_id": "3812345678XY99 (LA Wood Suppliers)",
            "nodes": [
                {
                    "name": "Plywood (Thin Plywood) bundle",
                    "node_type": "bundle",
                    "location": {
                        "city": None,
                        "country": "Malaysia"
                    },
                    "node_id": 1
                },
                {
                    "name": "Plywood (Thin Plywood) (recycled)",
                    "node_type": "material",
                    "amount": 857.0665999999999,
                    "unit": "g",
                    "component_name": "3812345678XY99",
                    "description": "Bottom Board",
                    "quantity": 1,
                    "location": {
                        "city": None,
                        "country": "Malaysia"
                    },
                    "node_id": 2
                },
                {
                    "name": "Plywood (Thin Plywood) (non-recycled)",
                    "node_type": "material",
                    "amount": 461.4973999999999,
                    "unit": "g",
                    "component_name": "3812345678XY99",
                    "description": "Bottom Board",
                    "quantity": 1,
                    "location": {
                        "city": None,
                        "country": "Malaysia"
                    },
                    "node_id": 3
                },
                {
                    "name": "Plywood (Thin Plywood) transportation",
                    "node_type": "transportation",
                    "location": {
                        "city": "臺南市",
                        "country": "Taiwan"
                    },
                    "node_id": 4
                },
                {
                    "name": "Sawing",
                    "scrap_rate": 0.66,
                    "scrap_fate": "Recycled",
                    "node_type": "production",
                    "location": {
                        "city": "臺南市",
                        "country": "Taiwan"
                    },
                    "node_id": 5
                },
                {
                    "name": "Cutting",
                    "scrap_rate": 0.66,
                    "scrap_fate": "Recycled",
                    "node_type": "production",
                    "location": {
                        "city": "Los Angeles",
                        "country": "United States"
                    },
                    "node_id": 5
                },
                {
                    "name": "Bottom Board bundle",
                    "node_type": "bundle",
                    "location": {
                        "city": "Los Angeles",
                        "country": "United States"
                    },
                    "node_id": 6
                }
            ],
            "edges": [
                {
                    "from_node_id": 2,
                    "to_node_id": 1
                },
                {
                    "from_node_id": 3,
                    "to_node_id": 1
                },
                {
                    "from_node_id": 1,
                    "to_node_id": 4
                },
                {
                    "from_node_id": 4,
                    "to_node_id": 5
                },
                {
                    "from_node_id": 5,
                    "to_node_id": 6
                }
            ]
        },
        {
            "component_name": "Side Bar Left",
            "component_id": "5123456789",
            "nodes": [
                {
                    "name": "Wood (Oak Wood)",
                    "node_type": "material",
                    "amount": 63.94,
                    "unit": "g",
                    "component_name": "3802150002BL03",
                    "description": "Side Bar Left",
                    "quantity": 1,
                    "location": {
                        "city": "Henderson",
                        "country": "United States"
                    },
                    "node_id": 1
                },
                {
                    "name": "Wood (Oak Wood) transportation",
                    "node_type": "transportation",
                    "location": {
                        "city": "Henderson",
                        "country": "United States"
                    },
                    "node_id": 2
                },
                {
                    "name": "Cutting",
                    "scrap_rate": 4.0,
                    "scrap_fate": "Fed back into system",
                    "node_type": "production",
                    "location": {
                        "city": "Los Angeles",
                        "country": "United States"
                    },
                    "node_id": 3
                },
                {
                    "name": "Side Bar Left bundle",
                    "node_type": "bundle",
                    "location": {
                        "city": "Los Angeles",
                        "country": "United States"
                    },
                    "node_id": 4
                }
            ],
            "edges": [
                {
                    "from_node_id": 1,
                    "to_node_id": 2
                },
                {
                    "from_node_id": 2,
                    "to_node_id": 3
                },
                {
                    "from_node_id": 3,
                    "to_node_id": 4
                }
            ]
        },
        {
            "component_name": "Side Bar Right",
            "component_id": "2098765432",
            "nodes": [
                {
                    "name": "Wood (Oak Wood)",
                    "node_type": "material",
                    "amount": 27.74,
                    "unit": "g",
                    "component_name": "2098765432",
                    "description": "Side Bar Right",
                    "quantity": 1,
                    "location": {
                        "city": None,
                        "country": None
                    },
                    "node_id": 1
                },
                {
                    "name": "Wood (Oak Wood) transportation",
                    "node_type": "transportation",
                    "location": {
                        "city": "Henderson",
                        "country": "United States"
                    },
                    "node_id": 2
                },
                {
                    "name": "Cutting",
                    "scrap_rate": 5.0,
                    "scrap_fate": "Fed back into system",
                    "node_type": "production",
                    "location": {
                        "city": "Los Angeles",
                        "country": "United States"
                    },
                    "node_id": 3
                },
                {
                    "name": "Side Bar Right bundle",
                    "node_type": "bundle",
                    "location": {
                        "city": "Los Angeles",
                        "country": "United States"
                    },
                    "node_id": 4
                }
            ],
            "edges": [
                {
                    "from_node_id": 1,
                    "to_node_id": 2
                },
                {
                    "from_node_id": 2,
                    "to_node_id": 3
                },
                {
                    "from_node_id": 3,
                    "to_node_id": 4
                }
            ]
        },
        {
            "component_name": "Side Bar Top",
            "component_id": "96602Q4581",
            "nodes": [
                {
                    "name": "Wood (Oak Wood) (recycled)",
                    "node_type": "material",
                    "amount": 9.07,
                    "unit": "g",
                    "component_name": "96602Q4581",
                    "description": "Side Bar Top",
                    "quantity": 2,
                    "location": {
                        "city": None,
                        "country": None
                    },
                    "node_id": 1
                },
                {
                    "name": "Wood (Oak Wood) transportation",
                    "node_type": "transportation",
                    "location": {
                        "city": "Henderson",
                        "country": "United States"
                    },
                    "node_id": 2
                },
                {
                    "name": "Cutting",
                    "scrap_rate": 5.0,
                    "scrap_fate": "Fed back into system",
                    "node_type": "production",
                    "location": {
                        "city": "Los Angeles",
                        "country": "United States"
                    },
                    "node_id": 3
                },
                {
                    "name": "Side Bar Top bundle",
                    "node_type": "bundle",
                    "location": {
                        "city": "Los Angeles",
                        "country": "United States"
                    },
                    "node_id": 4
                }
            ],
            "edges": [
                {"from_node_id": 1, "to_node_id": 2},
                {"from_node_id": 2, "to_node_id": 3},
                {"from_node_id": 3, "to_node_id": 4}
            ]
        },
        {
            "component_name": "Oak Wood",
            "component_id": "96612Q123AB34",
            "nodes": [
                {
                    "name": "Plywood (Thin Plywood) bundle",
                    "location": {
                        "city": None,
                        "country": "Malaysia"
                    },
                    "node_type": "bundle",
                    "node_id": 1
                },
                {
                    "name": "Plywood (Thin Plywood) (recycled)",
                    "location": {
                        "city": None,
                        "country": "Malaysia"
                    },
                    "node_type": "material",
                    "amount": 857.0665999999999,
                    "unit": "g",
                    "component_name": "96612Q123AB34",
                    "description": "Oak Wood",
                    "quantity": 1.0,
                    "node_id": 2
                },
                {
                    "name": "Plywood (Thin Plywood) (non-recycled)",
                    "location": {
                        "city": None,
                        "country": "Malaysia"
                    },
                    "node_type": "material",
                    "amount": 461.4973999999999,
                    "unit": "g",
                    "component_name": "96612Q123AB34",
                    "description": "Oak Wood",
                    "quantity": 1.0,
                    "node_id": 3
                },
                {
                    "name": "Plywood (Thin Plywood) transportation",
                    "node_type": "transportation",
                    "location": {
                        "city": "臺南市",
                        "country": "Taiwan"
                    },
                    "node_id": 4
                },
                {
                    "name": "Sawing",
                    "location": {
                        "city": "臺南市",
                        "country": "Taiwan"
                    },
                    "node_type": "production",
                    "scrap_rate": 0.1,
                    "scrap_fate": "Fed back into system",
                    "node_id": 6
                },
                {
                    "name": "Oak Wood bundle",
                    "location": {
                        "city": "臺南市",
                        "country": "Taiwan"
                    },
                    "node_type": "bundle",
                    "node_id": 7
                }
            ],
            "edges": [
                {
                    "from_node_id": 2,
                    "to_node_id": 1
                },
                {
                    "from_node_id": 3,
                    "to_node_id": 1
                },
                {
                    "from_node_id": 1,
                    "to_node_id": 4
                },
                {
                    "from_node_id": 4,
                    "to_node_id": 6
                },
                {
                    "from_node_id": 6,
                    "to_node_id": 7
                }
            ]
        },
        {
            "component_name": "Wood Lining",
            "component_id": "1000123456AB10",
            "nodes": [
                {
                    "name": "Wood (Oak Wood)",
                    "location": {
                        "city": "Henderson",
                        "country": "United States"
                    },
                    "node_type": "material",
                    "amount": 10.4,
                    "unit": "g",
                    "component_name": "1000123456AB10",
                    "description": "Wood Lining",
                    "quantity": 1.0,
                    "node_id": 1
                },
                {
                    "name": "Wood (Oak Wood) transportation",
                    "location": {
                        "city": "Los Angeles",
                        "country": "United States"
                    },
                    "node_type": "transportation",
                    "node_id": 2
                },
                {
                    "name": "Cutting",
                    "location": {
                        "city": "Los Angeles",
                        "country": "United States"
                    },
                    "node_type": "production",
                    "scrap_rate": 5.0,
                    "scrap_fate": "Fed back into system",
                    "node_id": 3
                },
                {
                    "name": "Wood Lining bundle",
                    "location": {
                        "city": "Los Angeles",
                        "country": "United States"
                    },
                    "node_type": "bundle",
                    "node_id": 4
                }
            ],
            "edges": [
                {
                    "from_node_id": 1,
                    "to_node_id": 2
                },
                {
                    "from_node_id": 2,
                    "to_node_id": 3
                },
                {
                    "from_node_id": 3,
                    "to_node_id": 4
                }
            ]
        },
        {
            "component_name": "Bolt",
            "component_id": "1000134567AB10",
            "nodes": [
                {
                    "name": "Metal (Steel) (recycled)",
                    "node_type": "material",
                    "amount": 204,
                    "unit": "g",
                    "component_name": "1000134567AB10",
                    "description": "Bolt",
                    "quantity": 1,
                    "location": {
                        "city": "Henderson",
                        "state": "Kentucky",
                        "country": "United States"
                    },
                    "node_id": 1
                },
                {
                    "name": "Metal (Steel) transportation",
                    "node_type": "transportation",
                    "mode": "Truck",
                    "location": {
                        "city": "Phoenix",
                        "state": "AZ",
                        "country": "United States"
                    },
                    "node_id": 2
                },
                {
                    "name": "Cutting",
                    "scrap_rate": 0.05,
                    "scrap_fate": "Fed back into system",
                    "node_type": "production",
                    "location": {
                        "city": "Phoenix",
                        "state": "AZ",
                        "country": "United States"
                    },
                    "node_id": 3
                },
                {
                    "name": "Bolt bundle",
                    "node_type": "bundle",
                    "location": {
                        "city": "Phoenix",
                        "state": "AZ",
                        "country": "United States"
                    },
                    "node_id": 4
                }
            ],
            "edges": [
                {
                    "from_node_id": 1,
                    "to_node_id": 2
                },
                {
                    "from_node_id": 2,
                    "to_node_id": 3
                },
                {
                    "from_node_id": 3,
                    "to_node_id": 4
                }
            ]
        },
    ]

    expected_packaging_components = [
        {
            "component_name": "Corrugate Box",
            "component_id": "1",
            "nodes": [
                {
                    "name": "cardboard (recycled)",
                    "location": {
                        "city": "Colombo",
                        "country": "Sri Lanka"
                    },
                    "node_type": "packaging",
                    "amount": 1625.325,
                    "unit": "g",
                    "quantity": 1.0,
                    "node_id": 1
                },
                {
                    "name": "cardboard (non-recycled)",
                    "location": {
                        "city": "Colombo",
                        "country": "Sri Lanka"
                    },
                    "node_type": "packaging",
                    "amount": 875.175,
                    "unit": "g",
                    "quantity": 1.0,
                    "node_id": 2
                },
                {
                    "name": "cardboard combined",
                    "location": {
                        "city": "Colombo",
                        "country": "Sri Lanka"
                    },
                    "node_type": "bundle",
                    "node_id": 3
                }
            ],
            "edges": [
                {
                    "from_node_id": 1,
                    "to_node_id": 3
                },
                {
                    "from_node_id": 2,
                    "to_node_id": 3
                }
            ]
        },
        {
            "component_name": "Corruage Box 2",
            "component_id": "2",
            "nodes": [
                {
                    "name": "cardboard (recycled)",
                    "location": {
                        "city": "Kathmandu",
                        "country": "Nepal"
                    },
                    "node_type": "packaging",
                    "amount": 1518.9375,
                    "unit": "g",
                    "quantity": 1.0,
                    "node_id": 1
                },
                {
                    "name": "cardboard (non-recycled)",
                    "location": {
                        "city": "Kathmandu",
                        "country": "Nepal"
                    },
                    "node_type": "packaging",
                    "amount": 506.3125,
                    "unit": "g",
                    "quantity": 1.0,
                    "node_id": 2
                },
                {
                    "name": "cardboard combined",
                    "location": {
                        "city": "Kathmandu",
                        "country": "Nepal"
                    },
                    "node_type": "bundle",
                    "node_id": 3
                }
            ],
            "edges": [
                {
                    "from_node_id": 1,
                    "to_node_id": 3
                },
                {
                    "from_node_id": 2,
                    "to_node_id": 3
                }
            ]
        },
        {
            "component_name": "Polybag",
            "component_id": "3",
            "nodes": [
                {
                    "name": "polybag (recycled)",
                    "location": {
                        "city": "Piscataway",
                        "country": "United States"
                    },
                    "node_type": "packaging",
                    "amount": 0.32,
                    "unit": "g",
                    "quantity": 1.0,
                    "node_id": 1
                },
                {
                    "name": "polybag (non-recycled)",
                    "location": {
                        "city": "Piscataway",
                        "country": "United States"
                    },
                    "node_type": "packaging",
                    "amount": 0.07999999999999999,
                    "unit": "g",
                    "quantity": 1.0,
                    "node_id": 2
                },
                {
                    "name": "polybag combined",
                    "location": {
                        "city": "Piscataway",
                        "country": "United States"
                    },
                    "node_type": "bundle",
                    "node_id": 3
                }
            ],
            "edges": [
                {
                    "from_node_id": 1,
                    "to_node_id": 3
                },
                {
                    "from_node_id": 2,
                    "to_node_id": 3
                }
            ]
        },
        {
            "component_name": "Tape",
            "component_id": "Tape",
            "nodes": [
                {
                    "name": "clear tape (recycled)",
                    "location": {
                        "city": "Shanghai",
                        "country": "China"
                    },
                    "node_type": "packaging",
                    "amount": 8.16,
                    "unit": "g",
                    "quantity": 1.0,
                    "node_id": 1
                },
                {
                    "name": "clear tape (non-recycled)",
                    "location": {
                        "city": "Shanghai",
                        "country": "China"
                    },
                    "node_type": "packaging",
                    "amount": 2.0399999999999996,
                    "unit": "g",
                    "quantity": 1.0,
                    "node_id": 2
                },
                {
                    "name": "clear tape combined",
                    "location": {
                        "city": "Shanghai",
                        "country": "China"
                    },
                    "node_type": "bundle",
                    "node_id": 3
                }
            ],
            "edges": [
                {
                    "from_node_id": 1,
                    "to_node_id": 3
                },
                {
                    "from_node_id": 2,
                    "to_node_id": 3
                }
            ]
        }
    ]

    def _validate_component_nodes(self, component, expected_component):
        """Helper method to validate nodes in a component against expected nodes.

        Args:
            component: The actual component being tested
            expected_component: The expected component to compare against
            is_packaging: Boolean indicating if this is a packaging component test
        """
        try:
            for node in component["nodes"]:
                # Find the expected node with the same name
                expected_node = next((en for en in expected_component["nodes"] if node["name"] == en["name"]), None)
                self.assertIsNotNone(expected_node, f"No expected node found with name: {node['name']} in component {component['component_name']}")

                # Check all fields match
                mismatches = []
                if node["node_type"] != expected_node["node_type"]:
                    mismatches.append(f"node_type: {node['node_type']} != {expected_node['node_type']}")
                if node.get("amount") != expected_node.get("amount"):
                    mismatches.append(f"amount: {node.get('amount')} != {expected_node.get('amount')}")
                if node.get("quantity") != expected_node.get("quantity"):
                    mismatches.append(f"quantity: {node.get('quantity')} != {expected_node.get('quantity')}")
                # Compare locations
                node_loc = node.get("location")
                expected_loc = expected_node.get("location")
                if node_loc is None and expected_loc is not None:
                    mismatches.append(f"location: None != {expected_loc}")
                elif expected_loc is None and node_loc is not None:
                    mismatches.append(f"location: {node_loc} != None")
                elif node_loc and expected_loc:
                    if node_loc.get("country") != expected_loc.get("country"):
                        mismatches.append(f"country: {node_loc.get('country')} != {expected_loc.get('country')}")
                    if node_loc.get("city") != expected_loc.get("city"):
                        mismatches.append(f"city: {node_loc.get('city')} != {expected_loc.get('city')}")
                if mismatches:
                    raise AssertionError(f"Node field mismatches for node '{node['name']}' in component '{component['component_name']}':\n" + "\n".join(mismatches))

                # Common validation for both types
                self.assertTrue(
                    node.get("component_name") == component["component_id"],
                    f"Component name mismatch: {node['component_name']} != {component['component_id']}"
                )
                self.assertTrue(
                    node.get("description") == component["component_name"],
                    f"Description mismatch: {node['description']} != {component['component_name']}"
                )
        except Exception as e:
            print(e)
            raise e

    def _validate_components_from_file(self, file_path, expected_components):
        """Helper method to validate components from a file against expected components.

        Args:
            file_path: Path to the input file
            expected_components: List of expected component dictionaries to compare against
        """
        with open(file_path, "rb") as file:
            response = client.post("/components", files={"file": file})

        self.assertEqual(response.status_code, 200)
        components = response.json()["components"]

        if file_path == self.file_path:  # Only check length for non-packaging components
            self.assertEqual(len(components), len(expected_components))

        for component in components:
            component_index = next(
                (
                    index
                    for index, c in enumerate(expected_components)
                    if c["component_name"] == component["component_name"]
                ),
                None
            )
            self.assertIsNotNone(component_index)
            self._validate_component_nodes(
                component,
                expected_components[component_index]
            )

            # Validate node-edge consistency for this component
            component_type = "packaging" if file_path == self.packaging_file_path else "regular"
            self._validate_single_component_node_edge_consistency(component, component_type)

    def _validate_single_component_node_edge_consistency(self, component, component_type):
        """Helper method to validate node-edge consistency for a single component.

        Args:
            component: The actual component being tested
            component_type: String indicating the type of component (packaging or regular)
        """
        component_name = component.get('component_name', 'Unknown')
        node_ids = {node['node_id'] for node in component['nodes']}
        edge_node_ids = set()

        # Check edge validity
        for edge in component['edges']:
            from_id = edge['from_node_id']
            to_id = edge['to_node_id']
            self.assertIn(from_id, node_ids,
                          f"Edge 'from_node_id' {from_id} not found in nodes for {component_type} component '{component_name}'")
            self.assertIn(to_id, node_ids,
                          f"Edge 'to_node_id' {to_id} not found in nodes for {component_type} component '{component_name}'")
            edge_node_ids.add(from_id)
            edge_node_ids.add(to_id)

        # Check node connectivity (only if more than one node)
        if len(component['nodes']) > 1:
            for node in component['nodes']:
                node_id = node['node_id']
                self.assertIn(node_id, edge_node_ids,
                              f"Node ID {node_id} (name: {node.get('name', 'N/A')}) not found in any edge for {component_type} component '{component_name}' which has multiple nodes.")

    def test_component_extraction(self):
        """Test extraction of regular components."""
        self._validate_components_from_file(
            self.file_path,
            self.expected_components
        )

    def test_packaging_component_extraction(self):
        """Test extraction of packaging components."""
        self._validate_components_from_file(
            self.packaging_file_path,
            self.expected_packaging_components
        )
