from typing import List
import json
import re
import itertools
import tempfile
import pandas as pd
from fastapi import FastAPI, UploadFile, File, HTTPException, Header
from pydantic import BaseModel
from unstructured.partition.auto import partition
from file_extraction.models import Component
from file_extraction.predictions import (
    get_bom_items_from_file,
    get_product_info_from_file,
    predict_file_section_relevance,
    predict_file_is_structured_table,
    BOMItems
)
from file_extraction.beta import (
    get_parts_from_file,
    get_components_from_file,
)
import logging

model_api = FastAPI()

def grouper(iterable, n):
    it = iter(iterable)
    return iter(lambda: list(itertools.islice(it, n)), [])

def get_bom_items_from_structured_table(
    filename: str,
    weight_regex: str,
    weight_column: str,
    raw_material_columns: List[str]
) -> BOMItems:
    df = pd.read_csv(filename)
    pattern = re.compile(weight_regex)
    df[weight_column] = df[weight_column].apply(lambda x: re.findall(pattern, str(x)))
    df[weight_column] = df[weight_column].apply(lambda x: x[0][-1] if len(x) else 0)

    raw_materials = []
    for _, row in df.iterrows():
        weight = row[weight_column]
        if weight:
            names = set()
            for column in raw_material_columns:
                val = row[column]
                if pd.notna(val) and val != "":
                    names.add(val)

            raw_material = ""
            for name in names:
                if len(raw_material) > 0:
                    raw_material += f"; {name}"
                else:
                    raw_material += str(name)

            raw_materials.append({
                "raw_material": raw_material,
                "weight": {
                    "amount": float(weight) / 1000, # assuming weight is in grams
                    "unit": "kg"
                }
            })

    return BOMItems(
        raw_materials=raw_materials,
        packaging_components=[],
    )

def get_product_output_from_file(filename: str, content_type: str):
    elements = partition(filename, content_type=content_type)

    reduced_elements = [
        {
            "type": el.to_dict()["type"],
            "text_as_html": el.to_dict()["metadata"].get("text_as_html", el.text),
            "page_number": el.to_dict()["metadata"].get("page_number", 1),
        }
        for el in elements
    ]
    text_ = "".join(el["text_as_html"] for el in reduced_elements)

    structured_file_context = predict_file_is_structured_table(text_)
    supported_structured_file_types = ["text/csv"]
    if structured_file_context.is_structured_table and content_type in supported_structured_file_types:
        bom = get_bom_items_from_structured_table(
            filename,
            structured_file_context.weight_column_regex,
            structured_file_context.weight_column,
            structured_file_context.raw_material_names_columns
        )
    else:
        relevant_elements = [
            el for el in grouper(reduced_elements, 20)
            if predict_file_section_relevance(json.dumps(el))
        ]

        text = " ".join(
            el["text_as_html"]
            for group in relevant_elements for el in group
        )
        bom = get_bom_items_from_file(text)

    product_info = get_product_info_from_file(json.dumps(reduced_elements))

    # Function to generate incremental node IDs
    def get_next_node_id():
        get_next_node_id.counter += 1
        return get_next_node_id.counter

    # Initialize the counter
    get_next_node_id.counter = 0

    # Generate product node ID
    product_node_id = get_next_node_id()

    nodes = [
        {
            "node_id": product_node_id,
            "node_type": "bundle",
            "quantity": 1,
            "name": product_info.product_name,
            "location": {
                "city": product_info.factory_location.city,
                "country": product_info.factory_location.country
            }
        }
    ]
    edges = []

    for raw_material in bom.raw_materials:
        node_name = raw_material.raw_material

        # Generate node IDs for material and transportation nodes
        material_node_id = get_next_node_id()
        transport_node_id = get_next_node_id()

        nodes.append(
            {
                "node_id": material_node_id,
                "node_type": "material",
                "quantity": 1,
                "name": node_name,
                "amount": raw_material.weight.amount / product_info.total_number_of_products,
                "unit": raw_material.weight.unit,
            }
        )
        nodes.append(
            {
                "node_id": transport_node_id,
                "node_type": "transportation",
                "quantity": 1,
                "name": f"{node_name} transportation",
                "location": {
                    "city": product_info.factory_location.city,
                    "country": product_info.factory_location.country
                }
            }
        )
        edges.append(
            {
                "from_node_id": material_node_id,
                "to_node_id": transport_node_id
            }
        )

        if raw_material.manufacturing_method:
            # Generate node ID for production node
            production_node_id = get_next_node_id()

            nodes.append(
                {
                    "node_id": production_node_id,
                    "node_type": "production",
                    "quantity": 1,
                    "name": f"{node_name} {raw_material.manufacturing_method}",
                    "location": {
                        "city": product_info.factory_location.city,
                        "country": product_info.factory_location.country
                    }
                }
            )
            edges.extend(
                [
                    {
                        "from_node_id": transport_node_id,
                        "to_node_id": production_node_id
                    },
                    {
                        "from_node_id": production_node_id,
                        "to_node_id": product_node_id
                    }
                ]
            )
        else:
            edges.append(
                {
                    "from_node_id": transport_node_id,
                    "to_node_id": product_node_id
                }
            )

    for component in bom.packaging_components:
        node_name = component.raw_material

        # Generate node IDs for packaging and transportation nodes
        packaging_node_id = get_next_node_id()
        transport_node_id = get_next_node_id()

        nodes.append(
            {
                "node_id": packaging_node_id,
                "node_type": "packaging",
                "quantity": 1,
                "name": node_name,
                "amount": component.weight.amount / product_info.total_number_of_products,
                "unit": component.weight.unit,
                "packaging_level": component.packaging_level,
                "component_name": component.component
            }
        )
        nodes.append(
            {
                "node_id": transport_node_id,
                "node_type": "transportation",
                "quantity": 1,
                "name": f"{node_name} transportation",
                "location": {
                    "city": product_info.factory_location.city,
                    "country": product_info.factory_location.country
                }
            }
        )
        edges.append(
            {
                "from_node_id": packaging_node_id,
                "to_node_id": transport_node_id
            }
        )
        if component.manufacturing_method:
            # Generate node ID for production node
            production_node_id = get_next_node_id()

            nodes.append(
                {
                    "node_id": production_node_id,
                    "node_type": "production",
                    "quantity": 1,
                    "name": f"{node_name} {component.manufacturing_method}",
                    "location": {
                        "city": product_info.factory_location.city,
                        "country": product_info.factory_location.country
                    }
                }
            )
            edges.extend(
                [
                    {
                        "from_node_id": transport_node_id,
                        "to_node_id": production_node_id
                    },
                    {
                        "from_node_id": production_node_id,
                        "to_node_id": product_node_id
                    }
                ]
            )
        else:
            edges.append(
                {
                    "from_node_id": transport_node_id,
                    "to_node_id": product_node_id
                }
            )

    product = {
        "product_name": product_info.product_name,
        "product_id": product_info.product_id,
        "annual_sales_volume": product_info.total_number_of_products,
        "factory": product_info.factory_location.model_dump(),
        "nodes": nodes,
        "edges": edges,
    }

    return product


@model_api.post("/")
async def pdf_extraction(
    file: UploadFile = File(...),
    x_parts_only: bool = Header(False),
):
    try:
        with tempfile.NamedTemporaryFile() as tmp:
            tmp.write(await file.read())
            tmp.flush()
            tmp_path = tmp.name

            if x_parts_only:
                return get_parts_from_file(
                    tmp_path,
                    file.content_type
                )

            product_output = get_product_output_from_file(
                tmp_path,
                file.content_type,
            )

        return {"product": product_output}

    except Exception as error:
        import traceback
        traceback.print_exc()
        raise HTTPException(
            500,
            detail=f"Error extracting product info from file: {error}"
        ) from error


class ComponentsResponse(BaseModel):
    components: List[Component]
    warnings: List[str]

@model_api.post("/components")
async def extract_components(
    file: UploadFile = File(...),
) -> ComponentsResponse:
    if file.content_type not in ["text/csv", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"]:
        raise HTTPException(400, detail="Unsupported file type")

    try:
        with tempfile.NamedTemporaryFile() as tmp:
            tmp.write(await file.read())
            tmp.flush()
            tmp_path = tmp.name

            result = await get_components_from_file(tmp_path)
            unique_components = list(set(component.component_id for component in result.components))
            components_ = []
            for component in result.components:
                if component.component_id in unique_components:
                    components_.append(component)
                    unique_components.remove(component.component_id)

        return ComponentsResponse(components=components_, warnings=result.warnings)
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=str(e)
        ) from e
