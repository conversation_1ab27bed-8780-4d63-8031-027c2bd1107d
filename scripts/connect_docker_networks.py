#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON>rip<PERSON> to connect the ML models container to the LCA network.
This script works on both Windows and Linux.

Usage:
    python scripts/connect_docker_networks.py

Purpose:
    This script ensures that the ML models container (ml-models-app) is connected
    to the LCA network (lca-network), which is necessary for the LCA service to
    communicate with the ML models service. This is required for features like
    product category prediction to work correctly.

    The script will:
    1. Check if <PERSON><PERSON> is running
    2. Check if the ML models container is running (and start it if needed)
    3. Check if the LCA network exists (and create it if needed)
    4. Connect the ML models container to the LCA network if not already connected

When to use:
    Run this script:
    - After starting Docker
    - After restarting your containers
    - If you encounter network-related errors in the LCA service logs
"""

import subprocess
import json
import sys
import time

def run_command(command):
    """Run a shell command and return the output."""
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"Error executing command: {command}")
        print(f"Error message: {e.stderr}")
        return None

def check_docker_running():
    """Check if Docker is running."""
    print("Checking if Docker is running...")
    result = run_command("docker info")
    if result is None:
        print("Docker is not running. Please start Docker and try again.")
        sys.exit(1)
    print("Docker is running.")
    return True

def check_container_running(container_name):
    """Check if a container is running."""
    print(f"Checking if container {container_name} is running...")
    result = run_command(f"docker ps -q -f name=^{container_name}$")
    if not result:
        print(f"Container {container_name} is not running. Please start it first.")
        return False
    print(f"Container {container_name} is running.")
    return True

def check_network_exists(network_name):
    """Check if a network exists."""
    print(f"Checking if network {network_name} exists...")
    result = run_command(f"docker network ls -q -f name=^{network_name}$")
    if not result:
        print(f"Network {network_name} does not exist. Please create it first.")
        return False
    print(f"Network {network_name} exists.")
    return True

def is_container_connected_to_network(container_name, network_name):
    """Check if a container is connected to a network."""
    print(f"Checking if container {container_name} is connected to network {network_name}...")
    result = run_command(f"docker network inspect {network_name}")
    if result is None:
        return False

    try:
        network_info = json.loads(result)
        containers = network_info[0].get("Containers", {})

        # Check if any container in the network has the specified name
        for container_id, container_info in containers.items():
            if container_info.get("Name") == container_name:
                print(f"Container {container_name} is already connected to network {network_name}.")
                return True

        print(f"Container {container_name} is not connected to network {network_name}.")
        return False
    except (json.JSONDecodeError, IndexError, KeyError) as e:
        print(f"Error parsing network information: {e}")
        return False

def connect_container_to_network(container_name, network_name):
    """Connect a container to a network."""
    print(f"Connecting container {container_name} to network {network_name}...")
    result = run_command(f"docker network connect {network_name} {container_name}")
    if result is None:
        print(f"Failed to connect {container_name} to {network_name}.")
        return False
    print(f"Successfully connected {container_name} to {network_name}.")
    return True

def main():
    """Main function."""
    # Define container and network names
    ml_container = "ml-models-app"
    lca_network = "product_lca_lca-network"  # Updated to match the actual network name

    # Check if Docker is running
    check_docker_running()

    # Check if ML container is running
    if not check_container_running(ml_container):
        print(f"Starting {ml_container}...")
        run_command(f"docker start {ml_container}")
        time.sleep(2)  # Give the container time to start
        if not check_container_running(ml_container):
            print(f"Failed to start {ml_container}. Please start it manually.")
            sys.exit(1)

    # Check if LCA network exists
    if not check_network_exists(lca_network):
        print(f"Creating {lca_network}...")
        run_command(f"docker network create {lca_network}")
        if not check_network_exists(lca_network):
            print(f"Failed to create {lca_network}. Please create it manually.")
            sys.exit(1)

    # Check if ML container is connected to LCA network
    if not is_container_connected_to_network(ml_container, lca_network):
        # Connect ML container to LCA network
        if not connect_container_to_network(ml_container, lca_network):
            print(f"Failed to connect {ml_container} to {lca_network}. Please connect it manually.")
            sys.exit(1)

    print("Network connection setup complete.")

if __name__ == "__main__":
    main()
