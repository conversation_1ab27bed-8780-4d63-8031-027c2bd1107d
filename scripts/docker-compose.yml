version: "3"

services:
  redis:
    image: redis:6
    container_name: ml-model-redis
    ports:
      - "6379:6379"
    volumes:
      - ./scripts/config/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - ml-models-network

  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ml-models-app
    ports:
      - "5001:5001"
    env_file:
      - .env
    environment:
      - REDIS_URL=redis://ml-model-redis:6379
    depends_on:
      - redis
    dns:
      - *******
      - *******
    networks:
      - ml-models-network

networks:
  ml-models-network:
    name: ml-models-network # This forces a specific name without the project prefix
    driver: bridge
