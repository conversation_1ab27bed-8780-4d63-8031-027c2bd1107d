#!/usr/bin/env python3
"""
Direct unit test for our fixes without needing the web server
Tests the ISIC parsing fix and geography source preservation
"""
import sys
sys.path.append('/home/<USER>/app')

import json
from unittest.mock import patch, MagicMock
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_isic_parsing_fix():
    """Test the Phase 1.3 ISIC parsing fix directly"""
    print("=== TESTING ISIC PARSING FIX ===\n")

    from emissions_factor_matching.predictions import map_isic_classification

    # Test the exact scenario that was failing
    print("1. Testing the exact failing scenario from logs:")
    print("   LLM Response: {'result': ['2013']}")

    try:
        # Mock the LLM to return the problematic response format
        with patch('emissions_factor_matching.predictions.get_chat_completion') as mock_completion:
            with patch('emissions_factor_matching.predictions.efs_with_geographies') as mock_df:
                # Set up the exact response that was failing
                mock_completion.return_value = '{"result": ["2013"]}'

                # Mock available ISIC codes in database
                mock_df.__getitem__.return_value.dropna.return_value.unique.return_value = ["2013", "2014", "2015"]

                # Call the function
                result = map_isic_classification(
                    enhanced_category="CHEMICAL_POLYMER_PLASTIC",
                    modifiers=["PET", "virgin"],
                    user_query="PET Virgin v2"
                )

                print(f"   Result: {result}")

                if result == ["2013"]:
                    print("   ✅ SUCCESS: ISIC parsing fix works!")
                    print("   ✅ Phase 1.3 no longer returns empty codes")
                else:
                    print(f"   ❌ FAILED: Expected ['2013'], got {result}")

    except Exception as e:
        print(f"   ❌ ERROR: {str(e)}")
        import traceback
        traceback.print_exc()

    # Test additional problematic cases
    print("\n2. Testing edge case - non-list value in object:")
    try:
        with patch('emissions_factor_matching.predictions.get_chat_completion') as mock_completion:
            with patch('emissions_factor_matching.predictions.efs_with_geographies') as mock_df:
                # Test case where the object contains a non-list value
                mock_completion.return_value = '{"result": "2013"}'  # String instead of list
                mock_df.__getitem__.return_value.dropna.return_value.unique.return_value = ["2013", "2014"]

                result = map_isic_classification(
                    enhanced_category="CHEMICAL_POLYMER_PLASTIC",
                    modifiers=["PET", "virgin"],
                    user_query="PET Virgin v2"
                )

                print(f"   Result: {result}")
                if result == []:
                    print("   ✅ SUCCESS: Correctly handled non-list value in object")
                else:
                    print(f"   ❌ FAILED: Should return empty list for non-list value")

    except Exception as e:
        print(f"   ❌ ERROR: {str(e)}")
        import traceback
        traceback.print_exc()

    print()

def test_geography_source_preservation():
    """Test the geography source preservation fix"""
    print("=== TESTING GEOGRAPHY SOURCE PRESERVATION ===\n")

    from emissions_factor_matching.geography import get_geography_activity_match
    from emissions_factor_matching.api import get_activity_from_dataset

    print("1. Testing get_geography_activity_match with preferred_source:")

    try:
        # Mock the database to have multiple sources for the same activity
        mock_data = [
            {
                'Activity Name': 'test activity',
                'Reference Product Name': 'test product',
                'Geography': 'GLO',
                'Source': 'Ecoinvent 3.11',
                'Activity UUID': 'uuid-1',
                'Product Information': 'test info'
            },
            {
                'Activity Name': 'test activity',
                'Reference Product Name': 'test product',
                'Geography': 'GLO',
                'Source': 'DEFRA',
                'Activity UUID': 'uuid-2',
                'Product Information': 'test info'
            }
        ]

        with patch('emissions_factor_matching.geography.efs_with_geographies') as mock_df:
            # Mock pandas operations
            mock_matches = MagicMock()
            mock_matches.empty = False

            # Mock the DEFRA match
            mock_defra_matches = MagicMock()
            mock_defra_matches.empty = False
            mock_defra_matches.iloc = [mock_data[1]]  # DEFRA entry

            # Mock the filtering operations
            mock_matches.__getitem__.return_value = mock_defra_matches
            mock_df.__getitem__.return_value.__and__.return_value.__and__.return_value.str.contains.return_value = mock_matches

            # Test without preferred source (should get first match)
            print("   Testing without preferred_source:")
            mock_matches.iloc = [mock_data[0]]  # Ecoinvent first
            result1 = get_geography_activity_match(
                activity_name="test activity",
                iso_code="GLO",
                reference_product="test product"
            )
            print(f"   Result: {result1}")

            # Test with preferred source DEFRA
            print("   Testing with preferred_source='DEFRA':")
            result2 = get_geography_activity_match(
                activity_name="test activity",
                iso_code="GLO",
                reference_product="test product",
                preferred_source="DEFRA"
            )
            print(f"   Result: {result2}")
            print("   ✅ SUCCESS: Geography matching accepts preferred_source parameter")

    except Exception as e:
        print(f"   ❌ ERROR: {str(e)}")
        import traceback
        traceback.print_exc()

    print()

def test_dynamic_filters():
    """Test that carbon_only=True adds DEFRA filter"""
    print("=== TESTING DYNAMIC FILTERS ===\n")

    from emissions_factor_matching.predictions import construct_dynamic_filters
    from pydantic import BaseModel

    class TestRequest(BaseModel):
        user_query_primary: str
        carbon_only: bool = False
        lcia_method: str = None

    print("1. Testing carbon_only=True adds DEFRA filter:")

    try:
        # Create request with carbon_only=True
        request = TestRequest(
            user_query_primary="test query",
            carbon_only=True
        )

        # Call construct_dynamic_filters
        filters = construct_dynamic_filters(
            request_model=request,
            enhanced_category="CHEMICAL_POLYMER_PLASTIC",
            modifiers=["PET", "virgin"],
            isic_codes=["2013"]
        )

        print(f"   Generated filters: {filters}")

        # Check if DEFRA filter is present
        filter_str = str(filters)
        if "DEFRA" in filter_str:
            print("   ✅ SUCCESS: DEFRA filter added for carbon_only=True")
        else:
            print("   ❌ FAILED: DEFRA filter not found in filters")

    except Exception as e:
        print(f"   ❌ ERROR: {str(e)}")
        import traceback
        traceback.print_exc()

    print()

def test_integration_scenario():
    """Test a simplified version of the full pipeline"""
    print("=== TESTING INTEGRATION SCENARIO ===\n")

    print("Testing simplified pipeline with our fixes:")

    try:
        from emissions_factor_matching.predictions import (
            map_isic_classification,
            construct_dynamic_filters
        )
        from pydantic import BaseModel

        class TestRequest(BaseModel):
            user_query_primary: str = "PET Virgin v2"
            carbon_only: bool = True

        request = TestRequest()

        # Step 1: Test ISIC mapping with object response
        print("1. Phase 1.3 - ISIC Classification:")
        with patch('emissions_factor_matching.predictions.get_chat_completion') as mock_completion:
            with patch('emissions_factor_matching.predictions.efs_with_geographies') as mock_df:
                mock_completion.return_value = '{"result": ["2013"]}'
                mock_df.__getitem__.return_value.dropna.return_value.unique.return_value = ["2013", "2014"]

                isic_codes = map_isic_classification(
                    enhanced_category="CHEMICAL_POLYMER_PLASTIC",
                    modifiers=["PET", "virgin"],
                    user_query="PET Virgin v2"
                )

                print(f"   ISIC codes: {isic_codes}")

                if isic_codes == ["2013"]:
                    print("   ✅ Phase 1.3 working correctly")
                else:
                    print("   ❌ Phase 1.3 failed")
                    return

        # Step 2: Test dynamic filters
        print("2. Phase 1.5 - Dynamic Filters:")
        filters = construct_dynamic_filters(
            request_model=request,
            enhanced_category="CHEMICAL_POLYMER_PLASTIC",
            modifiers=["PET", "virgin"],
            isic_codes=isic_codes
        )

        print(f"   Filters: {filters}")

        if "DEFRA" in str(filters):
            print("   ✅ Phase 1.5 adds DEFRA filter for carbon_only=True")
        else:
            print("   ❌ Phase 1.5 missing DEFRA filter")

        print("\n✅ Integration test completed successfully!")
        print("✅ Both fixes appear to be working together")

    except Exception as e:
        print(f"   ❌ ERROR: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Testing our fixes directly...\n")

    test_isic_parsing_fix()
    test_geography_source_preservation()
    test_dynamic_filters()
    test_integration_scenario()

    print("\n=== SUMMARY ===")
    print("If all tests passed:")
    print("✅ ISIC parsing fix handles object format responses")
    print("✅ Geography matching preserves preferred source")
    print("✅ Dynamic filters add DEFRA filter for carbon_only=True")
    print("✅ The fixes should resolve the test_filter_by_lcia_method failure")
